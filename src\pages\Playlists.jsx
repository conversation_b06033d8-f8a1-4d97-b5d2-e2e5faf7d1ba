import { useState, useEffect } from 'react';
import { Container, Row, Col, Button, Alert, InputGroup, Form } from 'react-bootstrap';
import { FaPlus, FaMusic, FaSearch, FaHeadphones } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { usePlaylist } from '../context/PlaylistContext';
import { useAuth } from '../context/AuthContext';
import PlaylistCard from '../components/playlists/PlaylistCard';
import CreatePlaylistModal from '../components/playlists/CreatePlaylistModal';
import AuthModals from '../components/auth/AuthModals';
import LoadingSpinner from '../components/common/LoadingSpinner';

const Playlists = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredPlaylists, setFilteredPlaylists] = useState([]);

  const { playlists, loading, error } = usePlaylist();
  const { currentUser } = useAuth();

  // Filter playlists based on search term
  useEffect(() => {
    if (playlists.length > 0 && searchTerm.trim() !== '') {
      const filtered = playlists.filter(playlist =>
        playlist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (playlist.description && playlist.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredPlaylists(filtered);
    } else {
      setFilteredPlaylists(playlists);
    }
  }, [playlists, searchTerm]);

  const handleCreateClick = () => {
    if (currentUser) {
      setShowCreateModal(true);
    } else {
      setShowAuthModal(true);
    }
  };

  return (
    <div className="playlists-page">
      {/* Hero Section with Gradient Background - Smaller Version */}
      <div
        className="hero-section text-center py-4 mb-4 mx-auto playlist-header"
        style={{
          background: 'linear-gradient(135deg, rgba(15, 15, 15, 0.9) 0%, rgba(0, 255, 209, 0.1) 100%)',
          borderRadius: '1rem',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
          border: '1px solid rgba(255, 255, 255, 0.05)',
          padding: '2rem 1rem',
          maxWidth: '900px'
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="d-flex justify-content-center mb-2">
            <div
              className="icon-container d-flex align-items-center justify-content-center"
              style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                background: 'rgba(0, 255, 209, 0.1)',
                boxShadow: '0 0 20px rgba(0, 255, 209, 0.2)',
                border: '1px solid rgba(0, 255, 209, 0.3)'
              }}
            >
              <FaHeadphones size={28} style={{ color: 'var(--dark-accent)' }} />
            </div>
          </div>
          <h2 className="fw-bold mb-2" style={{ color: 'var(--dark-text)' }}>Your Music Collections</h2>
          <p className="text-muted mb-3 mx-auto" style={{ maxWidth: '500px', fontSize: '0.95rem' }}>
            Create personalized playlists and organize your favorite tracks in one place.
          </p>

          {currentUser ? (
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="primary"
                onClick={handleCreateClick}
                className="d-flex align-items-center mx-auto"
                style={{
                  backgroundColor: 'var(--dark-accent)',
                  color: '#2c3e50',
                  border: 'none',
                  borderRadius: '50rem',
                  padding: '0.5rem 1.5rem',
                  boxShadow: '0 4px 15px rgba(0, 255, 209, 0.3)',
                  fontSize: '0.95rem',
                  fontWeight: '600'
                }}
              >
                <FaPlus className="me-2" /> Create New Playlist
              </Button>
            </motion.div>
          ) : (
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="primary"
                onClick={() => setShowAuthModal(true)}
                style={{
                  backgroundColor: 'var(--dark-accent)',
                  color: '#2c3e50',
                  border: 'none',
                  borderRadius: '50rem',
                  padding: '0.5rem 1.5rem',
                  boxShadow: '0 4px 15px rgba(0, 255, 209, 0.3)',
                  fontSize: '0.95rem',
                  fontWeight: '600'
                }}
              >
                Sign In to Create Playlists
              </Button>
            </motion.div>
          )}
        </motion.div>
      </div>

      <Container>
        {error && <Alert variant="danger">{error}</Alert>}

        {loading ? (
          <div className="text-center py-5">
            <LoadingSpinner />
          </div>
        ) : !currentUser ? (
          <Row className="mb-4 g-4 justify-content-center">
            <Col md={6} lg={5}>
              <div
                className="text-center py-4 h-100"
                style={{
                  backgroundColor: 'rgba(15, 15, 15, 0.5)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '1rem',
                  boxShadow: '0 10px 20px rgba(0, 0, 0, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.05)',
                  padding: '1.5rem'
                }}
              >
                <div className="mb-3 text-center">
                  <div
                    className="d-inline-flex align-items-center justify-content-center rounded-circle mb-2"
                    style={{
                      width: '50px',
                      height: '50px',
                      backgroundColor: 'rgba(0, 255, 209, 0.1)',
                      boxShadow: '0 0 15px rgba(0, 255, 209, 0.2)',
                      border: '1px solid rgba(0, 255, 209, 0.2)'
                    }}
                  >
                    <FaMusic size={20} style={{ color: 'var(--dark-accent)' }} />
                  </div>
                </div>
                <h5 className="mb-2">View Your Playlists</h5>
                <p className="text-muted mb-3 small" style={{ margin: '0 auto' }}>
                  Sign in to access your saved playlists
                </p>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => setShowAuthModal(true)}
                    style={{
                      backgroundColor: 'var(--dark-accent)',
                      color: '#2c3e50',
                      border: 'none',
                      borderRadius: '50rem',
                      padding: '0.4rem 1.2rem',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}
                  >
                    Sign In
                  </Button>
                </motion.div>
              </div>
            </Col>

            <Col md={6} lg={5}>
              <div
                className="text-center py-4 h-100"
                style={{
                  backgroundColor: 'rgba(15, 15, 15, 0.5)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '1rem',
                  boxShadow: '0 10px 20px rgba(0, 0, 0, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.05)',
                  padding: '1.5rem'
                }}
              >
                <div className="mb-3 text-center">
                  <div
                    className="d-inline-flex align-items-center justify-content-center rounded-circle mb-2"
                    style={{
                      width: '50px',
                      height: '50px',
                      backgroundColor: 'rgba(0, 255, 209, 0.1)',
                      boxShadow: '0 0 15px rgba(0, 255, 209, 0.2)',
                      border: '1px solid rgba(0, 255, 209, 0.2)'
                    }}
                  >
                    <FaPlus size={20} style={{ color: 'var(--dark-accent)' }} />
                  </div>
                </div>
                <h5 className="mb-2">Create New Playlists</h5>
                <p className="text-muted mb-3 small" style={{ margin: '0 auto' }}>
                  Sign in to create and manage playlists
                </p>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline-light"
                    size="sm"
                    onClick={() => setShowAuthModal(true)}
                    style={{
                      borderRadius: '50rem',
                      padding: '0.4rem 1.2rem',
                      fontSize: '0.9rem'
                    }}
                  >
                    Get Started
                  </Button>
                </motion.div>
              </div>
            </Col>
          </Row>
        ) : playlists.length > 0 ? (
          <>
            {/* Search and filter section */}
            <div className="mb-4 playlist-search">
              <div className="search-container mb-4">
                <div className="input-group custom-search-bar">
                  <span className="input-group-text" id="search-addon">
                    <FaSearch style={{ color: '#ffffff' }} />
                  </span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search your playlists..."
                    aria-label="Search playlists"
                    aria-describedby="search-addon"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <h3 className="mb-4 text-center">
                {searchTerm ? 'Search Results' : 'Your Playlists'}
                <span className="ms-2 badge rounded-pill" style={{ backgroundColor: 'var(--dark-accent)', color: '#0f0f0f' }}>
                  {filteredPlaylists.length}
                </span>
              </h3>
            </div>

            {/* Playlists grid */}
            <AnimatePresence>
              {filteredPlaylists.length > 0 ? (
                <Row xs={1} sm={2} md={3} lg={4} className="g-4">
                  {filteredPlaylists.map((playlist, index) => (
                    <Col key={playlist.id}>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                      >
                        <PlaylistCard playlist={playlist} />
                      </motion.div>
                    </Col>
                  ))}
                </Row>
              ) : (
                <div
                  className="text-center py-4 mx-auto"
                  style={{
                    maxWidth: '500px',
                    backgroundColor: 'rgba(15, 15, 15, 0.5)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: '1rem',
                    boxShadow: '0 10px 20px rgba(0, 0, 0, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.05)',
                    padding: '2rem'
                  }}
                >
                  <div className="mb-3 text-center">
                    <div
                      className="d-inline-flex align-items-center justify-content-center rounded-circle mb-2"
                      style={{
                        width: '60px',
                        height: '60px',
                        backgroundColor: 'rgba(0, 255, 209, 0.1)',
                        boxShadow: '0 0 15px rgba(0, 255, 209, 0.2)',
                        border: '1px solid rgba(0, 255, 209, 0.2)'
                      }}
                    >
                      <FaSearch size={24} style={{ color: 'var(--dark-accent)' }} />
                    </div>
                  </div>
                  <h5 className="mb-2">No playlists found</h5>
                  <p className="text-muted mb-3 small" style={{ maxWidth: '350px', margin: '0 auto' }}>
                    Try a different search term or create a new playlist.
                  </p>
                  <div className="d-flex justify-content-center gap-3">
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        onClick={() => setSearchTerm('')}
                        style={{
                          borderRadius: '50rem',
                          padding: '0.4rem 1.2rem',
                          fontSize: '0.9rem'
                        }}
                      >
                        Clear Search
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => setShowCreateModal(true)}
                        style={{
                          backgroundColor: 'var(--dark-accent)',
                          color: '#2c3e50',
                          border: 'none',
                          borderRadius: '50rem',
                          padding: '0.4rem 1.2rem',
                          fontSize: '0.9rem',
                          fontWeight: '600'
                        }}
                      >
                        Create Playlist
                      </Button>
                    </motion.div>
                  </div>
                </div>
              )}
            </AnimatePresence>
          </>
        ) : (
          <Row className="mb-4 justify-content-center">
            <Col md={8} lg={6}>
              <div
                className="text-center py-4"
                style={{
                  backgroundColor: 'rgba(15, 15, 15, 0.5)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '1rem',
                  boxShadow: '0 10px 20px rgba(0, 0, 0, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.05)',
                  padding: '1.5rem'
                }}
              >
                <div className="mb-3 text-center">
                  <div
                    className="d-inline-flex align-items-center justify-content-center rounded-circle mb-2"
                    style={{
                      width: '50px',
                      height: '50px',
                      backgroundColor: 'rgba(0, 255, 209, 0.1)',
                      boxShadow: '0 0 15px rgba(0, 255, 209, 0.2)',
                      border: '1px solid rgba(0, 255, 209, 0.2)'
                    }}
                  >
                    <FaPlus size={20} style={{ color: 'var(--dark-accent)' }} />
                  </div>
                </div>
                <h5 className="mb-2">No playlists yet</h5>
                <p className="text-muted mb-3 small" style={{ maxWidth: '350px', margin: '0 auto' }}>
                  Create your first playlist to start organizing your favorite songs.
                </p>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => setShowCreateModal(true)}
                    className="d-flex align-items-center mx-auto"
                    style={{
                      backgroundColor: 'var(--dark-accent)',
                      color: '#2c3e50',
                      border: 'none',
                      borderRadius: '50rem',
                      padding: '0.4rem 1.2rem',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}
                  >
                    <FaPlus className="me-2" /> Create Playlist
                  </Button>
                </motion.div>
              </div>
            </Col>
          </Row>
        )}

        <CreatePlaylistModal
          show={showCreateModal}
          onHide={() => setShowCreateModal(false)}
        />

        <AuthModals
          showLogin={showAuthModal}
          onHide={() => setShowAuthModal(false)}
        />
      </Container>
    </div>
  );
};

export default Playlists;
