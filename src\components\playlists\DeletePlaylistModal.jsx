import { useState } from 'react';
import { <PERSON><PERSON>, Button, Alert } from 'react-bootstrap';
import { usePlaylist } from '../../context/PlaylistContext';
import { motion } from 'framer-motion';

const DeletePlaylistModal = ({ show, onHide, playlist }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { deletePlaylist } = usePlaylist();

  const handleDelete = async () => {
    try {
      setError('');
      setLoading(true);
      await deletePlaylist(playlist.id);
      onHide();
    } catch (err) {
      setError('Failed to delete playlist: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal 
      show={show} 
      onHide={onHide}
      centered
      contentClassName="bg-dark text-light"
      backdropClassName="backdrop-blur"
    >
      <Modal.Header closeButton closeVariant="white" className="border-0">
        <Modal.Title>Delete Playlist</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}
        
        <p>Are you sure you want to delete the playlist "{playlist?.name}"?</p>
        <p className="text-danger">This action cannot be undone.</p>

        <div className="d-flex justify-content-end gap-2 mt-4">
          <Button 
            variant="outline-secondary" 
            onClick={onHide}
            disabled={loading}
          >
            Cancel
          </Button>
          
          <motion.div whileTap={{ scale: 0.95 }}>
            <Button 
              variant="danger" 
              onClick={handleDelete} 
              disabled={loading}
            >
              {loading ? 'Deleting...' : 'Delete Playlist'}
            </Button>
          </motion.div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default DeletePlaylistModal;
