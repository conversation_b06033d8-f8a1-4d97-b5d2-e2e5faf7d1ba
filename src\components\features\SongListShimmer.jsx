import React from 'react';
import { Table } from 'react-bootstrap';
import ShimmerEffect from '../common/ShimmerEffect';

/**
 * SongListShimmer - A table-based shimmer loader for song lists
 * 
 * @param {Object} props
 * @param {number} props.count - Number of shimmer rows to display
 * @param {boolean} props.showIndex - Whether to show the index column
 */
const SongListShimmer = ({ count = 5, showIndex = false }) => {
  return (
    <div className="song-list-container">
      <Table responsive hover variant="dark" className="song-table">
        <thead>
          <tr>
            {showIndex && <th className="text-center" style={{ width: '50px' }}>#</th>}
            <th>Title</th>
            <th>Artist</th>
            <th className="text-center" style={{ width: '80px' }}>Duration</th>
            <th className="text-center" style={{ width: '120px' }}>Actions</th>
          </tr>
        </thead>
        <tbody>
          {Array.from({ length: count }).map((_, index) => (
            <tr key={index} style={{ height: '60px' }}>
              {showIndex && (
                <td className="text-center align-middle">
                  <ShimmerEffect 
                    type="text" 
                    width="20px" 
                    height="20px" 
                    style={{ margin: '0 auto' }} 
                  />
                </td>
              )}
              <td className="align-middle">
                <div className="d-flex align-items-center">
                  <div className="me-3" style={{ width: '40px', height: '40px', flexShrink: 0 }}>
                    <ShimmerEffect type="image" width="40px" height="40px" rounded />
                  </div>
                  <div style={{ width: '70%' }}>
                    <ShimmerEffect type="text" height="1rem" className="mb-1" width="100%" />
                    <ShimmerEffect type="text" height="0.8rem" width="60%" />
                  </div>
                </div>
              </td>
              <td className="align-middle">
                <ShimmerEffect type="text" height="1rem" width="80%" />
              </td>
              <td className="text-center align-middle">
                <ShimmerEffect 
                  type="text" 
                  width="40px" 
                  height="1rem" 
                  style={{ margin: '0 auto' }} 
                />
              </td>
              <td className="text-center align-middle">
                <div className="d-flex justify-content-center gap-2">
                  <ShimmerEffect type="circle" width="32px" height="32px" />
                  <ShimmerEffect type="circle" width="32px" height="32px" />
                  <ShimmerEffect type="circle" width="32px" height="32px" />
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
    </div>
  );
};

export default SongListShimmer;
