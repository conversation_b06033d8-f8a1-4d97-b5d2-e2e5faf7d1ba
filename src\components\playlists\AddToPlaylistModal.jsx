import { useState, useEffect } from 'react';
import { <PERSON>dal, Button, Alert, ListGroup, Form, Row, Col } from 'react-bootstrap';
import { FaPlus, FaCheck, FaMusic, FaListUl, FaInfoCircle, FaLock } from 'react-icons/fa';
import { usePlaylist } from '../../context/PlaylistContext';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'framer-motion';
import CreatePlaylistModal from './CreatePlaylistModal';

const AddToPlaylistModal = ({ show, onHide, song }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedPlaylistId, setSelectedPlaylistId] = useState(null);
  const [songImage, setSongImage] = useState('');
  const [isValidSong, setIsValidSong] = useState(true);

  const { playlists, addSongToPlaylist } = usePlaylist();
  const { currentUser } = useAuth();

  // Validate song data
  useEffect(() => {
    setIsValidSong(song && song.id);
  }, [song]);

  // Set song image
  useEffect(() => {
    if (song && song.image && Array.isArray(song.image) && song.image.length > 0) {
      // Try to get the best quality image
      const bestImage = song.image.find(img => img && img.quality === '500x500')?.url ||
                        song.image[song.image.length - 1]?.url ||
                        song.image[0]?.url;
      setSongImage(bestImage || '');
    } else {
      setSongImage('');
    }
  }, [song]);

  const handleAddToPlaylist = async (playlistId) => {
    try {
      // Check if song is valid
      if (!song || !song.id) {
        setError("Invalid song data. Please try again.");
        return;
      }

      // Log song data for debugging
      console.log("Adding song to playlist:", song);

      // Ensure the song has all required properties for playback
      const enhancedSong = {
        ...song,
        // If duration is missing or 0, set a default (4 minutes)
        duration: song.duration || 240,
        // Make sure downloadUrl is present (even if empty, it will be fetched when played)
        downloadUrl: song.downloadUrl || []
      };

      console.log("Enhanced song for playlist:", enhancedSong);

      setError('');
      setSuccess('');
      setLoading(true);
      setSelectedPlaylistId(playlistId);

      // Find the playlist
      const playlist = playlists.find(p => p.id === playlistId);

      if (!playlist) {
        setError("Playlist not found. Please try again.");
        return;
      }

      // Check if song already exists in the playlist
      const songExists = playlist.songs && playlist.songs.some(s => s && s.id === song.id);

      if (songExists) {
        setError(`"${song?.name || 'Song'}" is already in "${playlist.name}"`);
        return;
      }

      await addSongToPlaylist(playlistId, enhancedSong);
      setSuccess(`Added "${enhancedSong?.name || 'Song'}" to "${playlist.name}"`);

      // Reset after a delay
      setTimeout(() => {
        setSuccess('');
        setSelectedPlaylistId(null);
      }, 2000);
    } catch (err) {
      console.error("Error adding song to playlist:", err);
      setError('Failed to add song to playlist: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Modal
        show={show}
        onHide={onHide}
        centered
        size="md"
        contentClassName="bg-dark text-light"
        backdropClassName="backdrop-blur"
      >
        <Modal.Header
          closeButton
          closeVariant="white"
          className="border-0"
          style={{
            background: 'linear-gradient(145deg, rgba(15, 15, 15, 0.95) 0%, rgba(30, 30, 30, 0.9) 100%)',
            borderBottom: '1px solid rgba(255, 255, 255, 0.05)'
          }}
        >
          <Modal.Title className="d-flex align-items-center">
            <FaListUl className="me-2" style={{ color: 'var(--dark-accent)' }} />
            Add to Playlist
          </Modal.Title>
        </Modal.Header>

        <Modal.Body style={{ padding: '1.5rem' }}>
          {/* Invalid Song Data Message */}
          {!isValidSong && (
            <div
              className="p-4 rounded mb-4 text-center"
              style={{
                background: 'rgba(220, 53, 69, 0.1)',
                border: '1px solid rgba(220, 53, 69, 0.2)'
              }}
            >
              <FaInfoCircle size={30} className="mb-3 text-danger" />
              <h6 className="mb-2 text-danger">Invalid Song Data</h6>
              <p className="text-muted mb-0">
                The song information is incomplete or invalid. Please try again with a valid song.
              </p>
            </div>
          )}

          {/* Authentication Required Message */}
          {isValidSong && !currentUser && (
            <>
              {/* Song Info Section */}
              <div className="mb-4 p-3 rounded" style={{
                background: 'rgba(0, 0, 0, 0.2)',
                border: '1px solid rgba(255, 255, 255, 0.05)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
              }}>
                <Row className="align-items-center">
                  <Col xs={3} sm={2}>
                    <div className="rounded overflow-hidden" style={{ aspectRatio: '1/1' }}>
                      {song && song.image && Array.isArray(song.image) && song.image[0] && song.image[0].url ? (
                        <img
                          src={song.image[0].url}
                          alt={song?.name || 'Song'}
                          className="img-fluid"
                          style={{ objectFit: 'cover', width: '100%', height: '100%' }}
                        />
                      ) : (
                        <div
                          className="d-flex align-items-center justify-content-center h-100"
                          style={{ backgroundColor: 'rgba(0, 0, 0, 0.3)' }}
                        >
                          <FaMusic style={{ color: 'var(--dark-accent)' }} />
                        </div>
                      )}
                    </div>
                  </Col>
                  <Col xs={9} sm={10}>
                    <h6 className="mb-0 text-truncate" style={{ color: 'var(--dark-text)' }}>
                      {song?.name || 'Untitled Song'}
                    </h6>
                    <small className="text-muted text-truncate d-block">
                      {song?.artists?.primary ?
                        song.artists.primary.map(artist => artist?.name || 'Unknown Artist').join(', ') :
                        'Unknown Artist'}
                    </small>
                  </Col>
                </Row>
              </div>

              <div
                className="p-4 rounded mb-4 text-center"
                style={{
                  background: 'rgba(255, 193, 7, 0.1)',
                  border: '1px solid rgba(255, 193, 7, 0.2)'
                }}
              >
                <FaLock size={30} className="mb-3 text-warning" />
                <h6 className="mb-2" style={{ color: '#ffc107' }}>Authentication Required</h6>
                <p className="text-muted mb-0">
                  You need to be logged in to add songs to playlists. Please sign in to continue.
                </p>
              </div>

              <div className="d-flex justify-content-center mt-4">
                <Button
                  variant="outline-secondary"
                  onClick={onHide}
                  className="me-2"
                  style={{ borderRadius: '8px' }}
                >
                  Cancel
                </Button>
                <Button
                  style={{
                    backgroundColor: 'var(--dark-accent)',
                    color: '#2c3e50',
                    borderRadius: '8px',
                    border: 'none'
                  }}
                  onClick={() => {
                    onHide();
                    // You can add navigation to login page here if needed
                  }}
                >
                  Sign In
                </Button>
              </div>
            </>
          )}

          {/* Main Playlist Content - Only show when song is valid and user is logged in */}
          {isValidSong && currentUser && (
            <>
              {/* Song Info Section */}
              <div className="mb-4 p-3 rounded" style={{
                background: 'rgba(0, 0, 0, 0.2)',
                border: '1px solid rgba(255, 255, 255, 0.05)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
              }}>
                <Row className="align-items-center">
                  <Col xs={3} sm={2}>
                    <div className="rounded overflow-hidden" style={{ aspectRatio: '1/1' }}>
                      <img
                        src={songImage || 'https://via.placeholder.com/80?text=Song'}
                        alt={song?.name || 'Song'}
                        className="img-fluid"
                        style={{ objectFit: 'cover', width: '100%', height: '100%' }}
                      />
                    </div>
                  </Col>
                  <Col xs={9} sm={10}>
                    <h6 className="mb-0 text-truncate" style={{ color: 'var(--dark-text)' }}>
                      {song?.name || 'Untitled Song'}
                    </h6>
                    <small className="text-muted text-truncate d-block">
                      {song?.artists?.primary ?
                        song.artists.primary.map(artist => artist?.name || 'Unknown Artist').join(', ') :
                        'Unknown Artist'}
                    </small>
                    <div className="mt-1">
                      <small className="text-muted d-flex align-items-center">
                        <FaInfoCircle size={10} className="me-1" /> Select a playlist below
                      </small>
                    </div>
                  </Col>
                </Row>
              </div>

              {/* Alerts */}
              {error && (
                <Alert
                  variant="danger"
                  className="mb-3 py-2"
                  style={{ backgroundColor: 'rgba(220, 53, 69, 0.15)', borderColor: 'rgba(220, 53, 69, 0.3)' }}
                >
                  {error}
                </Alert>
              )}

              {success && (
                <Alert
                  variant="success"
                  className="mb-3 py-2"
                  style={{ backgroundColor: 'rgba(40, 167, 69, 0.15)', borderColor: 'rgba(40, 167, 69, 0.3)' }}
                >
                  {success}
                </Alert>
              )}

              {/* Create New Playlist Button */}
              <div className="mb-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="btn w-100 d-flex align-items-center justify-content-center"
                  onClick={() => setShowCreateModal(true)}
                  style={{
                    backgroundColor: 'rgba(0, 255, 209, 0.15)',
                    color: 'var(--dark-accent)',
                    border: '1px solid rgba(0, 255, 209, 0.3)',
                    borderRadius: '8px',
                    padding: '0.75rem',
                    fontWeight: '500'
                  }}
                >
                  <FaPlus className="me-2" /> Create New Playlist
                </motion.button>
              </div>

              {/* Playlists Section */}
              <h6 className="mb-3 d-flex align-items-center">
                <FaMusic className="me-2" style={{ color: 'var(--dark-accent)', opacity: 0.8 }} />
                Your Playlists
              </h6>

              {playlists.length === 0 ? (
                <div
                  className="text-center p-4 rounded mb-3"
                  style={{
                    background: 'rgba(0, 0, 0, 0.2)',
                    border: '1px solid rgba(255, 255, 255, 0.05)'
                  }}
                >
                  <FaListUl size={30} className="mb-3 text-muted" />
                  <p className="mb-1" style={{ color: 'var(--dark-text)' }}>No playlists found</p>
                  <p className="text-muted mb-0" style={{ fontSize: '0.9rem' }}>
                    Create a playlist to add songs to it
                  </p>
                </div>
              ) : (
                <div
                  style={{
                    maxHeight: '300px',
                    overflowY: 'auto',
                    borderRadius: '8px',
                    border: '1px solid rgba(255, 255, 255, 0.05)',
                    background: 'rgba(0, 0, 0, 0.2)'
                  }}
                  className="mb-3 custom-scrollbar"
                >
                  <ListGroup variant="flush">
                    {playlists.map(playlist => (
                      <motion.div
                        key={playlist.id}
                        whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                      >
                        <ListGroup.Item
                          className="bg-transparent text-light border-secondary d-flex justify-content-between align-items-center"
                          action
                          onClick={() => handleAddToPlaylist(playlist.id)}
                          disabled={loading && selectedPlaylistId === playlist.id}
                          style={{
                            cursor: 'pointer',
                            borderColor: 'rgba(255, 255, 255, 0.05)',
                            padding: '0.75rem 1rem',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <div className="d-flex align-items-center">
                            <div
                              className="me-3 rounded d-flex align-items-center justify-content-center"
                              style={{
                                width: '40px',
                                height: '40px',
                                backgroundColor: 'rgba(0, 0, 0, 0.3)',
                                flexShrink: 0
                              }}
                            >
                              <FaMusic style={{ color: 'var(--dark-accent)', opacity: 0.8 }} />
                            </div>
                            <div>
                              <div className="fw-medium">{playlist.name}</div>
                              <small className="text-muted d-flex align-items-center">
                                <FaMusic className="me-1" size={10} />
                                {playlist.songs ? playlist.songs.length : 0} {playlist.songs && playlist.songs.length === 1 ? 'song' : 'songs'}
                              </small>
                            </div>
                          </div>

                          {loading && selectedPlaylistId === playlist.id ? (
                            <div
                              className="rounded-circle d-flex align-items-center justify-content-center"
                              style={{
                                width: '24px',
                                height: '24px',
                                overflow: 'hidden'
                              }}
                            >
                              <div className="shimmer-item" style={{ width: '100%', height: '100%', backgroundColor: 'rgba(0, 255, 209, 0.1)' }}>
                                <div className="shimmer-animation" />
                              </div>
                            </div>
                          ) : (
                            playlist.songs && song?.id && playlist.songs.some(s => s && s.id === song.id) && (
                              <div
                                className="rounded-circle d-flex align-items-center justify-content-center"
                                style={{
                                  width: '24px',
                                  height: '24px',
                                  backgroundColor: 'rgba(0, 255, 209, 0.15)',
                                  border: '1px solid rgba(0, 255, 209, 0.3)'
                                }}
                              >
                                <FaCheck size={12} style={{ color: 'var(--dark-accent)' }} />
                              </div>
                            )
                          )}
                        </ListGroup.Item>
                      </motion.div>
                    ))}
                  </ListGroup>
                </div>
              )}

              {/* Footer */}
              <div className="d-flex justify-content-between align-items-center mt-4">
                <small className="text-muted d-flex align-items-center">
                  <FaLock size={10} className="me-1" /> Only you can see your playlists
                </small>
                <Button
                  variant="outline-secondary"
                  onClick={onHide}
                  style={{ borderRadius: '8px' }}
                >
                  Close
                </Button>
              </div>
            </>
          )}

          {/* Simple Close Button for Invalid Song */}
          {!isValidSong && (
            <div className="d-flex justify-content-end mt-3">
              <Button
                variant="outline-secondary"
                onClick={onHide}
                style={{ borderRadius: '8px' }}
              >
                Close
              </Button>
            </div>
          )}
        </Modal.Body>
      </Modal>

      <CreatePlaylistModal
        show={showCreateModal}
        onHide={() => setShowCreateModal(false)}
      />
    </>
  );
};

export default AddToPlaylistModal;
