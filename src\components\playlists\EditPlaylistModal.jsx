import { useState, useEffect } from 'react';
import { Modal, Button, Form, Alert } from 'react-bootstrap';
import { usePlaylist } from '../../context/PlaylistContext';
import { motion } from 'framer-motion';

const EditPlaylistModal = ({ show, onHide, playlist }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { updatePlaylist } = usePlaylist();

  // Set initial values when playlist changes
  useEffect(() => {
    if (playlist) {
      setName(playlist.name || '');
      setDescription(playlist.description || '');
    }
  }, [playlist]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!name.trim()) {
      setError('Please enter a playlist name');
      return;
    }

    try {
      setError('');
      setLoading(true);

      const updates = {
        name,
        description
      };

      await updatePlaylist(playlist.id, updates);
      onHide();
    } catch (err) {
      setError('Failed to update playlist: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      contentClassName="bg-dark text-light"
      backdropClassName="backdrop-blur"
    >
      <Modal.Header closeButton closeVariant="white" className="border-0">
        <Modal.Title>Edit Playlist</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Playlist Name</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter playlist name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="bg-dark text-light border-secondary"
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Description (optional)</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              placeholder="Enter description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="bg-dark text-light border-secondary"
            />
          </Form.Group>

          <div className="d-flex justify-content-end gap-2">
            <Button
              variant="outline-secondary"
              onClick={onHide}
              disabled={loading}
            >
              Cancel
            </Button>

            <motion.div whileTap={{ scale: 0.95 }}>
              <Button
                variant="primary"
                type="submit"
                disabled={loading}
                style={{ backgroundColor: 'var(--dark-accent)', color: '#2c3e50', border: 'none', fontWeight: '600' }}
              >
                {loading ? 'Saving...' : 'Save Changes'}
              </Button>
            </motion.div>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default EditPlaylistModal;
