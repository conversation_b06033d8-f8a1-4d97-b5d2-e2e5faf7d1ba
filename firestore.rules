rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write only their own playlists
    match /users/{userId}/playlists/{playlistId} {
      allow read, write, delete: if request.auth != null && request.auth.uid == userId;
    }

    // Allow authenticated users to read public data
    match /songs/{songId} {
      allow read: if true;
    }

    match /albums/{albumId} {
      allow read: if true;
    }

    match /artists/{artistId} {
      allow read: if true;
    }
  }
}
