/**
 * Application version management
 * This file handles version checking and cache busting
 */

// Build timestamp - will be replaced during build
export const BUILD_TIMESTAMP = import.meta.env.VITE_BUILD_TIMESTAMP || new Date().toISOString();

// App version - update this when releasing new versions
export const APP_VERSION = '1.0.2';

// Cache duration in milliseconds (1 hour)
const CACHE_DURATION = 60 * 60 * 1000;

/**
 * Check if the application needs to be updated
 * @returns {Promise<boolean>} True if an update is needed
 */
export const checkForUpdates = async () => {
  try {
    // Get the last check time
    const lastCheckTime = localStorage.getItem('lastUpdateCheckTime');
    const currentTime = new Date().getTime();
    
    // Skip check if we've checked recently (within CACHE_DURATION)
    if (lastCheckTime && (currentTime - parseInt(lastCheckTime)) < CACHE_DURATION) {
      return false;
    }
    
    // Store current check time
    localStorage.setItem('lastUpdateCheckTime', currentTime.toString());
    
    // Get the current version from localStorage
    const storedVersion = localStorage.getItem('appVersion');
    const storedTimestamp = localStorage.getItem('buildTimestamp');
    
    // If no stored version or timestamp, this is first run - store current values
    if (!storedVersion || !storedTimestamp) {
      localStorage.setItem('appVersion', APP_VERSION);
      localStorage.setItem('buildTimestamp', BUILD_TIMESTAMP);
      return false;
    }
    
    // Check if version or build timestamp has changed
    const versionChanged = storedVersion !== APP_VERSION;
    const timestampChanged = storedTimestamp !== BUILD_TIMESTAMP;
    
    // If either has changed, update stored values and return true
    if (versionChanged || timestampChanged) {
      localStorage.setItem('appVersion', APP_VERSION);
      localStorage.setItem('buildTimestamp', BUILD_TIMESTAMP);
      return true;
    }
    
    // Check for new version on the server by requesting a version.json file
    // This file will be generated during build with the current timestamp
    try {
      const response = await fetch(`/version.json?t=${new Date().getTime()}`);
      if (response.ok) {
        const data = await response.json();
        if (data.buildTimestamp && data.buildTimestamp !== storedTimestamp) {
          localStorage.setItem('buildTimestamp', data.buildTimestamp);
          return true;
        }
      }
    } catch (error) {
      console.error('Error checking for version updates:', error);
    }
    
    return false;
  } catch (error) {
    console.error('Error in checkForUpdates:', error);
    return false;
  }
};

/**
 * Force reload the application
 */
export const forceReload = () => {
  // Clear any cached resources
  if ('caches' in window) {
    caches.keys().then(cacheNames => {
      cacheNames.forEach(cacheName => {
        caches.delete(cacheName);
      });
    });
  }
  
  // Hard reload the page
  window.location.reload(true);
};
