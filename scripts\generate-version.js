/**
 * Generate version.json file for cache busting
 * This script creates a version.json file with the current build timestamp
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create version.json with current timestamp
const generateVersionFile = () => {
  const timestamp = new Date().toISOString();
  
  // Create version data
  const versionData = {
    buildTimestamp: timestamp,
    buildDate: new Date().toUTCString()
  };
  
  // Ensure dist directory exists
  const distDir = path.resolve(__dirname, '../dist');
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
  
  // Write version.json file
  fs.writeFileSync(
    path.resolve(distDir, 'version.json'),
    JSON.stringify(versionData, null, 2)
  );
  
  // Also set environment variable for the build process
  process.env.VITE_BUILD_TIMESTAMP = timestamp;
  
  console.log(`✅ Generated version.json with timestamp: ${timestamp}`);
};

// Run the generator
generateVersionFile();
