import React from 'react';
import { Row, Col } from 'react-bootstrap';
import { motion } from 'framer-motion';
import ShimmerEffect from './ShimmerEffect';

/**
 * ShimmerGrid - A responsive grid layout for shimmer loading effects
 * 
 * @param {Object} props
 * @param {string} props.type - Type of grid (songs, albums, artists, playlists)
 * @param {number} props.count - Number of items to display
 * @param {number} props.xs - Number of columns on extra small screens (default: 2)
 * @param {number} props.sm - Number of columns on small screens (default: 2)
 * @param {number} props.md - Number of columns on medium screens (default: 3)
 * @param {number} props.lg - Number of columns on large screens (default: 4)
 * @param {number} props.xl - Number of columns on extra large screens (default: 5)
 * @param {string} props.className - Additional CSS classes
 */
const ShimmerGrid = ({
  type = 'songs',
  count = 8,
  xs = 2,
  sm = 2,
  md = 3,
  lg = 4,
  xl = 5,
  className = ''
}) => {
  // Generate shimmer items based on type
  const renderShimmerItem = (index) => {
    switch (type) {
      case 'songs':
        return (
          <div className="song-card-shimmer h-100">
            <div className="position-relative rounded overflow-hidden" style={{ aspectRatio: '1/1' }}>
              <ShimmerEffect type="image" height="100%" />
              <div className="position-absolute" style={{ 
                top: '10px', 
                right: '10px',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                overflow: 'hidden'
              }}>
                <ShimmerEffect type="circle" width="100%" height="100%" />
              </div>
            </div>
            <div className="p-2">
              <ShimmerEffect type="text" height="1.2rem" className="mb-2" width="80%" />
              <ShimmerEffect type="text" height="0.9rem" width="60%" />
            </div>
          </div>
        );
        
      case 'albums':
        return (
          <div className="album-card-shimmer h-100">
            <div className="position-relative rounded-top overflow-hidden" style={{ aspectRatio: '1/1' }}>
              <ShimmerEffect type="image" height="100%" />
            </div>
            <div className="p-3" style={{ 
              backgroundColor: 'rgba(15, 15, 15, 0.5)',
              borderBottomLeftRadius: '0.75rem',
              borderBottomRightRadius: '0.75rem',
              border: '1px solid rgba(255, 255, 255, 0.05)',
              borderTop: 'none'
            }}>
              <ShimmerEffect type="text" height="1.2rem" className="mb-2" width="90%" />
              <ShimmerEffect type="text" height="0.9rem" width="70%" />
              <div className="d-flex align-items-center mt-2">
                <ShimmerEffect type="text" height="0.8rem" width="40%" />
              </div>
            </div>
          </div>
        );
        
      case 'artists':
        return (
          <div className="artist-card-shimmer h-100 text-center p-3" style={{
            backgroundColor: 'rgba(15, 15, 15, 0.5)',
            borderRadius: '0.75rem',
            border: '1px solid rgba(255, 255, 255, 0.05)'
          }}>
            <div className="mx-auto mb-3" style={{ 
              width: '120px', 
              height: '120px',
              borderRadius: '50%',
              overflow: 'hidden'
            }}>
              <ShimmerEffect type="circle" width="100%" height="100%" />
            </div>
            <ShimmerEffect type="text" height="1.2rem" className="mb-2" width="70%" style={{ margin: '0 auto' }} />
            <ShimmerEffect type="text" height="0.9rem" width="50%" style={{ margin: '0 auto' }} />
          </div>
        );
        
      case 'playlists':
        return (
          <div className="playlist-card-shimmer h-100">
            <div className="position-relative rounded-top overflow-hidden" style={{ aspectRatio: '1/1' }}>
              <ShimmerEffect type="image" height="100%" />
              <div className="position-absolute d-flex align-items-center justify-content-center" style={{
                bottom: '0',
                right: '0',
                width: '40px',
                height: '40px',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderTopLeftRadius: '0.75rem'
              }}>
                <ShimmerEffect type="circle" width="24px" height="24px" />
              </div>
            </div>
            <div className="p-3" style={{ 
              backgroundColor: 'rgba(15, 15, 15, 0.5)',
              borderBottomLeftRadius: '0.75rem',
              borderBottomRightRadius: '0.75rem',
              border: '1px solid rgba(255, 255, 255, 0.05)',
              borderTop: 'none'
            }}>
              <ShimmerEffect type="text" height="1.2rem" className="mb-2" width="90%" />
              <ShimmerEffect type="text" height="0.9rem" width="70%" className="mb-2" />
              <div className="d-flex justify-content-between align-items-center mt-2">
                <ShimmerEffect type="text" height="0.8rem" width="40%" />
                <ShimmerEffect type="circle" width="24px" height="24px" />
              </div>
            </div>
          </div>
        );
        
      default:
        return (
          <div className="default-card-shimmer h-100">
            <div className="position-relative rounded overflow-hidden" style={{ aspectRatio: '1/1' }}>
              <ShimmerEffect type="image" height="100%" />
            </div>
            <div className="p-2">
              <ShimmerEffect type="text" height="1.2rem" className="mb-2" width="80%" />
              <ShimmerEffect type="text" height="0.9rem" width="60%" />
            </div>
          </div>
        );
    }
  };

  // Animation for staggered appearance
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.3 } }
  };

  return (
    <motion.div
      className={`shimmer-grid ${className}`}
      variants={container}
      initial="hidden"
      animate="show"
    >
      <Row className="g-4">
        {Array.from({ length: count }).map((_, index) => (
          <Col 
            key={index} 
            xs={12 / xs} 
            sm={12 / sm} 
            md={12 / md} 
            lg={12 / lg} 
            xl={12 / xl}
          >
            <motion.div variants={item}>
              {renderShimmerItem(index)}
            </motion.div>
          </Col>
        ))}
      </Row>
    </motion.div>
  );
};

export default ShimmerGrid;
