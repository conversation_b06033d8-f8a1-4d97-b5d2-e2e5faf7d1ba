import React from 'react';
import { FaGith<PERSON>, FaLinkedin, FaInstagram } from 'react-icons/fa';

const MobileFooter = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      className="d-block d-lg-none py-3 text-center"
      style={{
        backgroundColor: 'rgba(15, 15, 15, 0.95)',
        borderTop: '1px solid rgba(255, 255, 255, 0.05)',
        color: 'var(--dark-text)',
        marginBottom: '60px' // Space for mobile nav bar
      }}
    >
      <div className="container">
        <div className="d-flex justify-content-center gap-4 mb-3">
          <a
            href="https://github.com/infinityabhinav"
            target="_blank"
            rel="noopener noreferrer"
            className="social-icon-link"
            aria-label="GitHub"
            style={{
              width: '36px',
              height: '36px',
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'var(--dark-text)',
              fontSize: '1.1rem',
              transition: 'all 0.3s ease'
            }}
          >
            <FaGithub />
          </a>

          <a
            href="https://www.linkedin.com/in/infinityabhinav"
            target="_blank"
            rel="noopener noreferrer"
            className="social-icon-link"
            aria-label="LinkedIn"
            style={{
              width: '36px',
              height: '36px',
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'var(--dark-text)',
              fontSize: '1.1rem',
              transition: 'all 0.3s ease'
            }}
          >
            <FaLinkedin />
          </a>

          <a
            href="https://www.instagram.com/avkr5104"
            target="_blank"
            rel="noopener noreferrer"
            className="social-icon-link"
            aria-label="Instagram"
            style={{
              width: '36px',
              height: '36px',
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'var(--dark-text)',
              fontSize: '1.1rem',
              transition: 'all 0.3s ease'
            }}
          >
            <FaInstagram />
          </a>
        </div>
        <p className="mb-0 small text-muted">
          &copy; {currentYear} Musafir Music. All rights reserved.
        </p>
      </div>
    </footer>
  );
};

export default MobileFooter;
