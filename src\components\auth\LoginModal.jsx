import { useState } from 'react';
import { <PERSON><PERSON>, Button, Form, Alert } from 'react-bootstrap';
import { FaGoogle } from 'react-icons/fa';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'framer-motion';
import logger from '../../utils/logger';

const LoginModal = ({ show, onHide, switchToSignup, switchToReset }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { login, signInWithGoogle } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    try {
      setError('');
      setLoading(true);
      logger.info("Attempting to login with:", email);
      const user = await login(email, password);
      logger.info("Login successful:", user);
      onHide();
    } catch (err) {
      logger.error("Login error:", err);
      setError('Failed to sign in: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setError('');
      setLoading(true);
      logger.info("Attempting to sign in with Google");
      const user = await signInWithGoogle();
      logger.info("Google sign-in successful:", user);
      onHide();
    } catch (err) {
      logger.error("Google sign-in error:", err);

      // Display a more user-friendly error message
      if (err.code === 'auth/popup-closed-by-user') {
        setError('Sign-in was cancelled. Please try again.');
      } else if (err.code === 'auth/popup-blocked') {
        setError('Sign-in popup was blocked by your browser. Please allow popups for this site.');
      } else if (err.code === 'auth/cancelled-popup-request') {
        setError('Another sign-in attempt is in progress. Please wait.');
      } else if (err.code === 'auth/internal-error') {
        setError('Authentication service encountered an error. This might be due to network issues or browser settings. Please try again later.');
      } else {
        setError('Failed to sign in with Google: ' + (err.message || 'Unknown error'));
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      contentClassName="bg-dark text-light"
      backdropClassName="backdrop-blur"
    >
      <Modal.Header closeButton closeVariant="white" className="border-0">
        <Modal.Title>Login</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Email address</Form.Label>
            <Form.Control
              type="email"
              placeholder="Enter email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="bg-dark text-light border-secondary"
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Password</Form.Label>
            <Form.Control
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="bg-dark text-light border-secondary"
            />
          </Form.Group>

          <div className="d-grid gap-2 mb-3">
            <motion.div whileTap={{ scale: 0.95 }}>
              <Button
                variant="primary"
                type="submit"
                disabled={loading}
                className="w-100"
                style={{ backgroundColor: 'var(--dark-accent)', color: '#2c3e50', border: 'none', fontWeight: '600' }}
              >
                {loading ? 'Signing in...' : 'Sign In'}
              </Button>
            </motion.div>
          </div>

          <div className="d-grid gap-2">
            <motion.div whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline-light"
                onClick={handleGoogleSignIn}
                disabled={loading}
                className="w-100 d-flex align-items-center justify-content-center"
              >
                <FaGoogle className="me-2" /> Sign in with Google
              </Button>
            </motion.div>
          </div>
        </Form>

        <div className="mt-3 text-center">
          <p className="mb-1">
            <Button
              variant="link"
              onClick={switchToReset}
              className="p-0 text-muted"
            >
              Forgot password?
            </Button>
          </p>
          <p className="mb-0">
            Don't have an account?{' '}
            <Button
              variant="link"
              onClick={switchToSignup}
              className="p-0"
              style={{ color: 'var(--dark-accent)' }}
            >
              Sign up
            </Button>
          </p>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default LoginModal;
