import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaVolumeUp, FaVolumeDown, FaVolumeMute, FaVolumeOff } from 'react-icons/fa';

const VolumeControl = ({
  volume,
  isMuted,
  onVolumeChange,
  onMuteToggle,
  isExpanded
}) => {
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const volumeSliderRef = useRef(null);
  const volumeTimeoutRef = useRef(null);

  // Get appropriate volume icon
  const getVolumeIcon = () => {
    if (isMuted || volume === 0) return <FaVolumeMute />;
    if (volume < 0.3) return <FaVolumeOff />;
    if (volume < 0.7) return <FaVolumeDown />;
    return <FaVolumeUp />;
  };

  // Handle volume slider interaction
  const handleVolumeSliderMouseDown = useCallback((event) => {
    if (!volumeSliderRef.current) return;

    setIsDragging(true);
    const rect = volumeSliderRef.current.getBoundingClientRect();
    const clientY = event.type.includes('touch') ? event.touches[0].clientY : event.clientY;
    
    // Calculate volume based on vertical position (inverted)
    const newVolume = Math.max(0, Math.min(1, 1 - (clientY - rect.top) / rect.height));
    onVolumeChange(newVolume);

    // Clear any existing timeout
    if (volumeTimeoutRef.current) {
      clearTimeout(volumeTimeoutRef.current);
    }
  }, [onVolumeChange]);

  const handleVolumeSliderMouseMove = useCallback((event) => {
    if (!isDragging || !volumeSliderRef.current) return;

    const rect = volumeSliderRef.current.getBoundingClientRect();
    const clientY = event.type.includes('touch') ? event.touches[0].clientY : event.clientY;
    
    // Calculate volume based on vertical position (inverted)
    const newVolume = Math.max(0, Math.min(1, 1 - (clientY - rect.top) / rect.height));
    onVolumeChange(newVolume);
  }, [isDragging, onVolumeChange]);

  const handleVolumeSliderMouseUp = useCallback(() => {
    setIsDragging(false);
    
    // Auto-hide volume slider after interaction
    volumeTimeoutRef.current = setTimeout(() => {
      setShowVolumeSlider(false);
    }, 2000);
  }, []);

  // Handle volume button interactions
  const handleVolumeButtonClick = useCallback(() => {
    onMuteToggle();
  }, [onMuteToggle]);

  const handleVolumeButtonHover = useCallback(() => {
    setShowVolumeSlider(true);
    
    // Clear any existing timeout
    if (volumeTimeoutRef.current) {
      clearTimeout(volumeTimeoutRef.current);
    }
  }, []);

  const handleVolumeAreaLeave = useCallback(() => {
    if (!isDragging) {
      volumeTimeoutRef.current = setTimeout(() => {
        setShowVolumeSlider(false);
      }, 1000);
    }
  }, [isDragging]);

  // Global mouse events for volume dragging
  React.useEffect(() => {
    const handleGlobalMouseMove = (event) => {
      if (isDragging) {
        handleVolumeSliderMouseMove(event);
      }
    };

    const handleGlobalMouseUp = () => {
      if (isDragging) {
        handleVolumeSliderMouseUp();
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.addEventListener('touchmove', handleGlobalMouseMove);
      document.addEventListener('touchend', handleGlobalMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('touchmove', handleGlobalMouseMove);
      document.removeEventListener('touchend', handleGlobalMouseUp);
    };
  }, [isDragging, handleVolumeSliderMouseMove, handleVolumeSliderMouseUp]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (volumeTimeoutRef.current) {
        clearTimeout(volumeTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div 
      className={`volume-control ${isExpanded ? 'expanded' : 'collapsed'}`}
      onMouseEnter={handleVolumeButtonHover}
      onMouseLeave={handleVolumeAreaLeave}
    >
      {/* Volume button */}
      <motion.button
        className={`volume-btn ${isMuted ? 'muted' : ''}`}
        onClick={handleVolumeButtonClick}
        whileTap={{ scale: 0.95 }}
        whileHover={{ scale: 1.05 }}
        aria-label={isMuted ? "Unmute" : "Mute"}
      >
        {getVolumeIcon()}
      </motion.button>

      {/* Volume percentage display (expanded mode) */}
      {isExpanded && (
        <div className="volume-percentage">
          {Math.round((isMuted ? 0 : volume) * 100)}%
        </div>
      )}

      {/* Volume slider */}
      <AnimatePresence>
        {(showVolumeSlider || isExpanded) && (
          <motion.div
            className="volume-slider-container"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <div 
              className="volume-slider"
              ref={volumeSliderRef}
              onMouseDown={handleVolumeSliderMouseDown}
              onTouchStart={handleVolumeSliderMouseDown}
            >
              {/* Volume track */}
              <div className="volume-track">
                {/* Volume progress */}
                <motion.div 
                  className="volume-progress"
                  style={{ height: `${(isMuted ? 0 : volume) * 100}%` }}
                  transition={{ duration: isDragging ? 0 : 0.1 }}
                />
                
                {/* Volume thumb */}
                <motion.div 
                  className="volume-thumb"
                  style={{ bottom: `${(isMuted ? 0 : volume) * 100}%` }}
                  animate={{
                    scale: isDragging ? 1.3 : 1,
                    opacity: isDragging ? 1 : 0.9
                  }}
                  transition={{ duration: 0.2 }}
                />
              </div>
              
              {/* Volume level indicators */}
              <div className="volume-indicators">
                {[...Array(5)].map((_, index) => (
                  <div 
                    key={index}
                    className={`volume-indicator ${
                      (isMuted ? 0 : volume) > (index / 4) ? 'active' : ''
                    }`}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default VolumeControl;
