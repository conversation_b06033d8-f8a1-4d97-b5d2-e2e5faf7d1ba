import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Fa<PERSON>lay, Fa<PERSON>ause, FaH<PERSON><PERSON>, FaReg<PERSON>eart, FaPlus } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useSong } from '../../context/SongContext';
import { usePlaylist } from '../../context/PlaylistContext';
import { useAuth } from '../../context/AuthContext';
import AddToPlaylistModal from '../playlists/AddToPlaylistModal';
import { decodeHtmlEntities } from '../../utils/textUtils';

const SongCard = ({ song, isFavorite = false, onToggleFavorite }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showAddToPlaylistModal, setShowAddToPlaylistModal] = useState(false);
  const { currentSong, isPlaying, playSong, pauseSong } = useSong();
  const { addToLikedSongs, removeFromLikedSongs } = usePlaylist();
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  // Check if this song is currently playing
  const isCurrentSong = currentSong && currentSong.id === song.id;

  // Get high quality image
  const getImage = () => {
    if (!song.image || song.image.length === 0) {
      return 'https://via.placeholder.com/300';
    }

    // Find the highest quality image
    const highQualityImage = song.image.find(img => img.quality === '500x500');
    if (highQualityImage) return highQualityImage.url;

    return song.image[song.image.length - 1].url;
  };

  const handleCardClick = () => {
    // Navigate to song details page
    navigate(`/song/${song.id}`);
  };



  const handleFavoriteClick = async (e) => {
    e.stopPropagation(); // Prevent navigation

    // Call the parent component's toggle function for local state
    onToggleFavorite(song);

    // Update the Liked Songs playlist if user is logged in
    if (currentUser) {
      try {
        if (isFavorite) {
          // If it was favorited and now being unfavorited, remove from playlist
          await removeFromLikedSongs(song.id);
        } else {
          // If it was not favorited and now being favorited, add to playlist
          await addToLikedSongs(song);
        }
      } catch (err) {
        console.error("Error updating Liked Songs playlist:", err);
        // Continue even if playlist update fails
      }
    }
  };

  // Function to handle touch events for mobile
  const handleTouchStart = () => {
    // On mobile, we'll show the controls on touch
    setIsHovered(true);

    // Set a timeout to hide the controls after 3 seconds
    const timer = setTimeout(() => {
      setIsHovered(false);
    }, 3000);

    // Store the timer ID so we can clear it if needed
    return () => clearTimeout(timer);
  };

  return (
    <motion.div
      className="song-card h-100 cursor-pointer"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{
        y: -5,
        transition: { duration: 0.3 }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onTouchStart={handleTouchStart}
      onClick={handleCardClick}
      style={{
        backgroundColor: 'rgba(15, 15, 15, 0.5)',
        borderRadius: '0.75rem',
        overflow: 'hidden',
        border: '1px solid rgba(255, 255, 255, 0.05)',
        boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
        backdropFilter: 'blur(10px)',
        width: '100%',
        maxWidth: '100%'
      }}
    >
      <div className="position-relative">
        <div style={{
          width: '100%',
          paddingTop: '100%', /* 1:1 Aspect Ratio */
          position: 'relative',
          overflow: 'hidden',
          borderBottom: '1px solid rgba(255, 255, 255, 0.05)',
          minHeight: '120px'
        }}>
          <img
            src={getImage()}
            alt={song.name}
            loading="lazy"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        </div>

        {/* Play/Pause button that's always visible */}
        <div
          className="position-absolute bottom-0 end-0 m-2"
          style={{ zIndex: 2 }}
        >
          <motion.button
            className="btn rounded-circle d-flex align-items-center justify-content-center"
            whileHover={{ scale: 1.15 }}
            whileTap={{ scale: 0.9 }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (isCurrentSong && isPlaying) {
                console.log('Pause button clicked');
                pauseSong();
              } else {
                console.log('Play button clicked for song:', song.name);
                playSong(song);
                navigate(`/song/${song.id}`);
              }
            }}
            aria-label={isCurrentSong && isPlaying ? "Pause" : "Play"}
            style={{
              width: window.innerWidth < 576 ? '40px' : '36px',
              height: window.innerWidth < 576 ? '40px' : '36px',
              backgroundColor: isCurrentSong && isPlaying ? '#FF6B6B' : 'var(--dark-accent)',
              color: '#0f0f0f',
              boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
              padding: 0
            }}
          >
            {isCurrentSong && isPlaying ?
              <FaPause size={window.innerWidth < 576 ? 14 : 12} /> :
              <FaPlay size={window.innerWidth < 576 ? 14 : 12} />
            }
          </motion.button>
        </div>

        {/* Buttons that appear on hover */}
        {isHovered && (
          <motion.div
            className="position-absolute top-0 end-0 m-2 d-flex flex-column gap-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{ zIndex: 2 }}
          >
            {/* Favorite button */}
            <motion.button
              className="btn rounded-circle d-flex align-items-center justify-content-center song-action-btn"
              whileHover={{ scale: 1.3 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleFavoriteClick}
              aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
              style={{
                width: '40px',
                height: '40px',
                backgroundColor: 'rgba(15, 15, 15, 0.7)',
                backdropFilter: 'blur(4px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                padding: 0
              }}
            >
              {isFavorite ?
                <FaHeart style={{ color: '#FF6B6B', fontSize: '20px' }} /> :
                <FaRegHeart style={{ color: 'white', fontSize: '20px' }} />
              }
            </motion.button>

            {/* Add to playlist button */}
            <motion.button
              className="btn rounded-circle d-flex align-items-center justify-content-center song-action-btn"
              whileHover={{ scale: 1.3 }}
              whileTap={{ scale: 0.9 }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setShowAddToPlaylistModal(true);
              }}
              aria-label="Add to playlist"
              style={{
                width: '40px',
                height: '40px',
                backgroundColor: 'rgba(15, 15, 15, 0.7)',
                backdropFilter: 'blur(4px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                padding: 0
              }}
            >
              <FaPlus style={{ color: 'white', fontSize: '16px' }} />
            </motion.button>
          </motion.div>
        )}

        {/* Gradient overlay */}
        <div
          className="position-absolute top-0 start-0 w-100 h-100"
          style={{
            background: 'linear-gradient(to top, rgba(15, 15, 15, 0.8) 0%, rgba(15, 15, 15, 0) 50%)',
            zIndex: 1
          }}
        ></div>
      </div>

      <div className="p-2" style={{
        padding: window.innerWidth < 576 ? '0.5rem !important' : '0.75rem !important'
      }}>
        <h6 className="text-truncate mb-1 fw-bold" style={{
          color: 'var(--dark-text)',
          fontSize: window.innerWidth < 576 ? '0.85rem' : '0.9rem',
          lineHeight: '1.2'
        }}>{decodeHtmlEntities(song.name)}</h6>
        <p className="text-muted text-truncate mb-0" style={{
          fontSize: window.innerWidth < 576 ? '0.7rem' : '0.75rem',
          lineHeight: '1.2'
        }}>
          {song.artists && song.artists.primary && song.artists.primary.map(artist => decodeHtmlEntities(artist.name)).join(', ')}
        </p>
      </div>

      {/* Add to Playlist Modal */}
      <AddToPlaylistModal
        show={showAddToPlaylistModal}
        onHide={() => setShowAddToPlaylistModal(false)}
        song={song}
      />
    </motion.div>
  );
};

export default SongCard;
