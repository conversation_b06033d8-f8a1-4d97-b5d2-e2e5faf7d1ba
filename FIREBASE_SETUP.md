# Firebase Setup for Deployed Application

This guide will help you properly configure Firebase for your deployed application on Vercel or other hosting platforms.

## Common Issues with Firebase Authentication

When deploying a Firebase application, you might encounter authentication issues, especially with Google Sign-In. Here are some common issues and their solutions:

### 1. Authorized Domains

Firebase requires that all domains using authentication are explicitly authorized in the Firebase Console.

**Solution:**
1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to Authentication > Settings > Authorized domains
4. Add your deployed domain (e.g., `musafirmusic.vercel.app`)
5. Save changes

### 2. OAuth Redirect Domains

For Google Sign-In to work properly, you need to add your domain to the authorized redirect domains.

**Solution:**
1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to Authentication > Sign-in method
4. Edit the Google provider
5. Add your domain to the authorized domains list
6. Save changes

### 3. Content Security Policy (CSP)

A restrictive Content Security Policy can block the authentication popup.

**Solution:**
Update your CSP in `vercel.json` to include the necessary domains:

```json
{
  "key": "Content-Security-Policy",
  "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.google.com https://*.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://*.gstatic.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https://* https://*.google.com https://*.googleapis.com; connect-src 'self' https://saavn.dev https://*.firebaseio.com https://*.googleapis.com https://accounts.google.com; frame-src 'self' https://*.google.com https://accounts.google.com; media-src 'self' https://*; object-src 'none'"
}
```

### 4. X-Frame-Options Header

The `X-Frame-Options: DENY` header can prevent the authentication popup from working.

**Solution:**
Change it to `SAMEORIGIN` in your `vercel.json`:

```json
{ "key": "X-Frame-Options", "value": "SAMEORIGIN" }
```

## Environment Variables

Ensure all required Firebase environment variables are set in your Vercel project:

1. Go to your Vercel project settings
2. Navigate to the Environment Variables section
3. Add the following variables:
   - `VITE_FIREBASE_API_KEY`
   - `VITE_FIREBASE_AUTH_DOMAIN`
   - `VITE_FIREBASE_PROJECT_ID`
   - `VITE_FIREBASE_STORAGE_BUCKET`
   - `VITE_FIREBASE_MESSAGING_SENDER_ID`
   - `VITE_FIREBASE_APP_ID`
   - `VITE_FIREBASE_MEASUREMENT_ID` (optional)

## Testing Authentication

After making these changes:

1. Deploy your application again
2. Open the browser console (F12)
3. Try to sign in with Google
4. Check for any error messages in the console

If you still encounter issues, the error messages in the console should provide more specific information about what's going wrong.

## Troubleshooting

### Internal Error (auth/internal-error)

This generic error can have multiple causes:

1. **Network Issues**: Check if your browser can access Google's authentication servers
2. **CSP Blocking**: Ensure your Content Security Policy allows all necessary domains
3. **Popup Blocked**: Check if your browser is blocking popups
4. **Incorrect Configuration**: Verify all Firebase configuration values are correct

### Popup Closed (auth/popup-closed-by-user)

This occurs when the user closes the authentication popup before completing the process.

### Popup Blocked (auth/popup-blocked)

This occurs when the browser blocks the authentication popup.

**Solution:**
1. Allow popups for your domain
2. Ensure the sign-in is triggered by a user action (like a button click)

## Additional Resources

- [Firebase Authentication Documentation](https://firebase.google.com/docs/auth)
- [Troubleshooting Firebase Authentication](https://firebase.google.com/docs/auth/web/google-signin#troubleshooting)
- [Vercel Deployment Documentation](https://vercel.com/docs/deployments/overview)
