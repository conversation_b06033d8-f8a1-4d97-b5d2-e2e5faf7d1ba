import { lazy, Suspense, useEffect } from 'react';
import { BrowserRouter, Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { ThemeProvider } from './context/ThemeContext';
import { SongProvider } from './context/SongContext';
import { AuthProvider } from './context/AuthContext';
import { PlaylistProvider } from './context/PlaylistContext';
import Layout from './components/layout/Layout';
import ErrorBoundary from './components/common/ErrorBoundary';
import LoadingSpinner from './components/common/LoadingSpinner';
import GlobalAudioPlayer from './components/features/GlobalAudioPlayer';
import UpdateAlert from './components/common/UpdateAlert';

// Import styles
import './styles/main.scss';
import './styles/mobile.css'; // Mobile-specific styles

// Enhanced component to handle SPA routing
const RouteHandler = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check for the initialPath in sessionStorage (set by index.html)
    const initialPath = sessionStorage.getItem('initialPath');

    // Check if this is a fresh page load (set by index.html)
    const isFreshLoad = window.__SPA_ROUTE_FALLBACK__;

    // If we have a stored path and this is a fresh page load
    if (initialPath && location.pathname === '/' && initialPath !== '/' && isFreshLoad) {
      // Clear it so we don't redirect again
      sessionStorage.removeItem('initialPath');

      // Remove the global flag
      window.__SPA_ROUTE_FALLBACK__ = false;

      // Navigate to the stored path
      console.log('SPA Routing: Redirecting to:', initialPath);
      navigate(initialPath, { replace: true });
    }
  }, [navigate, location.pathname]);

  return null; // This component doesn't render anything
};

// Lazy load pages for better performance
const Home = lazy(() => import('./pages/Home'));
const Library = lazy(() => import('./pages/Library'));
const SongDetails = lazy(() => import('./pages/SongDetails'));
const Search = lazy(() => import('./pages/Search'));
const Playlists = lazy(() => import('./pages/Playlists'));
const PlaylistDetail = lazy(() => import('./pages/PlaylistDetail'));
const Profile = lazy(() => import('./pages/Profile'));
const NotFound = lazy(() => import('./pages/NotFound'));

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <BrowserRouter>
          <AuthProvider>
            <SongProvider>
              <PlaylistProvider>
                <Suspense fallback={<LoadingSpinner fullPage />}>
                  {/* Enhanced SPA routing handler */}
                  <RouteHandler />

                  <Routes>
                    <Route path="/" element={<Layout />}>
                      <Route index element={<Home />} />
                      <Route path="library" element={<Library />} />
                      <Route path="song/:id" element={<SongDetails />} />
                      <Route path="search" element={<Search />} />
                      <Route path="playlists" element={<Playlists />} />
                      <Route path="playlist/:id" element={<PlaylistDetail />} />
                      <Route path="profile" element={<Profile />} />
                      <Route path="*" element={<NotFound />} />
                    </Route>
                  </Routes>

                  {/* Global audio player that persists across routes */}
                  <GlobalAudioPlayer />

                  {/* Update alert that shows on page load */}
                  <UpdateAlert />
                </Suspense>
              </PlaylistProvider>
            </SongProvider>
          </AuthProvider>
        </BrowserRouter>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
