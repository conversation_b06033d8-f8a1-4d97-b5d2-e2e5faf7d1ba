import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import viteCompression from 'vite-plugin-compression'
import { splitVendorChunkPlugin } from 'vite'
import { execSync } from 'child_process'

// Generate a unique build ID based on timestamp
const timestamp = new Date().toISOString()

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    splitVendorChunkPlugin(), // Split vendor chunks for better caching
    viteCompression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 10240, // Only compress files larger than 10kb
      deleteOriginFile: false
    }),
    viteCompression({
      algorithm: 'brotliCompress',
      ext: '.br',
      threshold: 10240, // Only compress files larger than 10kb
      deleteOriginFile: false
    })
  ],
  build: {
    // Generate sourcemaps for better debugging
    sourcemap: 'hidden',

    // Minify the output for production
    minify: 'esbuild',

    // Target modern browsers for smaller bundle size
    target: 'es2020',

    // Reduce chunk size warnings threshold
    chunkSizeWarningLimit: 1000,

    // Optimize CSS
    cssCodeSplit: true,

    // Configure rollup options
    rollupOptions: {
      output: {
        // Chunk files to improve loading performance
        manualChunks: {
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'firebase-vendor': ['firebase/app', 'firebase/auth', 'firebase/firestore'],
          'ui-vendor': ['react-bootstrap', 'framer-motion', 'react-icons'],
          'query-vendor': ['@tanstack/react-query']
        },
        // Ensure clean URLs in multi-page apps with strong cache busting
        entryFileNames: `assets/[name].[hash].${timestamp.replace(/[:.]/g, '-')}.js`,
        chunkFileNames: `assets/[name].[hash].${timestamp.replace(/[:.]/g, '-')}.js`,
        assetFileNames: `assets/[name].[hash].${timestamp.replace(/[:.]/g, '-')}.[ext]`
      }
    },

    // Optimize dependencies
    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true
    },

    // Write manifest file for better asset management
    manifest: true,

    // Ensure assets are properly hashed for cache busting
    assetsInlineLimit: 4096, // 4kb

    // Generate version.json file after build
    emptyOutDir: true
  },

  // Configure server options
  server: {
    port: 5173, // Default Vite port
    strictPort: false, // Allow fallback to another port if 5173 is in use
    host: true,
    cors: true, // Enable CORS for development
    headers: {
      'Access-Control-Allow-Origin': '*',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block'
    },
    // Handle SPA routing with detailed configuration
    historyApiFallback: {
      disableDotRule: true,
      rewrites: [
        { from: /^\/.*/, to: '/index.html' }
      ]
    }
  },

  // Preview server configuration (for testing production builds)
  preview: {
    port: 4173,
    strictPort: false,
    host: true,
    cors: true,
    // Handle SPA routing in preview mode with detailed configuration
    historyApiFallback: {
      disableDotRule: true,
      rewrites: [
        { from: /^\/.*/, to: '/index.html' }
      ]
    }
  },

  // Define environment variables for cache busting
  define: {
    'import.meta.env.VITE_BUILD_TIMESTAMP': JSON.stringify(timestamp),
    'import.meta.env.VITE_GIT_COMMIT': JSON.stringify(
      process.env.VERCEL_GIT_COMMIT_SHA ||
      (() => {
        try {
          return execSync('git rev-parse --short HEAD').toString().trim()
        } catch (e) {
          return 'unknown'
        }
      })()
    )
  }
})
