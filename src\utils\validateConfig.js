/**
 * Utility to validate environment configuration
 */
import logger from './logger';

/**
 * Validates that all required environment variables are present
 * @returns {boolean} True if all required variables are present, false otherwise
 */
export const validateEnvConfig = () => {
  const requiredVars = [
    'VITE_API_URL',
    'VITE_FIREBASE_API_KEY',
    'VITE_FIREBASE_AUTH_DOMAIN',
    'VITE_FIREBASE_PROJECT_ID',
    'VITE_FIREBASE_STORAGE_BUCKET',
    'VITE_FIREBASE_MESSAGING_SENDER_ID',
    'VITE_FIREBASE_APP_ID'
  ];

  const missingVars = requiredVars.filter(varName => !import.meta.env[varName]);

  if (missingVars.length > 0) {
    logger.error('Missing required environment variables:', missingVars);
    return false;
  }

  logger.info('Environment configuration validated successfully');
  return true;
};

/**
 * Validates Firebase configuration
 * @param {Object} config - Firebase configuration object
 * @returns {boolean} True if configuration is valid, false otherwise
 */
export const validateFirebaseConfig = (config) => {
  const requiredProps = [
    'apiKey',
    'authDomain',
    'projectId',
    'storageBucket',
    'messagingSenderId',
    'appId'
  ];

  const missingProps = requiredProps.filter(prop => !config[prop]);

  if (missingProps.length > 0) {
    logger.error('Missing required Firebase configuration properties:', missingProps);
    return false;
  }

  // Validate API key format (basic check)
  if (config.apiKey && !config.apiKey.startsWith('AIza')) {
    logger.error('Firebase API key appears to be invalid');
    return false;
  }

  logger.info('Firebase configuration validated successfully');
  return true;
};

export default {
  validateEnvConfig,
  validateFirebaseConfig
};
