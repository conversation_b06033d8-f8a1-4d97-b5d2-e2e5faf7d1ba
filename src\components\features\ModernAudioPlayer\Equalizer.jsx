import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaRedo, FaSave } from 'react-icons/fa';

const Equalizer = ({ audioRef, onClose }) => {
  const [audioContext, setAudioContext] = useState(null);
  const [analyser, setAnalyser] = useState(null);
  const [gainNodes, setGainNodes] = useState([]);
  const [presets, setPresets] = useState({
    flat: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    rock: [3, 2, -1, -2, 1, 2, 3, 4, 4, 4],
    pop: [1, 2, 3, 2, 0, -1, -1, 1, 2, 3],
    jazz: [2, 1, 0, 1, 2, 2, 1, 1, 2, 3],
    classical: [3, 2, 1, 0, 0, 0, 1, 2, 3, 4],
    electronic: [4, 3, 1, 0, -1, 1, 2, 3, 4, 4],
    bass: [4, 3, 2, 1, 0, 0, 0, 0, 0, 0],
    treble: [0, 0, 0, 0, 0, 1, 2, 3, 4, 4],
    vocal: [0, 1, 2, 3, 2, 1, 0, 1, 2, 1]
  });
  
  const [currentPreset, setCurrentPreset] = useState('flat');
  const [eqValues, setEqValues] = useState(presets.flat);
  const [isEnabled, setIsEnabled] = useState(true);
  const [visualizerData, setVisualizerData] = useState(new Array(64).fill(0));
  
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const sourceRef = useRef(null);

  // Frequency bands (Hz)
  const frequencies = [32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000];

  // Initialize audio context and equalizer
  useEffect(() => {
    if (!audioRef.current) return;

    const initializeEqualizer = () => {
      try {
        const context = new (window.AudioContext || window.webkitAudioContext)();
        const source = context.createMediaElementSource(audioRef.current);
        const analyserNode = context.createAnalyser();
        
        analyserNode.fftSize = 128;
        analyserNode.smoothingTimeConstant = 0.8;
        
        // Create gain nodes for each frequency band
        const nodes = frequencies.map((freq, index) => {
          const filter = context.createBiquadFilter();
          const gain = context.createGain();
          
          // Configure filter
          if (index === 0) {
            filter.type = 'lowshelf';
            filter.frequency.value = freq;
          } else if (index === frequencies.length - 1) {
            filter.type = 'highshelf';
            filter.frequency.value = freq;
          } else {
            filter.type = 'peaking';
            filter.frequency.value = freq;
            filter.Q.value = 1;
          }
          
          // Set initial gain
          gain.gain.value = Math.pow(10, eqValues[index] / 20);
          
          return { filter, gain, frequency: freq };
        });
        
        // Connect nodes
        let previousNode = source;
        nodes.forEach(({ filter, gain }) => {
          previousNode.connect(filter);
          filter.connect(gain);
          previousNode = gain;
        });
        
        previousNode.connect(analyserNode);
        analyserNode.connect(context.destination);
        
        setAudioContext(context);
        setAnalyser(analyserNode);
        setGainNodes(nodes);
        sourceRef.current = source;
        
        // Start visualization
        startVisualization(analyserNode);
        
      } catch (error) {
        console.error('Failed to initialize equalizer:', error);
      }
    };

    // Wait for audio to be ready
    if (audioRef.current.readyState >= 2) {
      initializeEqualizer();
    } else {
      audioRef.current.addEventListener('canplay', initializeEqualizer);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext) {
        audioContext.close();
      }
    };
  }, [audioRef]);

  // Start visualization
  const startVisualization = (analyserNode) => {
    const dataArray = new Uint8Array(analyserNode.frequencyBinCount);
    
    const animate = () => {
      analyserNode.getByteFrequencyData(dataArray);
      setVisualizerData([...dataArray]);
      drawVisualizer(dataArray);
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
  };

  // Draw visualizer
  const drawVisualizer = (dataArray) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    ctx.clearRect(0, 0, width, height);
    
    const barWidth = width / dataArray.length;
    const gradient = ctx.createLinearGradient(0, height, 0, 0);
    gradient.addColorStop(0, '#00FFD1');
    gradient.addColorStop(0.5, '#00A8FF');
    gradient.addColorStop(1, '#FF6B6B');
    
    ctx.fillStyle = gradient;
    
    dataArray.forEach((value, index) => {
      const barHeight = (value / 255) * height;
      const x = index * barWidth;
      const y = height - barHeight;
      
      ctx.fillRect(x, y, barWidth - 1, barHeight);
    });
  };

  // Handle EQ value change
  const handleEqChange = (index, value) => {
    const newValues = [...eqValues];
    newValues[index] = value;
    setEqValues(newValues);
    setCurrentPreset('custom');
    
    // Update gain node
    if (gainNodes[index] && isEnabled) {
      gainNodes[index].gain.value = Math.pow(10, value / 20);
    }
  };

  // Apply preset
  const applyPreset = (presetName) => {
    const preset = presets[presetName];
    if (!preset) return;
    
    setEqValues(preset);
    setCurrentPreset(presetName);
    
    // Update all gain nodes
    if (gainNodes.length > 0 && isEnabled) {
      gainNodes.forEach((node, index) => {
        node.gain.value = Math.pow(10, preset[index] / 20);
      });
    }
  };

  // Reset equalizer
  const resetEqualizer = () => {
    applyPreset('flat');
  };

  // Toggle equalizer
  const toggleEqualizer = () => {
    setIsEnabled(!isEnabled);
    
    if (gainNodes.length > 0) {
      gainNodes.forEach((node, index) => {
        node.gain.value = isEnabled ? 1 : Math.pow(10, eqValues[index] / 20);
      });
    }
  };

  return (
    <motion.div
      className="equalizer-panel"
      initial={{ opacity: 0, y: 300 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 300 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="equalizer-header">
        <div className="header-left">
          <h3>Equalizer</h3>
          <button
            className={`toggle-btn ${isEnabled ? 'enabled' : 'disabled'}`}
            onClick={toggleEqualizer}
          >
            {isEnabled ? 'ON' : 'OFF'}
          </button>
        </div>
        
        <div className="header-right">
          <button className="action-btn" onClick={resetEqualizer} title="Reset">
            <FaRedo />
          </button>
          <button className="action-btn" onClick={onClose} title="Close">
            <FaTimes />
          </button>
        </div>
      </div>

      {/* Visualizer */}
      <div className="visualizer-container">
        <canvas
          ref={canvasRef}
          width={300}
          height={80}
          className="visualizer-canvas"
        />
      </div>

      {/* Presets */}
      <div className="eq-presets">
        {Object.keys(presets).map((presetName) => (
          <button
            key={presetName}
            className={`preset-btn ${currentPreset === presetName ? 'active' : ''}`}
            onClick={() => applyPreset(presetName)}
          >
            {presetName.charAt(0).toUpperCase() + presetName.slice(1)}
          </button>
        ))}
      </div>

      {/* EQ Sliders */}
      <div className="eq-sliders">
        {frequencies.map((freq, index) => (
          <div key={freq} className="eq-slider-container">
            <div className="frequency-label">
              {freq >= 1000 ? `${freq / 1000}k` : freq}
            </div>
            
            <input
              type="range"
              min="-12"
              max="12"
              step="0.5"
              value={eqValues[index]}
              onChange={(e) => handleEqChange(index, parseFloat(e.target.value))}
              className="eq-slider"
              orient="vertical"
              disabled={!isEnabled}
            />
            
            <div className="gain-value">
              {eqValues[index] > 0 ? '+' : ''}{eqValues[index]}dB
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default Equalizer;
