import { useState } from 'react';
import { Card, Dropdown } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FaEllipsisH, FaPlay, FaMusic, FaEdit, FaTrash } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { usePlaylist } from '../../context/PlaylistContext';
import EditPlaylistModal from './EditPlaylistModal';
import DeletePlaylistModal from './DeletePlaylistModal';

const PlaylistCard = ({ playlist }) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const navigate = useNavigate();

  const { name, description, songs = [] } = playlist;

  // Get a high-quality cover image from the first song in the playlist, or use a custom default
  const coverImage = songs && songs.length > 0 && songs[0] && songs[0].image && songs[0].image.length > 0
    ? (
      // Try to get the highest quality image available
      songs[0].image.find(img => img.quality === '500x500')?.url ||
      songs[0].image[songs[0].image.length - 1]?.url ||
      songs[0].image[0].url
    )
    : `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300' viewBox='0 0 300 300'%3E%3Crect width='300' height='300' fill='%230f0f0f'/%3E%3Cpath d='M150,70 C110,70 80,100 80,140 C80,180 110,210 150,210 C190,210 220,180 220,140 C220,100 190,70 150,70 Z M150,190 C120,190 100,170 100,140 C100,110 120,90 150,90 C180,90 200,110 200,140 C200,170 180,190 150,190 Z' fill='%2300FFD1' opacity='0.7'/%3E%3Ccircle cx='150' cy='140' r='30' fill='%230f0f0f'/%3E%3Ccircle cx='150' cy='140' r='10' fill='%2300FFD1'/%3E%3C/svg%3E`;

  return (
    <>
      <motion.div
        className="playlist-card"
        whileHover={{ y: -5 }}
        whileTap={{ scale: 0.98 }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Card
          className="h-100 bg-dark text-light border-0"
          style={{
            borderRadius: '1rem',
            overflow: 'hidden',
            boxShadow: '0 10px 20px rgba(0, 0, 0, 0.15)',
            backdropFilter: 'blur(10px)',
            backgroundColor: 'rgba(15, 15, 15, 0.5)',
            border: '1px solid rgba(255, 255, 255, 0.05)',
            transition: 'all 0.3s ease'
          }}
        >
          <div
            className="text-decoration-none cursor-pointer"
            onClick={() => navigate(`/playlist/${playlist.id}`)}
            style={{ cursor: 'pointer' }}
          >
            <div className="position-relative">
              <Card.Img
                variant="top"
                src={coverImage}
                style={{
                  height: '200px',
                  objectFit: 'cover',
                  filter: 'brightness(0.9)',
                  transition: 'all 0.5s ease',
                  imageRendering: 'high-quality',
                  boxShadow: 'inset 0 0 30px rgba(0, 0, 0, 0.3)'
                }}
                className="hover-zoom"
              />

              {/* Play button overlay */}
              {isHovered && (
                <div
                  className="position-absolute top-50 start-50 translate-middle"
                  style={{ zIndex: 2 }}
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="rounded-circle d-flex align-items-center justify-content-center"
                    style={{
                      width: '60px',
                      height: '60px',
                      backgroundColor: 'var(--dark-accent)',
                      boxShadow: '0 4px 15px rgba(0, 255, 209, 0.4)',
                      border: '2px solid rgba(0, 255, 209, 0.2)'
                    }}
                  >
                    <FaPlay style={{ color: '#2c3e50', fontSize: '20px', marginLeft: '4px' }} />
                  </motion.div>
                </div>
              )}

              {/* Song count badge */}
              <div
                className="position-absolute bottom-0 start-0 m-3 d-flex align-items-center"
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  backdropFilter: 'blur(5px)',
                  padding: '6px 12px',
                  borderRadius: '50rem',
                  fontSize: '0.85rem',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(255, 255, 255, 0.1)'
                }}
              >
                <FaMusic className="me-2" style={{ fontSize: '0.8rem', color: 'var(--dark-accent)' }} />
                <span style={{ fontWeight: '500' }}>
                  {songs ? songs.length : 0} {songs && songs.length === 1 ? 'song' : 'songs'}
                </span>
              </div>

              {/* Options dropdown */}
              <div className="position-absolute top-0 end-0 m-2">
                <Dropdown>
                  <Dropdown.Toggle
                    variant="link"
                    id={`dropdown-playlist-${playlist.id}`}
                    className="p-1 text-light"
                    style={{
                      backgroundColor: 'rgba(0, 0, 0, 0.7)',
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '50%',
                      width: '36px',
                      height: '36px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
                      backdropFilter: 'blur(5px)'
                    }}
                  >
                    <FaEllipsisH size={14} />
                  </Dropdown.Toggle>

                  <Dropdown.Menu
                    variant="dark"
                    style={{
                      backgroundColor: 'rgba(15, 15, 15, 0.95)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.05)',
                      borderRadius: '0.75rem',
                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
                      padding: '0.5rem'
                    }}
                  >
                    <Dropdown.Item
                      onClick={() => setShowEditModal(true)}
                      style={{
                        borderRadius: '0.5rem',
                        padding: '0.5rem 1rem',
                        transition: 'all 0.2s ease'
                      }}
                    >
                      <FaEdit className="me-2" size={14} /> Edit Playlist
                    </Dropdown.Item>
                    <Dropdown.Item
                      onClick={() => setShowDeleteModal(true)}
                      className="text-danger"
                      style={{
                        borderRadius: '0.5rem',
                        padding: '0.5rem 1rem',
                        transition: 'all 0.2s ease'
                      }}
                    >
                      <FaTrash className="me-2" size={14} /> Delete Playlist
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              </div>
            </div>
          </div>

          <Card.Body className="p-3">
            <div
              className="text-decoration-none"
              onClick={() => navigate(`/playlist/${playlist.id}`)}
              style={{ cursor: 'pointer' }}
            >
              <Card.Title
                className="mb-1 text-truncate fw-bold"
                style={{
                  color: '#ffffff',
                  fontSize: '1.1rem',
                  textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                }}
              >
                {name}
              </Card.Title>
              {description && (
                <Card.Text
                  className="text-truncate"
                  style={{
                    fontSize: '0.85rem',
                    color: '#e0e0e0',
                    opacity: 0.9
                  }}
                >
                  {description}
                </Card.Text>
              )}
            </div>
          </Card.Body>
        </Card>
      </motion.div>

      <EditPlaylistModal
        show={showEditModal}
        onHide={() => setShowEditModal(false)}
        playlist={playlist}
      />

      <DeletePlaylistModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        playlist={playlist}
      />
    </>
  );
};

export default PlaylistCard;
