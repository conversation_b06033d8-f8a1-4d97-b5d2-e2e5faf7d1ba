import axios from 'axios';

const API_BASE_URL = 'https://saavn.dev/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Add timeout to prevent hanging requests
  timeout: 15000,
});

// Search endpoints
export const searchAll = async (query) => {
  const response = await api.get(`/search?query=${encodeURIComponent(query)}`);
  return response.data;
};

export const searchSongs = async (query, page = 0, limit = 10) => {
  const response = await api.get(`/search/songs?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`);
  return response.data;
};

export const searchAlbums = async (query, page = 0, limit = 10) => {
  const response = await api.get(`/search/albums?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`);
  return response.data;
};

export const searchArtists = async (query, page = 0, limit = 10) => {
  const response = await api.get(`/search/artists?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`);
  return response.data;
};

export const searchPlaylists = async (query, page = 0, limit = 10) => {
  const response = await api.get(`/search/playlists?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`);
  return response.data;
};

// Song endpoints
export const getSongById = async (id) => {
  const response = await api.get(`/songs/${id}`);
  return response.data;
};

export const getSongsByIds = async (ids) => {
  const response = await api.get(`/songs?ids=${ids}`);
  return response.data;
};

export const getSongsByLink = async (link) => {
  const response = await api.get(`/songs?link=${encodeURIComponent(link)}`);
  return response.data;
};

export const getSongSuggestions = async (id, limit = 10) => {
  const response = await api.get(`/songs/${id}/suggestions?limit=${limit}`);
  return response.data;
};

// Get trending Hindi songs based on most listened in the latest year
export const getTrendingSongs = async (limit = 20) => {
  // Since the API doesn't have a direct trending endpoint, we'll search for popular Hindi terms
  // Using "top hindi" to get most listened Hindi songs
  const response = await api.get(`/search/songs?query=top hindi 2023&page=0&limit=${limit}`);
  return response.data;
};

// Get latest Hindi songs based on the latest year
export const getLatestSongs = async (limit = 20) => {
  // Using the current year with Hindi to get the latest Hindi releases
  const currentYear = new Date().getFullYear();
  const response = await api.get(`/search/songs?query=hindi ${currentYear}&page=0&limit=${limit}`);
  return response.data;
};

// Get featured Hindi song (latest released)
export const getFeaturedHindiSong = async () => {
  // Using "new hindi" to get the latest Hindi releases
  const response = await api.get(`/search/songs?query=new hindi&page=0&limit=1`);
  return response.data;
};

// Album endpoints
export const getAlbumById = async (id) => {
  const response = await api.get(`/albums/${id}`);
  return response.data;
};

export const getAlbumsByLink = async (link) => {
  const response = await api.get(`/albums?link=${encodeURIComponent(link)}`);
  return response.data;
};

// Artist endpoints
export const getArtistById = async (id) => {
  const response = await api.get(`/artists/${id}`);
  return response.data;
};

export const getArtistsByLink = async (link) => {
  const response = await api.get(`/artists?link=${encodeURIComponent(link)}`);
  return response.data;
};

// Playlist endpoints
export const getPlaylistById = async (id) => {
  const response = await api.get(`/playlists/${id}`);
  return response.data;
};

export const getPlaylistsByLink = async (link) => {
  const response = await api.get(`/playlists?link=${encodeURIComponent(link)}`);
  return response.data;
};

export default api;
