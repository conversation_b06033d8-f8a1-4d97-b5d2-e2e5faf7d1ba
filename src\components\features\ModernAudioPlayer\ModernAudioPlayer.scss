// Modern Audio Player Styles
:root {
  --player-primary: #00FFD1;
  --player-secondary: #00A8FF;
  --player-accent: #FF6B6B;
  --player-dark-bg: #0f0f0f;
  --player-dark-card: #1a1a1a;
  --player-light-bg: #ffffff;
  --player-light-card: #f8f9fa;
  --player-text-primary: #ffffff;
  --player-text-secondary: #b3b3b3;
  --player-border: rgba(255, 255, 255, 0.1);
  --player-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  --player-radius: 16px;
  --player-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-audio-player {
  position: relative;
  width: 100%;
  border-radius: var(--player-radius);
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid var(--player-border);
  box-shadow: var(--player-shadow);
  transition: var(--player-transition);
  z-index: 1000;

  &.dark-theme {
    background: linear-gradient(135deg, 
      rgba(15, 15, 15, 0.95) 0%, 
      rgba(26, 26, 26, 0.95) 100%);
    color: var(--player-text-primary);
  }

  &.light-theme {
    background: linear-gradient(135deg, 
      rgba(248, 249, 250, 0.95) 0%, 
      rgba(255, 255, 255, 0.95) 100%);
    color: #333333;
    --player-text-primary: #333333;
    --player-text-secondary: #666666;
    --player-border: rgba(0, 0, 0, 0.1);
  }

  &.expanded {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    z-index: 9999;
  }

  // Background with blur effect
  .player-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    filter: blur(40px) brightness(0.3);
    opacity: 0.6;
    z-index: -1;
  }

  // Main content container
  .player-content {
    position: relative;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-height: 80px;

    .expanded & {
      padding: 24px;
      height: 100vh;
      justify-content: space-between;
    }
  }

  // Top bar with action buttons
  .player-top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .collapsed & {
      display: none;
    }

    .player-actions-left,
    .player-actions-right {
      display: flex;
      gap: 8px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--player-text-primary);
      cursor: pointer;
      transition: var(--player-transition);
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
      }

      &.close-btn {
        background: rgba(255, 107, 107, 0.2);
        
        &:hover {
          background: rgba(255, 107, 107, 0.3);
        }
      }

      .liked {
        color: var(--player-accent);
      }
    }
  }
}

// Song Information Styles
.song-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;

  &.expanded {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .song-artwork {
    position: relative;
    flex-shrink: 0;

    .collapsed & {
      width: 60px;
      height: 60px;
    }

    .expanded & {
      width: 280px;
      height: 280px;
    }

    .artwork-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
      transition: var(--player-transition);

      &.loaded {
        opacity: 1;
      }
    }

    .artwork-placeholder {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--player-primary), var(--player-secondary));
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .expand-btn {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.7);
      border: none;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      opacity: 0;
      transition: var(--player-transition);
      cursor: pointer;

      .song-artwork:hover & {
        opacity: 1;
      }
    }

    .artwork-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid var(--player-primary);
        border-radius: 50%;
      }
    }
  }

  .song-details {
    flex: 1;
    min-width: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .expanded & {
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }

    .song-text {
      min-width: 0;
      flex: 1;

      .song-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--player-text-primary);
        margin: 0 0 4px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .expanded & {
          font-size: 24px;
          white-space: normal;
          text-align: center;
        }
      }

      .song-artist {
        font-size: 14px;
        color: var(--player-text-secondary);
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .expanded & {
          font-size: 16px;
          white-space: normal;
          text-align: center;
        }
      }

      .song-metadata {
        margin-top: 16px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        p {
          margin: 0;
          font-size: 14px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .metadata-label {
          color: var(--player-text-secondary);
          font-weight: 500;
        }

        .metadata-value {
          color: var(--player-text-primary);
        }
      }
    }

    .expand-btn-text {
      background: none;
      border: none;
      color: var(--player-text-secondary);
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      transition: var(--player-transition);

      &:hover {
        color: var(--player-text-primary);
        background: rgba(255, 255, 255, 0.1);
      }

      .expanded & {
        display: none;
      }
    }
  }

  .collapse-btn {
    position: absolute;
    top: 24px;
    right: 24px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--player-text-primary);
    cursor: pointer;
    transition: var(--player-transition);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .collapsed & {
      display: none;
    }
  }
}

// Seek Bar Styles
.seekbar-section {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .time-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--player-text-secondary);
    font-variant-numeric: tabular-nums;

    .collapsed & {
      display: none;
    }
  }

  .seekbar-container {
    position: relative;
    height: 6px;
    cursor: pointer;
    padding: 8px 0;

    &.hovering .seekbar-track {
      height: 8px;
    }

    &.dragging .seekbar-track {
      height: 10px;
    }

    .seekbar-track {
      position: relative;
      width: 100%;
      height: 6px;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 3px;
      transition: var(--player-transition);
      overflow: hidden;
      cursor: pointer;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg,
          rgba(0, 255, 209, 0.1) 0%,
          rgba(0, 168, 255, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .seekbar-container:hover &::before {
        opacity: 1;
      }
    }

    .seekbar-buffered {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background: rgba(255, 255, 255, 0.25);
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .seekbar-progress {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background: linear-gradient(90deg,
        var(--player-primary) 0%,
        var(--player-secondary) 100%);
      border-radius: 3px;
      transition: width 0.1s ease;
      box-shadow: 0 0 10px rgba(0, 255, 209, 0.3);

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 20px;
        height: 100%;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.3) 100%);
        border-radius: 0 3px 3px 0;
      }
    }

    .seekbar-thumb {
      position: absolute;
      top: 50%;
      width: 16px;
      height: 16px;
      background: var(--player-primary);
      border: 2px solid rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow: 0 4px 12px rgba(0, 255, 209, 0.5);
      opacity: 0;
      transition: var(--player-transition);
      cursor: grab;

      &:active {
        cursor: grabbing;
      }

      .seekbar-container:hover &,
      .seekbar-container.dragging & {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
      }

      .seekbar-container.dragging & {
        box-shadow: 0 6px 20px rgba(0, 255, 209, 0.8);
      }
    }

    .seekbar-preview {
      position: absolute;
      bottom: 100%;
      transform: translateX(-50%);
      margin-bottom: 8px;
      pointer-events: none;

      .preview-tooltip {
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        backdrop-filter: blur(10px);
      }
    }
  }

  .duration-time-compact {
    font-size: 12px;
    color: var(--player-text-secondary);
    text-align: right;
    font-variant-numeric: tabular-nums;

    .expanded & {
      display: none;
    }
  }
}

// Player Controls Styles
.player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;

  &.expanded {
    flex-direction: column;
    gap: 24px;
  }

  .primary-controls {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .secondary-controls {
    display: flex;
    align-items: center;
    gap: 12px;

    .collapsed & {
      display: none;
    }
  }

  .compact-controls {
    display: flex;
    align-items: center;
    gap: 8px;

    .expanded & {
      display: none;
    }
  }

  .control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--player-text-primary);
    cursor: pointer;
    transition: var(--player-transition);
    backdrop-filter: blur(10px);
    font-size: 18px;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    &.active {
      background: var(--player-primary);
      color: #000000;
      box-shadow: 0 0 20px rgba(0, 255, 209, 0.5);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
  }

  .play-pause-btn {
    width: 64px;
    height: 64px;
    background: var(--player-primary);
    color: #000000;
    font-size: 24px;
    box-shadow: 0 0 20px rgba(0, 255, 209, 0.5);

    &:hover {
      background: var(--player-primary);
      box-shadow: 0 0 30px rgba(0, 255, 209, 0.7);
      transform: scale(1.1);
    }

    .expanded & {
      width: 80px;
      height: 80px;
      font-size: 32px;
    }
  }

  .control-btn-small {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .repeat-btn {
    position: relative;

    .repeat-indicator {
      position: absolute;
      top: -4px;
      right: -4px;
      background: var(--player-primary);
      color: #000000;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      font-size: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }
}

// Volume Control Styles
.volume-control {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;

  &.expanded {
    flex-direction: column;
    gap: 16px;
  }

  .volume-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--player-text-primary);
    cursor: pointer;
    transition: var(--player-transition);
    font-size: 16px;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    &.muted {
      color: var(--player-accent);
    }
  }

  .volume-percentage {
    font-size: 12px;
    color: var(--player-text-secondary);
    min-width: 32px;
    text-align: center;
    font-variant-numeric: tabular-nums;

    .collapsed & {
      display: none;
    }
  }

  .volume-slider-container {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 20px;
    background: rgba(0, 0, 0, 0.95);
    border-radius: 12px;
    padding: 16px 12px;
    backdrop-filter: blur(20px);
    border: 1px solid var(--player-border);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    min-width: 60px;

    .expanded & {
      position: static;
      transform: none;
      margin: 0;
      background: rgba(255, 255, 255, 0.1);
      box-shadow: none;
    }

    .collapsed & {
      z-index: 10001;

      // Ensure it doesn't interfere with other elements
      &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: -1;
        pointer-events: none;
      }
    }
  }

  .volume-slider {
    width: 8px;
    height: 100px;
    position: relative;
    cursor: pointer;

    .expanded & {
      height: 120px;
    }

    .volume-track {
      position: relative;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      overflow: hidden;
    }

    .volume-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background: linear-gradient(0deg, var(--player-primary), var(--player-secondary));
      border-radius: 4px;
      transition: height 0.1s ease;
    }

    .volume-thumb {
      position: absolute;
      left: 50%;
      width: 16px;
      height: 16px;
      background: var(--player-primary);
      border-radius: 50%;
      transform: translateX(-50%);
      box-shadow: 0 2px 8px rgba(0, 255, 209, 0.4);
      transition: var(--player-transition);
    }
  }

  .volume-indicators {
    position: absolute;
    right: -20px;
    top: 0;
    height: 100%;
    display: flex;
    flex-direction: column-reverse;
    justify-content: space-between;
    align-items: center;

    .volume-indicator {
      width: 3px;
      height: 3px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transition: var(--player-transition);

      &.active {
        background: var(--player-primary);
        box-shadow: 0 0 4px rgba(0, 255, 209, 0.5);
      }
    }
  }
}

// Error Message Styles
.error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  margin: 8px 0;

  .error-text {
    color: var(--player-accent);
    font-size: 14px;
    flex: 1;
  }

  .error-retry {
    background: var(--player-accent);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--player-transition);
    margin-left: 12px;

    &:hover {
      background: #ff5252;
      transform: scale(1.05);
    }
  }
}

// Enhanced Player Styles
.modern-audio-player {
  // Add glassmorphism effect
  background: rgba(15, 15, 15, 0.8);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(0, 255, 209, 0.1) 0%,
      rgba(0, 168, 255, 0.05) 50%,
      rgba(255, 107, 107, 0.1) 100%
    );
    border-radius: inherit;
    z-index: -1;
  }

  // Improved hover effects
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  }

  // Better transitions
  * {
    transition: var(--player-transition);
  }

  // Loading state
  &.loading {
    .player-content {
      opacity: 0.7;
      pointer-events: none;
    }
  }

  // Enhanced background blur
  .player-background {
    filter: blur(60px) brightness(0.2) saturate(150%);
    transform: scale(1.1);
    opacity: 0.8;
  }
}

// Playlist Queue Styles
.playlist-queue {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: rgba(15, 15, 15, 0.95);
  backdrop-filter: blur(20px);
  border-left: 1px solid var(--player-border);
  display: flex;
  flex-direction: column;
  z-index: 10000;

  &.empty {
    justify-content: center;
    align-items: center;
  }

  .queue-header {
    padding: 24px;
    border-bottom: 1px solid var(--player-border);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .queue-info {
      h3 {
        margin: 0 0 4px 0;
        color: var(--player-text-primary);
        font-size: 20px;
        font-weight: 600;
      }

      .queue-count {
        font-size: 14px;
        color: var(--player-text-secondary);
      }
    }

    .close-btn {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--player-text-primary);
      cursor: pointer;
      transition: var(--player-transition);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .empty-queue {
    text-align: center;
    color: var(--player-text-secondary);

    svg {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    p {
      font-size: 16px;
      margin: 0;
    }
  }

  .queue-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--player-primary);
      border-radius: 3px;
    }
  }

  .queue-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 24px;
    cursor: pointer;
    transition: var(--player-transition);
    border-left: 3px solid transparent;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    &.current {
      background: rgba(0, 255, 209, 0.1);
      border-left-color: var(--player-primary);
    }

    &.dragging {
      opacity: 0.5;
      transform: scale(0.95);
    }

    &.drag-over {
      background: rgba(0, 255, 209, 0.2);
    }

    .drag-handle {
      color: var(--player-text-secondary);
      cursor: grab;
      opacity: 0;
      transition: var(--player-transition);

      &:active {
        cursor: grabbing;
      }
    }

    &:hover .drag-handle {
      opacity: 1;
    }

    .queue-item-artwork {
      position: relative;
      width: 48px;
      height: 48px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }

      .artwork-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--player-primary), var(--player-secondary));
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
      }

      .playing-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        border-radius: 4px;
        padding: 4px;

        .playing-bars {
          display: flex;
          gap: 2px;
          align-items: end;

          .bar {
            width: 3px;
            height: 12px;
            background: var(--player-primary);
            border-radius: 1px;
          }
        }
      }
    }

    .queue-item-info {
      flex: 1;
      min-width: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .song-details {
        min-width: 0;
        flex: 1;

        .song-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--player-text-primary);
          margin: 0 0 4px 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .song-artist {
          font-size: 12px;
          color: var(--player-text-secondary);
          margin: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .song-duration {
        font-size: 12px;
        color: var(--player-text-secondary);
        font-variant-numeric: tabular-nums;
      }
    }

    .queue-item-actions {
      display: flex;
      gap: 8px;
      opacity: 0;
      transition: var(--player-transition);

      .action-btn {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--player-text-primary);
        cursor: pointer;
        transition: var(--player-transition);
        font-size: 12px;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        &.remove-btn:hover {
          background: rgba(255, 107, 107, 0.2);
          color: var(--player-accent);
        }
      }
    }

    &:hover .queue-item-actions {
      opacity: 1;
    }
  }

  .queue-actions {
    padding: 16px 24px;
    border-top: 1px solid var(--player-border);
    display: flex;
    gap: 12px;

    .queue-action-btn {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 8px;
      padding: 12px;
      color: var(--player-text-primary);
      cursor: pointer;
      transition: var(--player-transition);
      font-size: 14px;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &.clear-btn:hover {
        background: rgba(255, 107, 107, 0.2);
        color: var(--player-accent);
      }
    }
  }
}

// Equalizer Styles
.equalizer-panel {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 600px;
  max-width: 90vw;
  background: rgba(15, 15, 15, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid var(--player-border);
  border-radius: 16px 16px 0 0;
  z-index: 10000;

  .equalizer-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--player-border);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      h3 {
        margin: 0;
        color: var(--player-text-primary);
        font-size: 18px;
        font-weight: 600;
      }

      .toggle-btn {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 16px;
        padding: 6px 12px;
        color: var(--player-text-primary);
        cursor: pointer;
        transition: var(--player-transition);
        font-size: 12px;
        font-weight: 600;

        &.enabled {
          background: var(--player-primary);
          color: #000000;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.2);

          &.enabled {
            background: var(--player-primary);
          }
        }
      }
    }

    .header-right {
      display: flex;
      gap: 8px;

      .action-btn {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--player-text-primary);
        cursor: pointer;
        transition: var(--player-transition);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }

  .visualizer-container {
    padding: 16px 24px;
    display: flex;
    justify-content: center;

    .visualizer-canvas {
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.3);
    }
  }

  .eq-presets {
    padding: 0 24px 16px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;

    .preset-btn {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 16px;
      padding: 8px 16px;
      color: var(--player-text-primary);
      cursor: pointer;
      transition: var(--player-transition);
      font-size: 12px;
      text-transform: capitalize;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &.active {
        background: var(--player-primary);
        color: #000000;
      }
    }
  }

  .eq-sliders {
    padding: 16px 24px 24px;
    display: flex;
    gap: 16px;
    justify-content: center;
    align-items: end;

    .eq-slider-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .frequency-label {
        font-size: 10px;
        color: var(--player-text-secondary);
        font-weight: 500;
        text-align: center;
        min-width: 32px;
      }

      .eq-slider {
        writing-mode: bt-lr; /* IE */
        -webkit-appearance: slider-vertical; /* WebKit */
        width: 6px;
        height: 120px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        outline: none;
        cursor: pointer;

        &::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 16px;
          height: 16px;
          background: var(--player-primary);
          border-radius: 50%;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0, 255, 209, 0.4);
        }

        &::-moz-range-thumb {
          width: 16px;
          height: 16px;
          background: var(--player-primary);
          border-radius: 50%;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 8px rgba(0, 255, 209, 0.4);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .gain-value {
        font-size: 10px;
        color: var(--player-text-secondary);
        font-variant-numeric: tabular-nums;
        text-align: center;
        min-width: 40px;
      }
    }
  }
}

// Mobile Responsive Styles
@media (max-width: 768px) {
  .modern-audio-player {
    border-radius: 12px;

    &.expanded {
      border-radius: 0;
    }

    .player-content {
      padding: 12px;
      gap: 12px;

      .expanded & {
        padding: 16px;
      }
    }

    .action-btn {
      width: 36px;
      height: 36px;
      font-size: 14px;
    }
  }

  .song-info {
    gap: 12px;

    &.expanded {
      gap: 20px;
    }

    .song-artwork {
      .collapsed & {
        width: 50px;
        height: 50px;
      }

      .expanded & {
        width: 240px;
        height: 240px;
      }
    }

    .song-details .song-text {
      .song-title {
        font-size: 14px;

        .expanded & {
          font-size: 20px;
        }
      }

      .song-artist {
        font-size: 12px;

        .expanded & {
          font-size: 14px;
        }
      }
    }
  }

  .player-controls {
    gap: 12px;

    .control-btn {
      width: 40px;
      height: 40px;
      font-size: 16px;
    }

    .play-pause-btn {
      width: 56px;
      height: 56px;
      font-size: 20px;

      .expanded & {
        width: 72px;
        height: 72px;
        font-size: 28px;
      }
    }

    .control-btn-small {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }
  }

  .volume-control {
    .volume-btn {
      width: 36px;
      height: 36px;
      font-size: 14px;
    }
  }

  .playlist-queue {
    width: 100vw;
    right: 0;

    .queue-header {
      padding: 16px;
    }

    .queue-item {
      padding: 8px 16px;
      gap: 8px;

      .queue-item-artwork {
        width: 40px;
        height: 40px;
      }
    }
  }

  .equalizer-panel {
    width: 100vw;
    border-radius: 16px 16px 0 0;

    .eq-sliders {
      gap: 12px;
      padding: 12px 16px 20px;

      .eq-slider-container {
        .eq-slider {
          height: 100px;
        }
      }
    }

    .eq-presets {
      padding: 0 16px 12px;
      gap: 6px;

      .preset-btn {
        padding: 6px 12px;
        font-size: 11px;
      }
    }
  }
}

@media (max-width: 480px) {
  .song-info.expanded .song-artwork {
    width: 200px;
    height: 200px;
  }

  .equalizer-panel .eq-sliders {
    gap: 8px;

    .eq-slider-container {
      .eq-slider {
        height: 80px;
      }

      .frequency-label,
      .gain-value {
        font-size: 9px;
      }
    }
  }
}
