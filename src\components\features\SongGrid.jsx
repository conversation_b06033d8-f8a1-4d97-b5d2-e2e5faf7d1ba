import { useState, useEffect } from 'react';
import SongCard from './SongCard';
import LoadingSpinner from '../common/LoadingSpinner';
import { motion } from 'framer-motion';
import { usePlaylist } from '../../context/PlaylistContext';
import { useAuth } from '../../context/AuthContext';

const SongGrid = ({
  songs,
  isLoading,
  title,
  showLoadMore = false,
  onLoadMore,
  mobileColumns = 3 // Default to 3 columns on mobile, can be set to 2 for specific sections
}) => {
  const [favorites, setFavorites] = useState([]);
  const { addToLikedSongs, removeFromLikedSongs } = usePlaylist();
  const { currentUser } = useAuth();

  useEffect(() => {
    // Load favorites from localStorage
    const storedFavorites = localStorage.getItem('favorites');
    if (storedFavorites) {
      setFavorites(JSON.parse(storedFavorites));
    }
  }, []);

  const handleToggleFavorite = async (song) => {
    const isFavorite = favorites.some(fav => fav.id === song.id);
    let updatedFavorites;

    if (isFavorite) {
      updatedFavorites = favorites.filter(fav => fav.id !== song.id);

      // Remove from Liked Songs playlist if user is logged in
      if (currentUser) {
        try {
          await removeFromLikedSongs(song.id);
        } catch (err) {
          console.error("Error removing from Liked Songs playlist:", err);
          // Continue with local favorites even if playlist update fails
        }
      }
    } else {
      updatedFavorites = [...favorites, song];

      // Add to Liked Songs playlist if user is logged in
      if (currentUser) {
        try {
          await addToLikedSongs(song);
        } catch (err) {
          console.error("Error adding to Liked Songs playlist:", err);
          // Continue with local favorites even if playlist update fails
        }
      }
    }

    // Update local storage regardless of playlist operations
    setFavorites(updatedFavorites);
    localStorage.setItem('favorites', JSON.stringify(updatedFavorites));
  };

  if (isLoading) {
    return <LoadingSpinner type="songGrid" count={mobileColumns === 2 ? 4 : 6} />;
  }

  if (!songs || songs.length === 0) {
    return (
      <div className="text-center py-5">
        <p className="mb-0">No songs found.</p>
      </div>
    );
  }

  return (
    <div className="mb-4">
      {title && <h5 className="mb-3 fw-bold">{title}</h5>}

      <motion.div
        className={`song-grid-container ${mobileColumns === 2 ? 'mobile-two-columns' : ''}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.05 }}
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
          gap: window.innerWidth < 576 ? '0.5rem' : '1rem'
        }}
      >
        {songs.map(song => (
          <SongCard
            key={song.id}
            song={song}
            isFavorite={favorites.some(fav => fav.id === song.id)}
            onToggleFavorite={handleToggleFavorite}
          />
        ))}
      </motion.div>

      {showLoadMore && (
        <div className="text-center mt-3">
          <motion.button
            className="btn btn-sm rounded-pill px-3"
            onClick={onLoadMore}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              backgroundColor: 'rgba(157, 78, 221, 0.15)',
              color: 'var(--dark-accent)',
              border: '1px solid rgba(157, 78, 221, 0.2)',
              fontSize: '0.85rem'
            }}
          >
            Load More
          </motion.button>
        </div>
      )}
    </div>
  );
};

export default SongGrid;
