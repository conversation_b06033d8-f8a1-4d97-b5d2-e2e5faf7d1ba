import React from 'react';
import { Row, Col, Container } from 'react-bootstrap';
import ShimmerEffect from './ShimmerEffect';

/**
 * SongCardShimmer - Shimmer loader for song cards
 */
export const SongCardShimmer = ({ count = 6 }) => {
  return (
    <div className="song-grid-container">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="song-card h-100 cursor-pointer">
          <div className="position-relative rounded overflow-hidden" style={{ aspectRatio: '1/1' }}>
            <ShimmerEffect type="image" height="100%" />
          </div>
          <div className="p-2">
            <ShimmerEffect type="text" height="1.2rem" className="mb-2" width="80%" />
            <ShimmerEffect type="text" height="0.9rem" width="60%" />
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * SongListShimmer - Shimmer loader for song list items
 */
export const SongListShimmer = ({ count = 5 }) => {
  return (
    <div className="song-list-shimmer">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="d-flex align-items-center p-2 mb-2" style={{ height: '60px' }}>
          <ShimmerEffect type="circle" width="40px" height="40px" className="me-3" />
          <div className="flex-grow-1">
            <ShimmerEffect type="text" height="1rem" className="mb-2" width="70%" />
            <ShimmerEffect type="text" height="0.8rem" width="50%" />
          </div>
          <ShimmerEffect type="circle" width="32px" height="32px" className="ms-2" />
        </div>
      ))}
    </div>
  );
};

/**
 * PlaylistShimmer - Shimmer loader for playlist items
 */
export const PlaylistShimmer = ({ count = 4 }) => {
  return (
    <Row className="g-4">
      {Array.from({ length: count }).map((_, index) => (
        <Col key={index} xs={6} md={4} lg={3}>
          <div className="card bg-dark h-100">
            <ShimmerEffect type="image" height="160px" />
            <div className="card-body">
              <ShimmerEffect type="text" height="1.2rem" className="mb-2" width="80%" />
              <ShimmerEffect type="text" height="0.9rem" width="60%" className="mb-3" />
              <div className="d-flex justify-content-between">
                <ShimmerEffect type="text" height="0.8rem" width="30%" />
                <ShimmerEffect type="circle" width="30px" height="30px" />
              </div>
            </div>
          </div>
        </Col>
      ))}
    </Row>
  );
};

/**
 * SongDetailShimmer - Shimmer loader for song detail page
 */
export const SongDetailShimmer = () => {
  return (
    <Container>
      <Row className="mb-4">
        <Col md={4} className="mb-4 mb-md-0">
          <ShimmerEffect type="image" height="300px" className="rounded" />
        </Col>
        <Col md={8}>
          <ShimmerEffect type="text" height="2rem" className="mb-3" width="70%" />
          <ShimmerEffect type="text" height="1.2rem" className="mb-4" width="50%" />
          
          <div className="d-flex mb-4">
            <ShimmerEffect type="button" width="120px" height="40px" className="me-2" />
            <ShimmerEffect type="button" width="120px" height="40px" className="me-2" />
            <ShimmerEffect type="circle" width="40px" height="40px" />
          </div>
          
          <div className="mb-3">
            <ShimmerEffect type="text" height="1rem" className="mb-2" width="100%" />
            <ShimmerEffect type="text" height="1rem" className="mb-2" width="100%" />
            <ShimmerEffect type="text" height="1rem" className="mb-2" width="80%" />
          </div>
          
          <div className="d-flex mt-4">
            <ShimmerEffect type="circle" width="30px" height="30px" className="me-3" />
            <ShimmerEffect type="text" height="1rem" width="120px" />
          </div>
        </Col>
      </Row>
      
      <div className="mt-5">
        <ShimmerEffect type="text" height="1.5rem" className="mb-4" width="200px" />
        <SongListShimmer count={3} />
      </div>
    </Container>
  );
};

/**
 * SearchResultsShimmer - Shimmer loader for search results
 */
export const SearchResultsShimmer = () => {
  return (
    <div className="search-results-shimmer">
      <ShimmerEffect type="text" height="1.5rem" className="mb-4" width="200px" />
      <SongListShimmer count={3} />
    </div>
  );
};

/**
 * SuggestionShimmer - Shimmer loader for search suggestions
 */
export const SuggestionShimmer = ({ count = 3 }) => {
  return (
    <div className="p-3">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="d-flex align-items-center mb-2 p-2">
          <ShimmerEffect type="circle" width="30px" height="30px" className="me-2" />
          <ShimmerEffect type="text" height="1rem" width="80%" />
        </div>
      ))}
    </div>
  );
};

export default {
  SongCardShimmer,
  SongListShimmer,
  PlaylistShimmer,
  SongDetailShimmer,
  SearchResultsShimmer,
  SuggestionShimmer
};
