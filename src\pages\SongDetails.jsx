import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { getSongById } from '../services/api';
import RelatedSongs from '../components/features/RelatedSongs';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { FaHeart, FaRegHeart, FaCompactDisc, FaCalendarDay, FaLanguage, FaPlay, FaPause, FaHeadphones, FaPlus, FaListUl } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useSong } from '../context/SongContext';
import { usePlaylist } from '../context/PlaylistContext';
import { useAuth } from '../context/AuthContext';
import AddToPlaylistModal from '../components/playlists/AddToPlaylistModal';
import { decodeHtmlEntities, formatPlayCount } from '../utils/textUtils';

const SongDetails = () => {
  const { id } = useParams();
  const [song, setSong] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showAddToPlaylistModal, setShowAddToPlaylistModal] = useState(false);
  const { playSong, pauseSong, isPlaying, currentSong } = useSong();
  const { addToLikedSongs, removeFromLikedSongs } = usePlaylist();
  const { currentUser } = useAuth();

  // Check if this song is currently playing
  const isCurrentSong = currentSong && song && currentSong.id === song.id;

  useEffect(() => {
    const fetchSongDetails = async () => {
      try {
        setIsLoading(true);
        const response = await getSongById(id);

        if (response.success && response.data && response.data.length > 0) {
          const songData = response.data[0];
          setSong(songData);

          // Check if song is in favorites
          const storedFavorites = localStorage.getItem('favorites');
          if (storedFavorites) {
            const favorites = JSON.parse(storedFavorites);
            setIsFavorite(favorites.some(fav => fav.id === songData.id));
          }

          // We don't auto-set the current song anymore to prevent unnecessary reloads
          // This will be handled by the play button click instead
        } else {
          setError('Song not found');
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching song details:', err);
        setError('Failed to load song details. Please try again later.');
        setIsLoading(false);
      }
    };

    if (id) {
      fetchSongDetails();
    }
  }, [id]); // Remove playSong and currentSong from dependencies

  const handleToggleFavorite = async () => {
    const storedFavorites = localStorage.getItem('favorites');
    let favorites = storedFavorites ? JSON.parse(storedFavorites) : [];

    // Update local storage favorites
    if (isFavorite) {
      favorites = favorites.filter(fav => fav.id !== song.id);
    } else {
      favorites.push(song);
    }
    localStorage.setItem('favorites', JSON.stringify(favorites));

    // Update UI state
    setIsFavorite(!isFavorite);

    // Update the Liked Songs playlist if user is logged in
    if (currentUser) {
      try {
        if (isFavorite) {
          // If it was favorited and now being unfavorited, remove from playlist
          await removeFromLikedSongs(song.id);
          console.log("Song removed from Liked Songs playlist");
        } else {
          // If it was not favorited and now being favorited, add to playlist
          await addToLikedSongs(song);
          console.log("Song added to Liked Songs playlist");
        }
      } catch (err) {
        console.error("Error updating Liked Songs playlist:", err);
        // Continue even if playlist update fails
      }
    }
  };

  const handlePlayPause = (e) => {
    // Prevent default behavior to avoid page reload
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (isCurrentSong && isPlaying) {
      console.log('Pausing current song');
      pauseSong();
    } else if (song) {
      console.log('Playing song:', song.name);
      playSong(song);
    }
  };

  const getHighQualityImage = () => {
    if (!song || !song.image || song.image.length === 0) {
      return 'https://via.placeholder.com/300';
    }

    const highQualityImage = song.image.find(img => img.quality === '500x500');
    if (highQualityImage) return highQualityImage.url;

    return song.image[song.image.length - 1].url;
  };

  if (isLoading) {
    return <LoadingSpinner type="songDetail" fullPage />;
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        {error}
      </div>
    );
  }

  if (!song) {
    return (
      <div className="text-center py-5">
        <p className="mb-3">Song not found</p>
        <Link to="/" className="btn btn-primary">Back to Home</Link>
      </div>
    );
  }

  return (
    <div className="song-details-page py-4">
      <div className="container py-2 py-md-4">
        <div className="row g-4">
          <div className="col-md-5 col-lg-4 mb-4 mb-md-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="position-relative"
            >
              <img
                src={getHighQualityImage()}
                alt={song.name}
                className="img-fluid rounded-3 shadow mx-auto d-block"
                style={{
                  width: '100%',
                  maxWidth: '300px',
                  objectFit: 'cover',
                  aspectRatio: '1/1'
                }}
              />

              {/* Overlay gradient with theme colors */}
              <div
                className="position-absolute bottom-0 start-0 end-0 rounded-bottom-3"
                style={{
                  background: 'linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%)',
                  height: '50%'
                }}
              ></div>

              {/* Favorite button */}
              <button
                className="btn btn-light position-absolute top-0 end-0 m-2 m-sm-3 rounded-circle shadow-sm"
                onClick={handleToggleFavorite}
                aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
                style={{
                  width: '36px',
                  height: '36px',
                  padding: '0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {isFavorite ? (
                  <FaHeart className="text-danger" size={16} />
                ) : (
                  <FaRegHeart size={16} />
                )}
              </button>
            </motion.div>
          </div>

          <div className="col-md-7 col-lg-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="h-100 d-flex flex-column"
            >
              <div className="mb-4">
                <h1 className="mb-2 fs-2 fs-md-1" style={{ color: '#ffffff' }}>{decodeHtmlEntities(song.name)}</h1>
                <p className="fs-6" style={{ color: '#e0e0e0' }}>
                  {song.artists && song.artists.primary && song.artists.primary.map(artist => decodeHtmlEntities(artist.name)).join(', ')}
                </p>
              </div>

              {/* Album, Released, Language, Listens info */}
              <div className="card shadow-sm mb-4 p-2 p-sm-3 rounded-3" style={{ backgroundColor: 'rgba(15, 15, 15, 0.5)' }}>
                <div className="row g-2 g-sm-3">
                  <div className="col-6 col-md-3">
                    <div className="d-flex align-items-center">
                      <div className="rounded-circle p-1 p-sm-2 me-2 me-sm-3 d-flex align-items-center justify-content-center"
                        style={{
                          backgroundColor: 'rgba(157, 78, 221, 0.15)',
                          width: '35px',
                          height: '35px'
                        }}
                      >
                        <FaCompactDisc style={{ color: 'var(--dark-primary)', fontSize: '16px' }} />
                      </div>
                      <div>
                        <small className="d-block" style={{ color: 'var(--dark-primary)' }}>Album</small>
                        <span className="fw-medium small text-truncate d-inline-block" style={{ maxWidth: '100px' }}>
                          {song.album && song.album.name ? decodeHtmlEntities(song.album.name) : 'Single'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="col-6 col-md-3">
                    <div className="d-flex align-items-center">
                      <div className="rounded-circle p-1 p-sm-2 me-2 me-sm-3 d-flex align-items-center justify-content-center"
                        style={{
                          backgroundColor: 'rgba(0, 255, 209, 0.15)',
                          width: '35px',
                          height: '35px'
                        }}
                      >
                        <FaCalendarDay style={{ color: '#00FFD1', fontSize: '16px' }} />
                      </div>
                      <div>
                        <small className="d-block" style={{ color: '#00FFD1' }}>Released</small>
                        <span className="fw-medium small">{song.year || 'Unknown'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="col-6 col-md-3">
                    <div className="d-flex align-items-center">
                      <div className="rounded-circle p-1 p-sm-2 me-2 me-sm-3 d-flex align-items-center justify-content-center"
                        style={{
                          backgroundColor: 'rgba(255, 107, 107, 0.15)',
                          width: '35px',
                          height: '35px'
                        }}
                      >
                        <FaLanguage style={{ color: '#FF6B6B', fontSize: '16px' }} />
                      </div>
                      <div>
                        <small className="d-block" style={{ color: '#FF6B6B' }}>Language</small>
                        <span className="fw-medium small">{song.language || 'Unknown'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="col-6 col-md-3">
                    <div className="d-flex align-items-center">
                      <div className="rounded-circle p-1 p-sm-2 me-2 me-sm-3 d-flex align-items-center justify-content-center"
                        style={{
                          backgroundColor: 'rgba(32, 201, 151, 0.15)',
                          width: '35px',
                          height: '35px'
                        }}
                      >
                        <FaHeadphones style={{ color: '#20C997', fontSize: '16px' }} />
                      </div>
                      <div>
                        <small className="d-block" style={{ color: '#20C997' }}>Listens</small>
                        <span className="fw-medium small">{formatPlayCount(song.playCount)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Play/Pause and Add to Playlist Buttons */}
              <div className="d-flex justify-content-center gap-3 mb-4">
                <button
                  className={`btn ${isCurrentSong && isPlaying ? 'btn-danger' : 'btn-primary'} btn-md btn-lg-lg rounded-pill px-3 px-md-4 d-flex align-items-center justify-content-center`}
                  onClick={(e) => handlePlayPause(e)}
                  style={{
                    width: 'auto',
                    minWidth: '120px',
                    height: '42px',
                    color: isCurrentSong && isPlaying ? '#fff' : '#2c3e50',
                    fontWeight: '600'
                  }}
                >
                  {isCurrentSong && isPlaying ? (
                    <>
                      <FaPause className="me-2" /> <span>Pause</span>
                    </>
                  ) : (
                    <>
                      <FaPlay className="me-2" /> <span>Play</span>
                    </>
                  )}
                </button>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="btn btn-outline-light rounded-pill px-3 px-md-4 d-flex align-items-center justify-content-center"
                  onClick={() => setShowAddToPlaylistModal(true)}
                  style={{
                    width: 'auto',
                    minWidth: '160px',
                    height: '42px',
                    borderColor: 'rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <FaListUl className="me-2" /> <span>Add to Playlist</span>
                </motion.button>
              </div>

              <div className="mt-auto">
                <RelatedSongs songId={id} />
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Add to Playlist Modal */}
      <AddToPlaylistModal
        show={showAddToPlaylistModal}
        onHide={() => setShowAddToPlaylistModal(false)}
        song={song ? {
          id: song.id,
          name: song.name || 'Untitled Song',
          image: song.image ? [...song.image] : [],
          artists: song.artists ? {
            primary: song.artists.primary ? [...song.artists.primary] : []
          } : { primary: [] }
        } : null}
      />
    </div>
  );
};

export default SongDetails;
