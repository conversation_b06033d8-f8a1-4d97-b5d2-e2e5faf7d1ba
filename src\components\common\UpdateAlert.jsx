import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaSync, FaTimes } from 'react-icons/fa';
import { APP_VERSION, BUILD_TIMESTAMP, checkForUpdates, forceReload } from '../../utils/version';

/**
 * UpdateAlert component that shows a beautiful alert in the middle of the page
 * asking the user to click to update the application.
 */
const UpdateAlert = () => {
  const [showAlert, setShowAlert] = useState(false);

  useEffect(() => {
    // Check for updates when component mounts
    const checkVersionAndUpdate = async () => {
      try {
        // Check if we need to update
        const updateNeeded = await checkForUpdates();

        // Get the last time we showed an update alert
        const lastUpdateAlertTime = localStorage.getItem('lastUpdateAlertTime');
        const currentTime = new Date().getTime();

        // Show alert if:
        // 1. An update is needed, OR
        // 2. It's been more than 24 hours since we last showed the alert
        const timeCondition = !lastUpdateAlertTime ||
          (currentTime - parseInt(lastUpdateAlertTime || '0')) > 24 * 60 * 60 * 1000;

        const shouldShowAlert = updateNeeded || timeCondition;

        if (shouldShowAlert) {
          // Show the alert after a short delay when the page loads
          const timer = setTimeout(() => {
            setShowAlert(true);
          }, 1000);

          return () => clearTimeout(timer);
        }
      } catch (error) {
        console.error('Error checking for updates:', error);
      }
    };

    checkVersionAndUpdate();

    // Set up periodic check for updates (every 30 minutes)
    const intervalId = setInterval(async () => {
      const updateNeeded = await checkForUpdates();
      if (updateNeeded) {
        setShowAlert(true);
      }
    }, 30 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, []);

  const handleUpdate = () => {
    // Mark that the user has seen this version and update alert time
    localStorage.setItem('lastSeenVersion', APP_VERSION);
    localStorage.setItem('buildTimestamp', BUILD_TIMESTAMP);
    localStorage.setItem('lastUpdateAlertTime', new Date().getTime().toString());

    // Hide the alert
    setShowAlert(false);

    // Force reload with cache clearing
    setTimeout(() => {
      forceReload();
    }, 500);
  };

  const handleClose = () => {
    // Mark that the user has seen this version and update alert time
    localStorage.setItem('lastSeenVersion', APP_VERSION);
    localStorage.setItem('buildTimestamp', BUILD_TIMESTAMP);
    localStorage.setItem('lastUpdateAlertTime', new Date().getTime().toString());

    // Hide the alert
    setShowAlert(false);
  };

  // For testing purposes - this can be triggered from the console
  // by calling window.forceUpdateAlert()
  useEffect(() => {
    window.forceUpdateAlert = () => {
      localStorage.removeItem('lastSeenVersion');
      localStorage.removeItem('buildTimestamp');
      localStorage.removeItem('lastUpdateAlertTime');
      localStorage.removeItem('lastUpdateCheckTime');
      setShowAlert(true);
      console.log('Update alert has been forced to show');
    };

    // Also expose a function to force reload without showing the alert
    window.forceAppReload = () => {
      console.log('Forcing application reload with cache clearing');
      forceReload();
    };

    return () => {
      delete window.forceUpdateAlert;
      delete window.forceAppReload;
    };
  }, []);

  return (
    <AnimatePresence>
      {showAlert && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3 }}
          className="update-alert-overlay"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            backdropFilter: 'blur(5px)'
          }}
        >
          <motion.div
            className="update-alert-container"
            style={{
              backgroundColor: '#1a1a1a',
              borderRadius: '12px',
              padding: '2rem',
              maxWidth: '400px',
              width: '90%',
              boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 255, 209, 0.2)',
              border: '1px solid rgba(0, 255, 209, 0.2)',
              position: 'relative'
            }}
          >
            <button
              onClick={handleClose}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: 'none',
                border: 'none',
                color: '#aaa',
                fontSize: '16px',
                cursor: 'pointer',
                padding: '5px',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '30px',
                height: '30px'
              }}
              className="hover-effect"
            >
              <FaTimes />
            </button>

            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '1.5rem'
              }}
            >
              <div
                style={{
                  backgroundColor: 'rgba(0, 255, 209, 0.15)',
                  borderRadius: '50%',
                  width: '60px',
                  height: '60px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <FaSync size={24} style={{ color: '#00FFD1' }} />
              </div>
            </div>

            <h3
              style={{
                textAlign: 'center',
                color: '#fff',
                marginBottom: '1rem',
                fontSize: '1.5rem'
              }}
            >
              Update Available
            </h3>

            <p
              style={{
                textAlign: 'center',
                color: '#ccc',
                marginBottom: '1.5rem',
                fontSize: '0.95rem',
                lineHeight: '1.5'
              }}
            >
              A new version of MusicMania is available. Click the button below to update in less than 1 second.
            </p>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleUpdate}
              style={{
                width: '100%',
                padding: '0.75rem',
                borderRadius: '8px',
                border: 'none',
                backgroundColor: '#00FFD1',
                color: '#0f0f0f',
                fontWeight: '600',
                fontSize: '1rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <FaSync className="me-2" /> Update Now
            </motion.button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default UpdateAlert;
