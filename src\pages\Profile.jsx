import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Button, ListGroup, Badge, Alert } from 'react-bootstrap';
import { motion } from 'framer-motion';
import { FaUser, FaSignOutAlt, FaMusic, FaHeart, FaHistory, FaEdit, FaCalendarAlt, FaHeadphones, FaPlayCircle } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { usePlaylist } from '../context/PlaylistContext';
import { useSong } from '../context/SongContext';
import LoadingSpinner from '../components/common/LoadingSpinner';
import SongList from '../components/features/SongList';

const Profile = () => {
  const { currentUser, logout, updateUserProfile } = useAuth();
  const { playlists } = usePlaylist();
  const { previousSongs } = useSong();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [likedSongs, setLikedSongs] = useState([]);
  const [recentlyPlayed, setRecentlyPlayed] = useState([]);
  const [joinDate, setJoinDate] = useState('');
  const [stats, setStats] = useState({
    totalPlaylists: 0,
    totalLikedSongs: 0,
    totalListened: 0
  });

  useEffect(() => {
    // Redirect if not logged in
    if (!currentUser) {
      navigate('/');
      return;
    }

    const loadUserData = async () => {
      try {
        setIsLoading(true);

        // Get liked songs playlist
        const likedPlaylist = playlists.find(p => p.name === 'Liked Songs');
        if (likedPlaylist && likedPlaylist.songs) {
          setLikedSongs(likedPlaylist.songs.slice(0, 5)); // Show only the first 5
        }

        // Get recently played songs
        setRecentlyPlayed(previousSongs.slice(0, 5));

        // Calculate join date (metadata from Firebase user)
        if (currentUser.metadata && currentUser.metadata.creationTime) {
          const creationDate = new Date(currentUser.metadata.creationTime);
          setJoinDate(creationDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }));
        }

        // Calculate stats
        setStats({
          totalPlaylists: playlists.length,
          totalLikedSongs: likedPlaylist ? (likedPlaylist.songs ? likedPlaylist.songs.length : 0) : 0,
          totalListened: previousSongs.length
        });

        setIsLoading(false);
      } catch (err) {
        console.error('Error loading profile data:', err);
        setError('Failed to load profile data');
        setIsLoading(false);
      }
    };

    loadUserData();
  }, [currentUser, navigate, playlists, previousSongs]);

  const handleLogout = async () => {
    try {
      setError('');
      await logout();
      navigate('/');
    } catch (err) {
      console.error('Failed to log out:', err);
      setError('Failed to log out');
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <Container>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Row className="mb-4 profile-header">
          <Col>
            <h1 className="display-5 mb-0" style={{ color: '#ffffff' }}>
              <FaUser className="me-3" style={{ color: 'var(--dark-accent)' }} />
              Profile
            </h1>
            <p className="mt-2" style={{ color: '#e0e0e0' }}>Manage your account and view your music stats</p>
          </Col>
        </Row>

        {error && (
          <Alert variant="danger" className="mb-4">
            {error}
          </Alert>
        )}

        <Row className="g-4">
          {/* User Info Card */}
          <Col lg={4} md={6} className="mb-4 profile-card">
            <motion.div
              whileHover={{ y: -5 }}
              transition={{ duration: 0.2 }}
            >
              <Card className="h-100 border-0 shadow-sm" style={{
                backgroundColor: 'rgba(25, 25, 25, 0.6)',
                backdropFilter: 'blur(10px)',
                borderRadius: '1rem'
              }}>
                <Card.Body className="p-4">
                  <div className="text-center mb-4">
                    <div className="user-avatar mb-3" style={{
                      width: '100px',
                      height: '100px',
                      borderRadius: '50%',
                      backgroundColor: 'var(--dark-accent)',
                      color: '#0f0f0f',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '2.5rem',
                      margin: '0 auto'
                    }}>
                      {currentUser.displayName ? currentUser.displayName.charAt(0).toUpperCase() :
                       currentUser.email.charAt(0).toUpperCase()}
                    </div>
                    <h3 className="mb-1" style={{ color: '#ffffff' }}>{currentUser.displayName || 'Music Lover'}</h3>
                    <p style={{ color: '#e0e0e0' }} className="mb-0">{currentUser.email}</p>
                    {joinDate && (
                      <div className="d-flex align-items-center justify-content-center mt-2 small" style={{ color: '#e0e0e0' }}>
                        <FaCalendarAlt className="me-1" style={{ color: 'var(--dark-accent)' }} />
                        <span>Joined {joinDate}</span>
                      </div>
                    )}
                  </div>

                  <ListGroup variant="flush" className="mb-4 profile-stats">
                    <ListGroup.Item className="bg-transparent border-secondary d-flex justify-content-between align-items-center">
                      <div className="d-flex align-items-center">
                        <FaMusic className="me-2" style={{ color: 'var(--dark-accent)' }} />
                        <span style={{ color: '#ffffff' }}>Playlists</span>
                      </div>
                      <Badge bg="dark" pill style={{ backgroundColor: 'rgba(0, 255, 209, 0.2)', color: '#ffffff', border: '1px solid rgba(0, 255, 209, 0.3)' }}>
                        {stats.totalPlaylists}
                      </Badge>
                    </ListGroup.Item>
                    <ListGroup.Item className="bg-transparent border-secondary d-flex justify-content-between align-items-center">
                      <div className="d-flex align-items-center">
                        <FaHeart className="me-2" style={{ color: 'var(--dark-accent)' }} />
                        <span style={{ color: '#ffffff' }}>Liked Songs</span>
                      </div>
                      <Badge bg="dark" pill style={{ backgroundColor: 'rgba(0, 255, 209, 0.2)', color: '#ffffff', border: '1px solid rgba(0, 255, 209, 0.3)' }}>
                        {stats.totalLikedSongs}
                      </Badge>
                    </ListGroup.Item>
                    <ListGroup.Item className="bg-transparent border-secondary d-flex justify-content-between align-items-center">
                      <div className="d-flex align-items-center">
                        <FaHeadphones className="me-2" style={{ color: 'var(--dark-accent)' }} />
                        <span style={{ color: '#ffffff' }}>Songs Played</span>
                      </div>
                      <Badge bg="dark" pill style={{ backgroundColor: 'rgba(0, 255, 209, 0.2)', color: '#ffffff', border: '1px solid rgba(0, 255, 209, 0.3)' }}>
                        {stats.totalListened}
                      </Badge>
                    </ListGroup.Item>
                  </ListGroup>

                  <div className="d-grid gap-2">
                    <Button
                      variant="outline-danger"
                      className="d-flex align-items-center justify-content-center"
                      onClick={handleLogout}
                    >
                      <FaSignOutAlt className="me-2" /> Sign Out
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </motion.div>
          </Col>

          {/* Recently Played */}
          <Col lg={8} md={6} className="mb-4">
            <Card className="h-100 border-0 shadow-sm" style={{
              backgroundColor: 'rgba(25, 25, 25, 0.6)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem'
            }}>
              <Card.Body className="p-4">
                <div className="d-flex align-items-center mb-4">
                  <FaHistory className="me-2" style={{ color: 'var(--dark-accent)', fontSize: '1.5rem' }} />
                  <h4 className="mb-0" style={{ color: '#ffffff' }}>Recently Played</h4>
                </div>

                {recentlyPlayed.length > 0 ? (
                  <SongList songs={recentlyPlayed} showIndex={true} />
                ) : (
                  <div className="text-center py-4">
                    <FaPlayCircle size={40} className="mb-3" style={{ color: 'var(--dark-accent)', opacity: 0.5 }} />
                    <p className="mb-0" style={{ color: '#ffffff' }}>You haven't played any songs yet.</p>
                    <p style={{ color: '#e0e0e0' }}>Start listening to see your history here!</p>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>

          {/* Liked Songs */}
          <Col lg={12} className="mb-4">
            <Card className="border-0 shadow-sm" style={{
              backgroundColor: 'rgba(25, 25, 25, 0.6)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem'
            }}>
              <Card.Body className="p-4">
                <div className="d-flex align-items-center justify-content-between mb-4">
                  <div className="d-flex align-items-center">
                    <FaHeart className="me-2" style={{ color: 'var(--dark-accent)', fontSize: '1.5rem' }} />
                    <h4 className="mb-0" style={{ color: '#ffffff' }}>Liked Songs</h4>
                  </div>

                  {likedSongs.length > 0 && (
                    <Button
                      variant="outline-light"
                      size="sm"
                      onClick={() => navigate('/playlists')}
                      style={{
                        borderColor: 'var(--dark-accent)',
                        color: '#ffffff',
                        backgroundColor: 'rgba(0, 255, 209, 0.1)'
                      }}
                    >
                      View All
                    </Button>
                  )}
                </div>

                {likedSongs.length > 0 ? (
                  <SongList songs={likedSongs} showIndex={true} />
                ) : (
                  <div className="text-center py-4">
                    <FaHeart size={40} className="mb-3" style={{ color: 'var(--dark-accent)', opacity: 0.5 }} />
                    <p className="mb-0" style={{ color: '#ffffff' }}>You haven't liked any songs yet.</p>
                    <p style={{ color: '#e0e0e0' }}>Like songs to add them to your collection!</p>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </motion.div>
    </Container>
  );
};

export default Profile;
