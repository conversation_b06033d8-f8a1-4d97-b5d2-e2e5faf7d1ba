// Audio utility functions for the modern audio player

/**
 * Format time in seconds to MM:SS or HH:MM:SS format
 * @param {number} time - Time in seconds
 * @returns {string} Formatted time string
 */
export const formatTime = (time) => {
  if (!time || isNaN(time)) return '0:00';
  
  const hours = Math.floor(time / 3600);
  const minutes = Math.floor((time % 3600) / 60);
  const seconds = Math.floor(time % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * Parse time string to seconds
 * @param {string} timeString - Time string in MM:SS or HH:MM:SS format
 * @returns {number} Time in seconds
 */
export const parseTime = (timeString) => {
  if (!timeString) return 0;
  
  const parts = timeString.split(':').map(Number);
  
  if (parts.length === 2) {
    // MM:SS format
    return parts[0] * 60 + parts[1];
  } else if (parts.length === 3) {
    // HH:MM:SS format
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  }
  
  return 0;
};

/**
 * Get audio URL from song object with fallback options
 * @param {Object} song - Song object
 * @returns {string} Audio URL
 */
export const getAudioUrl = (song) => {
  if (!song) return '';
  
  // Handle different possible audio URL formats
  if (song.audioUrl) return song.audioUrl;
  if (song.audio_url) return song.audio_url;
  if (song.url) return song.url;
  if (song.downloadUrl && song.downloadUrl[0]) return song.downloadUrl[0].url;
  if (song.downloadUrl && Array.isArray(song.downloadUrl)) {
    const highQuality = song.downloadUrl.find(url => url.quality === '320kbps') || 
                       song.downloadUrl.find(url => url.quality === '160kbps') ||
                       song.downloadUrl[0];
    return highQuality?.url || '';
  }
  
  return '';
};

/**
 * Get image URL from song object with fallback options
 * @param {Object} song - Song object
 * @returns {string} Image URL
 */
export const getImageUrl = (song) => {
  if (!song) return '';
  
  return song.image || song.thumbnail || song.albumArt || song.cover || '';
};

/**
 * Calculate buffer percentage
 * @param {HTMLAudioElement} audioElement - Audio element
 * @returns {number} Buffer percentage (0-100)
 */
export const getBufferPercentage = (audioElement) => {
  if (!audioElement || !audioElement.buffered || audioElement.buffered.length === 0) {
    return 0;
  }
  
  const bufferedEnd = audioElement.buffered.end(audioElement.buffered.length - 1);
  const duration = audioElement.duration;
  
  if (!duration || duration === 0) return 0;
  
  return (bufferedEnd / duration) * 100;
};

/**
 * Create audio context for advanced audio processing
 * @returns {AudioContext|null} Audio context or null if not supported
 */
export const createAudioContext = () => {
  try {
    const AudioContext = window.AudioContext || window.webkitAudioContext;
    return new AudioContext();
  } catch (error) {
    console.warn('AudioContext not supported:', error);
    return null;
  }
};

/**
 * Detect audio format support
 * @param {string} format - Audio format (mp3, ogg, wav, etc.)
 * @returns {boolean} Whether format is supported
 */
export const isAudioFormatSupported = (format) => {
  const audio = document.createElement('audio');
  
  const mimeTypes = {
    mp3: 'audio/mpeg',
    ogg: 'audio/ogg',
    wav: 'audio/wav',
    m4a: 'audio/mp4',
    aac: 'audio/aac',
    flac: 'audio/flac',
    webm: 'audio/webm'
  };
  
  const mimeType = mimeTypes[format.toLowerCase()];
  if (!mimeType) return false;
  
  return audio.canPlayType(mimeType) !== '';
};

/**
 * Crossfade between two audio elements
 * @param {HTMLAudioElement} currentAudio - Current audio element
 * @param {HTMLAudioElement} nextAudio - Next audio element
 * @param {number} duration - Crossfade duration in seconds
 * @returns {Promise} Promise that resolves when crossfade is complete
 */
export const crossfade = (currentAudio, nextAudio, duration = 3) => {
  return new Promise((resolve) => {
    if (!currentAudio || !nextAudio) {
      resolve();
      return;
    }
    
    const steps = 50;
    const stepDuration = (duration * 1000) / steps;
    let step = 0;
    
    const interval = setInterval(() => {
      const progress = step / steps;
      
      currentAudio.volume = Math.max(0, 1 - progress);
      nextAudio.volume = Math.min(1, progress);
      
      step++;
      
      if (step >= steps) {
        clearInterval(interval);
        currentAudio.pause();
        resolve();
      }
    }, stepDuration);
    
    // Start playing the next audio
    nextAudio.play().catch(console.error);
  });
};

/**
 * Apply fade in/out effect to audio element
 * @param {HTMLAudioElement} audio - Audio element
 * @param {string} type - 'in' or 'out'
 * @param {number} duration - Fade duration in seconds
 * @returns {Promise} Promise that resolves when fade is complete
 */
export const fade = (audio, type = 'in', duration = 1) => {
  return new Promise((resolve) => {
    if (!audio) {
      resolve();
      return;
    }
    
    const steps = 50;
    const stepDuration = (duration * 1000) / steps;
    let step = 0;
    
    const startVolume = type === 'in' ? 0 : audio.volume;
    const endVolume = type === 'in' ? audio.volume : 0;
    
    if (type === 'in') {
      audio.volume = 0;
    }
    
    const interval = setInterval(() => {
      const progress = step / steps;
      audio.volume = startVolume + (endVolume - startVolume) * progress;
      
      step++;
      
      if (step >= steps) {
        clearInterval(interval);
        audio.volume = endVolume;
        
        if (type === 'out') {
          audio.pause();
        }
        
        resolve();
      }
    }, stepDuration);
  });
};

/**
 * Normalize volume level (0-1) to decibels
 * @param {number} volume - Volume level (0-1)
 * @returns {number} Volume in decibels
 */
export const volumeToDecibels = (volume) => {
  if (volume <= 0) return -Infinity;
  return 20 * Math.log10(volume);
};

/**
 * Convert decibels to volume level (0-1)
 * @param {number} decibels - Volume in decibels
 * @returns {number} Volume level (0-1)
 */
export const decibelsToVolume = (decibels) => {
  if (decibels === -Infinity) return 0;
  return Math.pow(10, decibels / 20);
};

/**
 * Get audio metadata from file
 * @param {File} file - Audio file
 * @returns {Promise<Object>} Promise that resolves with metadata
 */
export const getAudioMetadata = (file) => {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    const url = URL.createObjectURL(file);
    
    audio.addEventListener('loadedmetadata', () => {
      const metadata = {
        duration: audio.duration,
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      };
      
      URL.revokeObjectURL(url);
      resolve(metadata);
    });
    
    audio.addEventListener('error', (error) => {
      URL.revokeObjectURL(url);
      reject(error);
    });
    
    audio.src = url;
  });
};
