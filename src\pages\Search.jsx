import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { searchSongs, searchAlbums, searchArtists } from '../services/api';
import SearchBar from '../components/common/SearchBar';
import SongGrid from '../components/features/SongGrid';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { FaSearch, FaMusic, FaUser, FaCompactDisc } from 'react-icons/fa';
import { useSong } from '../context/SongContext';
import { motion } from 'framer-motion';

const Search = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const query = searchParams.get('q') || '';

  const [songs, setSongs] = useState([]);
  const [albums, setAlbums] = useState([]);
  const [artists, setArtists] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('songs');

  const { currentSong } = useSong();

  useEffect(() => {
    const fetchSearchResults = async () => {
      if (!query) return;

      try {
        setIsLoading(true);
        setError(null);

        // Fetch songs
        const songsResponse = await searchSongs(query, 0, 20);
        if (songsResponse.success && songsResponse.data && songsResponse.data.results) {
          setSongs(songsResponse.data.results);
        }

        // Fetch albums
        const albumsResponse = await searchAlbums(query, 0, 20);
        if (albumsResponse.success && albumsResponse.data && albumsResponse.data.results) {
          setAlbums(albumsResponse.data.results);
        }

        // Fetch artists
        const artistsResponse = await searchArtists(query, 0, 20);
        if (artistsResponse.success && artistsResponse.data && artistsResponse.data.results) {
          setArtists(artistsResponse.data.results);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching search results:', err);
        setError('Failed to load search results. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchSearchResults();
  }, [query]);

  const handleSearch = (newQuery) => {
    setSearchParams({ q: newQuery });
  };



  const renderContent = () => {
    if (isLoading) {
      switch (activeTab) {
        case 'songs':
          return <LoadingSpinner type="songGrid" count={8} />;
        case 'albums':
          return <LoadingSpinner type="playlists" count={8} />;
        case 'artists':
          return <LoadingSpinner type="playlists" count={8} />;
        default:
          return <LoadingSpinner type="songGrid" count={8} />;
      }
    }

    if (error) {
      return (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      );
    }

    if (!query) {
      return (
        <div className="text-center py-5 empty-search">
          <div className="icon-container mb-3">
            <FaSearch size={48} className="text-muted" />
          </div>
          <p className="mb-0" style={{ color: '#ffffff' }}>Enter a search term to find songs, albums, and artists.</p>
        </div>
      );
    }

    if (activeTab === 'songs' && songs.length === 0) {
      return (
        <div className="text-center py-5">
          <p className="mb-0" style={{ color: '#ffffff' }}>No songs found for "{query}".</p>
        </div>
      );
    }

    if (activeTab === 'albums' && albums.length === 0) {
      return (
        <div className="text-center py-5">
          <p className="mb-0" style={{ color: '#ffffff' }}>No albums found for "{query}".</p>
        </div>
      );
    }

    if (activeTab === 'artists' && artists.length === 0) {
      return (
        <div className="text-center py-5">
          <p className="mb-0" style={{ color: '#ffffff' }}>No artists found for "{query}".</p>
        </div>
      );
    }

    switch (activeTab) {
      case 'songs':
        return <SongGrid
          songs={songs}
          isLoading={false}
        />;
      case 'albums':
        return (
          <div className="album-grid">
            <div className="row g-4">
              {albums.map(album => (
                <div key={album.id} className="col-6 col-md-4 col-lg-3">
                  <motion.div
                    className="album-card h-100 cursor-pointer"
                    whileHover={{ y: -5, transition: { duration: 0.3 } }}
                    style={{
                      backgroundColor: 'rgba(15, 15, 15, 0.5)',
                      borderRadius: '0.75rem',
                      overflow: 'hidden',
                      border: '1px solid rgba(255, 255, 255, 0.05)',
                      boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                  >
                    <div className="position-relative">
                      <div style={{
                        width: '100%',
                        paddingTop: '100%',
                        position: 'relative',
                        overflow: 'hidden',
                        borderBottom: '1px solid rgba(255, 255, 255, 0.05)'
                      }}>
                        <img
                          src={album.image && album.image.length > 0
                            ? (
                              // Try to get the highest quality image available
                              album.image.find(img => img.quality === '500x500')?.url ||
                              album.image.find(img => img.quality === '150x150')?.url ||
                              album.image[album.image.length - 1]?.url ||
                              album.image[0].url
                            )
                            : 'https://via.placeholder.com/500'
                          }
                          alt={album.name}
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            imageRendering: 'high-quality'
                          }}
                        />
                      </div>
                    </div>
                    <div className="p-3">
                      <h6 className="text-truncate mb-1 fw-bold" style={{ color: '#ffffff', textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)' }}>{album.name}</h6>
                      <p className="text-truncate mb-0" style={{ fontSize: '0.75rem', color: '#e0e0e0' }}>
                        {album.artists && album.artists.primary && album.artists.primary.map(artist => artist.name).join(', ')}
                      </p>
                    </div>
                  </motion.div>
                </div>
              ))}
            </div>
          </div>
        );
      case 'artists':
        return (
          <div className="artist-grid">
            <div className="row g-4">
              {artists.map(artist => (
                <div key={artist.id} className="col-6 col-md-4 col-lg-3">
                  <motion.div
                    className="artist-card h-100 cursor-pointer text-center"
                    whileHover={{ y: -5, transition: { duration: 0.3 } }}
                    style={{
                      backgroundColor: 'rgba(15, 15, 15, 0.5)',
                      borderRadius: '0.75rem',
                      overflow: 'hidden',
                      border: '1px solid rgba(255, 255, 255, 0.05)',
                      boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)',
                      padding: '1.5rem 1rem'
                    }}
                  >
                    <div className="position-relative mb-3">
                      <div className="mx-auto" style={{
                        width: '120px',
                        height: '120px',
                        borderRadius: '50%',
                        overflow: 'hidden',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.2)'
                      }}>
                        <img
                          src={artist.image && artist.image.length > 0
                            ? (
                              // Try to get the highest quality image available
                              artist.image.find(img => img.quality === '500x500')?.url ||
                              artist.image.find(img => img.quality === '150x150')?.url ||
                              artist.image[artist.image.length - 1]?.url ||
                              artist.image[0].url
                            )
                            : 'https://via.placeholder.com/500'
                          }
                          alt={artist.name}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            imageRendering: 'high-quality'
                          }}
                        />
                      </div>
                    </div>
                    <h6 className="fw-bold mb-1" style={{ color: '#ffffff', textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)' }}>{artist.name}</h6>
                    <p className="mb-0" style={{ fontSize: '0.75rem', color: '#e0e0e0' }}>Artist</p>
                  </motion.div>
                </div>
              ))}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="search-page">
      <div className="search-header mb-4 py-4 rounded-4" style={{
        background: 'linear-gradient(135deg, rgba(157, 78, 221, 0.2) 0%, rgba(0, 255, 209, 0.1) 100%)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.05)',
        boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
        padding: '20px'
      }}>
        <div className="container">
          <h4 className="mb-3 fw-bold text-center" style={{ color: '#ffffff' }}>Discover Your Music</h4>
          <div className="search-container search-page-search">
            <SearchBar onSearch={handleSearch} placeholder="Search for songs, artists, or albums..." />
          </div>
        </div>
      </div>

      {query && (
        <div className="mb-4">
          <h4 className="fw-bold" style={{ color: '#ffffff' }}>
            <span style={{ color: 'var(--dark-accent)' }}>Results</span> for "{query}"
          </h4>
        </div>
      )}

      <div className="search-tabs mb-4">
        <div className="d-flex justify-content-center justify-content-md-start">
          <div className="tab-buttons d-flex gap-2 rounded-pill p-1" style={{
            backgroundColor: 'rgba(15, 15, 15, 0.5)',
            border: '1px solid rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)'
          }}>
            <button
              className={`btn rounded-pill px-3 py-2 ${activeTab === 'songs' ? 'active-tab' : ''}`}
              onClick={() => setActiveTab('songs')}
              style={{
                backgroundColor: activeTab === 'songs' ? 'var(--dark-accent)' : 'transparent',
                color: activeTab === 'songs' ? '#2c3e50' : 'var(--dark-text)',
                border: 'none',
                transition: 'all 0.3s ease',
                fontSize: '0.9rem',
                fontWeight: activeTab === 'songs' ? '600' : '400'
              }}
            >
              Songs ({songs.length})
            </button>
            <button
              className={`btn rounded-pill px-3 py-2 ${activeTab === 'albums' ? 'active-tab' : ''}`}
              onClick={() => setActiveTab('albums')}
              style={{
                backgroundColor: activeTab === 'albums' ? 'var(--dark-accent)' : 'transparent',
                color: activeTab === 'albums' ? '#2c3e50' : 'var(--dark-text)',
                border: 'none',
                transition: 'all 0.3s ease',
                fontSize: '0.9rem',
                fontWeight: activeTab === 'albums' ? '600' : '400'
              }}
            >
              Albums ({albums.length})
            </button>
            <button
              className={`btn rounded-pill px-3 py-2 ${activeTab === 'artists' ? 'active-tab' : ''}`}
              onClick={() => setActiveTab('artists')}
              style={{
                backgroundColor: activeTab === 'artists' ? 'var(--dark-accent)' : 'transparent',
                color: activeTab === 'artists' ? '#2c3e50' : 'var(--dark-text)',
                border: 'none',
                transition: 'all 0.3s ease',
                fontSize: '0.9rem',
                fontWeight: activeTab === 'artists' ? '600' : '400'
              }}
            >
              Artists ({artists.length})
            </button>
          </div>
        </div>
      </div>

      <div className="search-results">
        {renderContent()}
      </div>


    </div>
  );
};

export default Search;
