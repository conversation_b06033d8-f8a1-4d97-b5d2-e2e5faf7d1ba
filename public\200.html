<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- Base URL for all relative URLs -->
    <base href="/" />
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Viewport and theme -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <meta name="theme-color" content="#0f0f0f" />

    <!-- Primary Meta Tags -->
    <title>MusicMania - Modern Music Player</title>
    <meta name="title" content="MusicMania - Modern Music Player" />
    <meta name="description" content="A responsive music player web app with a modern UI/UX, dark/light theme toggle, and integration with the saavn.dev API." />
    <meta name="keywords" content="music player, web app, react, bootstrap, firebase, responsive design" />
    <meta name="author" content="Abhinav" />

    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()" />

    <style>
      body {
        background-color: #0f0f0f;
        color: #E0E0E0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        margin: 0;
        padding: 20px;
        text-align: center;
      }
      h1 {
        color: #00FFD1;
        margin-bottom: 1rem;
      }
      p {
        margin-bottom: 1.5rem;
        max-width: 600px;
      }
      .btn {
        background-color: #00FFD1;
        color: #0f0f0f;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.3s ease;
      }
      .btn:hover {
        background-color: #00e6bd;
        transform: translateY(-2px);
      }
      .loading {
        margin-top: 20px;
        font-size: 0.9rem;
        opacity: 0.7;
      }
    </style>
  </head>
  <body>
    <h1>Redirecting to MusicMania</h1>
    <p>You're being redirected to the requested page. If you're not redirected automatically, please click the button below.</p>
    <a href="/" class="btn" id="redirect-btn">Go to MusicMania</a>
    <div class="loading" id="loading-text">Redirecting in <span id="countdown">3</span> seconds...</div>

    <script>
      // This is a fallback page that redirects to the main app
      (function() {
        // Store the current path for the main app to use
        const currentPath = window.location.pathname + window.location.search;
        sessionStorage.setItem('initialPath', currentPath);

        // Set up countdown
        let count = 3;
        const countdownEl = document.getElementById('countdown');
        const interval = setInterval(() => {
          count--;
          countdownEl.textContent = count;
          if (count <= 0) {
            clearInterval(interval);
            window.location.href = '/';
          }
        }, 1000);

        // Update the redirect button to include the current path info
        const redirectBtn = document.getElementById('redirect-btn');
        redirectBtn.addEventListener('click', function(e) {
          e.preventDefault();
          clearInterval(interval);
          window.location.href = '/';
        });
      })();
    </script>
  </body>
</html>
