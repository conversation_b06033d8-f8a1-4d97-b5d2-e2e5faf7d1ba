import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import App from './App.jsx';
import { validateEnvConfig } from './utils/validateConfig';
import logger from './utils/logger';

// Validate environment configuration
const isConfigValid = validateEnvConfig();
if (!isConfigValid && import.meta.env.PROD) {
  // In production, show a user-friendly error
  document.getElementById('root').innerHTML = `
    <div style="text-align: center; padding: 2rem; font-family: system-ui, sans-serif;">
      <h1>Configuration Error</h1>
      <p>The application could not start due to a configuration error.</p>
      <p>Please contact support for assistance.</p>
    </div>
  `;
  logger.error('Application failed to start due to invalid configuration');
} else {
  // Create a client
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: 1,
        staleTime: 5 * 60 * 1000, // 5 minutes
      },
    },
  });

  // Log environment information
  logger.info(`Starting application in ${import.meta.env.MODE} mode`);

  // Render the app
  createRoot(document.getElementById('root')).render(
    <StrictMode>
      <QueryClientProvider client={queryClient}>
        <App />
      </QueryClientProvider>
    </StrictMode>,
  );
}
