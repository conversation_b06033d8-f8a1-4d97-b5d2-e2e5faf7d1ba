/**
 * Logger utility for the application
 * In production, all logs are suppressed except errors
 */

const isProduction = import.meta.env.PROD;
const enableLogs = import.meta.env.VITE_ENABLE_LOGS === 'true';

/**
 * Log information to the console (only in development)
 * @param {...any} args - Arguments to log
 */
export const logInfo = (...args) => {
  if (!isProduction || enableLogs) {
    console.log(...args);
  }
};

/**
 * Log warnings to the console (only in development)
 * @param {...any} args - Arguments to log
 */
export const logWarning = (...args) => {
  if (!isProduction || enableLogs) {
    console.warn(...args);
  }
};

/**
 * Log errors to the console (always logged)
 * @param {...any} args - Arguments to log
 */
export const logError = (...args) => {
  console.error(...args);
};

/**
 * Default logger object with all methods
 */
const logger = {
  info: logInfo,
  warn: logWarning,
  error: logError
};

export default logger;
