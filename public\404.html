<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- Base URL for all relative URLs -->
    <base href="/" />
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Viewport and theme -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <meta name="theme-color" content="#0f0f0f" />

    <!-- Primary Meta Tags -->
    <title>Page Not Found - MusicMania</title>
    <meta name="title" content="Page Not Found - MusicMania" />
    <meta name="description" content="A responsive music player web app with a modern UI/UX, dark/light theme toggle, and integration with the saavn.dev API." />
    <meta name="keywords" content="music player, web app, react, bootstrap, firebase, responsive design" />
    <meta name="author" content="Abhinav" />

    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()" />
    
    <style>
      body {
        background-color: #0f0f0f;
        color: #E0E0E0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        margin: 0;
        padding: 20px;
        text-align: center;
      }
      h1 {
        color: #00FFD1;
        margin-bottom: 1rem;
        font-size: 3rem;
      }
      p {
        margin-bottom: 1.5rem;
        max-width: 600px;
        font-size: 1.1rem;
      }
      .btn {
        background-color: #00FFD1;
        color: #0f0f0f;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 1rem;
      }
      .btn:hover {
        background-color: #00e6bd;
        transform: translateY(-2px);
      }
      .error-code {
        font-size: 8rem;
        font-weight: bold;
        color: rgba(0, 255, 209, 0.1);
        position: absolute;
        z-index: -1;
      }
    </style>
  </head>
  <body>
    <div class="error-code">404</div>
    <h1>Page Not Found</h1>
    <p>The page you're looking for doesn't exist or has been moved. Don't worry, we'll get you back to the music.</p>
    <a href="/" class="btn" id="home-btn">Go to Home Page</a>

    <script>
      // This is a fallback page for 404 errors
      (function() {
        // Store the current path for potential debugging
        const currentPath = window.location.pathname + window.location.search;
        console.log('404 error for path:', currentPath);
        
        // Add click event to home button
        const homeBtn = document.getElementById('home-btn');
        homeBtn.addEventListener('click', function(e) {
          // No need to prevent default as we want to navigate to home
        });
      })();
    </script>
  </body>
</html>
