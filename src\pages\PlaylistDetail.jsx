import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Button, Dropdown, Alert } from 'react-bootstrap';
import { FaPlay, FaPause, FaEllipsisH, FaMusic, FaHeart, FaRegHeart, FaPlus } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import { usePlaylist } from '../context/PlaylistContext';
import { useAuth } from '../context/AuthContext';
import { useSong } from '../context/SongContext';
import SongList from '../components/features/SongList';
import LoadingSpinner from '../components/common/LoadingSpinner';
import EditPlaylistModal from '../components/playlists/EditPlaylistModal';
import DeletePlaylistModal from '../components/playlists/DeletePlaylistModal';

const PlaylistDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [playlist, setPlaylist] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const { currentUser } = useAuth();
  const {
    currentSong,
    isPlaying,
    playSong,
    pauseSong,
    resumeSong,
    playPlaylist,
    currentPlaylist
  } = useSong();
  const { playlists } = usePlaylist();

  // Fetch playlist data
  useEffect(() => {
    const fetchPlaylist = async () => {
      try {
        setLoading(true);

        // First check if the playlist is in the context
        const contextPlaylist = playlists.find(p => p.id === id);

        if (contextPlaylist) {
          setPlaylist(contextPlaylist);
          setLoading(false);
          return;
        }

        // If not in context, fetch from Firestore
        if (currentUser) {
          try {
            // Use the user-specific collection path
            const playlistRef = doc(db, `users/${currentUser.uid}/playlists`, id);
            const playlistDoc = await getDoc(playlistRef);

            if (playlistDoc.exists()) {
              setPlaylist({
                id: playlistDoc.id,
                ...playlistDoc.data()
              });
            } else {
              // Try to get from localStorage as fallback
              const localPlaylists = localStorage.getItem('userPlaylists');
              if (localPlaylists) {
                const parsedPlaylists = JSON.parse(localPlaylists);
                const localPlaylist = parsedPlaylists.find(p => p.id === id);
                if (localPlaylist) {
                  setPlaylist(localPlaylist);
                } else {
                  setError('Playlist not found');
                  navigate('/playlists');
                }
              } else {
                setError('Playlist not found');
                navigate('/playlists');
              }
            }
          } catch (fetchErr) {
            console.error('Error fetching from Firestore:', fetchErr);

            // Try to get from localStorage as fallback
            const localPlaylists = localStorage.getItem('userPlaylists');
            if (localPlaylists) {
              const parsedPlaylists = JSON.parse(localPlaylists);
              const localPlaylist = parsedPlaylists.find(p => p.id === id);
              if (localPlaylist) {
                setPlaylist(localPlaylist);
              } else {
                setError('Playlist not found');
                navigate('/playlists');
              }
            } else {
              setError('Playlist not found');
              navigate('/playlists');
            }
          }
        } else {
          setError('You must be logged in to view playlists');
          navigate('/playlists');
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching playlist:', err);
        setError('Failed to load playlist');
        setLoading(false);
      }
    };

    fetchPlaylist();
  }, [id, playlists, navigate]);

  // Handle play all songs
  const handlePlayAll = () => {
    console.log('handlePlayAll called, playlist:', playlist?.id, 'songs:', playlist?.songs?.length);

    if (!playlist || !playlist.songs || playlist.songs.length === 0) {
      console.log('Playlist is empty or undefined, cannot play');
      return;
    }

    // If this playlist is already playing, toggle play/pause
    if (currentPlaylist && currentPlaylist.id === playlist.id && currentSong) {
      console.log('This playlist is already active, toggling play/pause');
      if (isPlaying) {
        console.log('Pausing current playlist');
        pauseSong();
      } else {
        console.log('Resuming current playlist');
        resumeSong();
      }
    } else {
      // Start playing the playlist from the beginning
      console.log('Starting playlist from beginning:', playlist.name);
      console.log('First song:', playlist.songs[0]?.name);

      // Make sure the playlist object has all required properties
      const enhancedSongs = playlist.songs.map(song => {
        // Ensure each song has all required properties for playback
        return {
          ...song,
          // If duration is missing or 0, set a default (4 minutes)
          duration: song.duration || 240,
          // Make sure downloadUrl is present
          downloadUrl: song.downloadUrl || []
        };
      });

      const playlistToPlay = {
        ...playlist,
        id: playlist.id || `playlist-${Date.now()}`, // Ensure we have an ID
        songs: enhancedSongs // Use the enhanced songs array
      };

      console.log('Playing playlist with ID:', playlistToPlay.id);
      console.log('Enhanced songs:', enhancedSongs.length);
      playPlaylist(playlistToPlay, 0);
    }
  };

  // Check if current user is the owner
  const isOwner = currentUser && playlist?.userId === currentUser.uid;

  // Get a high-quality cover image from the first song in the playlist, or use a custom default
  const coverImage = playlist?.songs?.length > 0 && playlist.songs[0]?.image && playlist.songs[0].image.length > 0
    ? (
      // Try to get the highest quality image available
      playlist.songs[0].image.find(img => img.quality === '500x500')?.url ||
      playlist.songs[0].image[playlist.songs[0].image.length - 1]?.url ||
      playlist.songs[0].image[0].url
    )
    : `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300' viewBox='0 0 300 300'%3E%3Crect width='300' height='300' fill='%230f0f0f'/%3E%3Cpath d='M150,70 C110,70 80,100 80,140 C80,180 110,210 150,210 C190,210 220,180 220,140 C220,100 190,70 150,70 Z M150,190 C120,190 100,170 100,140 C100,110 120,90 150,90 C180,90 200,110 200,140 C200,170 180,190 150,190 Z' fill='%2300FFD1' opacity='0.7'/%3E%3Ccircle cx='150' cy='140' r='30' fill='%230f0f0f'/%3E%3Ccircle cx='150' cy='140' r='10' fill='%2300FFD1'/%3E%3C/svg%3E`;

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <LoadingSpinner />
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="mb-4 playlist-detail-header">
        <Col md={4} className="mb-4 mb-md-0">
          <div
            className="position-relative rounded-3 overflow-hidden playlist-image"
            style={{
              boxShadow: '0 15px 30px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)',
              aspectRatio: '1/1',
              background: 'linear-gradient(145deg, rgba(20, 20, 20, 0.8) 0%, rgba(10, 10, 10, 0.9) 100%)',
              padding: '8px'
            }}
          >
            <div
              className="position-relative rounded-3 overflow-hidden h-100"
              style={{
                boxShadow: 'inset 0 0 30px rgba(0, 0, 0, 0.4)'
              }}
            >
              <img
                src={coverImage}
                alt={playlist.name}
                className="img-fluid w-100 h-100"
                style={{
                  objectFit: 'cover',
                  imageRendering: 'high-quality',
                  filter: 'brightness(1) contrast(1.05)',
                  boxShadow: 'inset 0 0 30px rgba(0, 0, 0, 0.5)',
                  borderRadius: '4px'
                }}
              />

              {/* Play button overlay */}
              <div
                className="position-absolute top-0 left-0 w-100 h-100 d-flex align-items-center justify-content-center"
                style={{
                  background: 'rgba(0, 0, 0, 0.3)',
                  opacity: 0,
                  transition: 'opacity 0.3s ease',
                  ':hover': { opacity: 1 }
                }}
              >
                <motion.button
                  className="btn rounded-circle d-flex align-items-center justify-content-center"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handlePlayAll}
                  aria-label="Play all"
                  style={{
                    width: '60px',
                    height: '60px',
                    backgroundColor: 'var(--dark-accent)',
                    color: '#2c3e50',
                    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)'
                  }}
                >
                  <FaPlay size={20} />
                </motion.button>
              </div>
            </div>
          </div>
        </Col>

        <Col md={8}>
          <div className="d-flex justify-content-between align-items-start playlist-info">
            <div>
              <h1 className="mb-2" style={{ color: '#ffffff' }}>{playlist.name}</h1>
              {playlist.description && (
                <p style={{ color: '#e0e0e0' }}>{playlist.description}</p>
              )}

              <div className="d-flex align-items-center mb-3" style={{ color: '#cccccc' }}>
                <FaMusic className="me-2" />
                <span>{playlist.songs?.length || 0} {playlist.songs?.length === 1 ? 'song' : 'songs'}</span>
              </div>

              <div className="d-flex gap-2 playlist-actions">
                <motion.button
                  className="btn d-flex align-items-center justify-content-center"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handlePlayAll}
                  disabled={!playlist.songs || playlist.songs.length === 0}
                  style={{
                    backgroundColor: 'var(--dark-accent)',
                    color: '#2c3e50',
                    border: 'none',
                    borderRadius: '50rem',
                    padding: '0.5rem 1.5rem',
                    fontWeight: '600'
                  }}
                >
                  {currentPlaylist && currentPlaylist.id === playlist.id && isPlaying ? (
                    <>
                      <FaPause className="me-2" /> Pause
                    </>
                  ) : (
                    <>
                      <FaPlay className="me-2" /> Play All
                    </>
                  )}
                </motion.button>

                {isOwner && (
                  <Dropdown>
                    <Dropdown.Toggle
                      variant="outline-secondary"
                      id="dropdown-playlist-options"
                    >
                      <FaEllipsisH />
                    </Dropdown.Toggle>

                    <Dropdown.Menu variant="dark">
                      <Dropdown.Item onClick={() => setShowEditModal(true)}>Edit playlist</Dropdown.Item>
                      <Dropdown.Item onClick={() => setShowDeleteModal(true)} className="text-danger">Delete playlist</Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>
                )}
              </div>
            </div>
          </div>
        </Col>
      </Row>

      {playlist.songs && playlist.songs.length > 0 ? (
        <SongList
          songs={playlist.songs}
          showIndex={true}
          showRemoveOption={isOwner}
          playlistId={playlist.id}
        />
      ) : (
        <div
          className="text-center py-5 rounded-4 mt-4 empty-playlist"
          style={{
            background: 'linear-gradient(145deg, rgba(15, 15, 15, 0.8) 0%, rgba(30, 30, 30, 0.6) 100%)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.05)',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
            padding: '3rem 2rem'
          }}
        >
          <div className="mb-4">
            <div
              className="d-inline-flex align-items-center justify-content-center rounded-circle mb-3 icon-container"
              style={{
                width: '80px',
                height: '80px',
                background: 'linear-gradient(135deg, rgba(0, 255, 209, 0.2) 0%, rgba(0, 255, 209, 0.1) 100%)',
                boxShadow: '0 4px 20px rgba(0, 255, 209, 0.15)'
              }}
            >
              <FaMusic size={35} style={{ color: 'var(--dark-accent)' }} />
            </div>
          </div>
          <h3 className="mb-3" style={{ color: '#ffffff' }}>This playlist is empty</h3>
          <p style={{ color: '#e0e0e0', maxWidth: '500px', margin: '0 auto' }} className="mb-4">
            Your playlist is ready for some amazing tracks. Search for your favorite songs and add them to start building your collection.
          </p>
          {isOwner && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn d-inline-flex align-items-center justify-content-center"
              onClick={() => navigate('/search')}
              style={{
                backgroundColor: 'var(--dark-accent)',
                color: '#2c3e50',
                border: 'none',
                borderRadius: '50rem',
                padding: '0.5rem 1.5rem',
                fontWeight: '600',
                boxShadow: '0 4px 15px rgba(0, 255, 209, 0.3)'
              }}
            >
              <FaPlus className="me-2" /> Add Songs
            </motion.button>
          )}
        </div>
      )}

      <EditPlaylistModal
        show={showEditModal}
        onHide={() => setShowEditModal(false)}
        playlist={playlist}
      />

      <DeletePlaylistModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        playlist={playlist}
        onDelete={() => navigate('/playlists')}
      />
    </Container>
  );
};

export default PlaylistDetail;
