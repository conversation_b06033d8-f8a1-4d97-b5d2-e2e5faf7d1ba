import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaPlay, FaPause } from 'react-icons/fa';
import { getSongById, searchSongs } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import { useSong } from '../../context/SongContext';
import { motion } from 'framer-motion';

// Helper function to format play count in K/M format
const formatPlayCount = (count) => {
  if (!count) return '0';

  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  } else {
    return count.toString();
  }
};

const RelatedSongs = ({ songId }) => {
  const [relatedSongs, setRelatedSongs] = useState([]);
  const [currentSongDetails, setCurrentSongDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const { playSong, pauseSong, currentSong, isPlaying } = useSong();
  const navigate = useNavigate();

  // Function to retry loading related songs
  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    setRetryCount(prevCount => prevCount + 1);
  };

  useEffect(() => {
    const fetchRelatedSongs = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Step 1: Get current song details
        console.log(`Fetching details for song ID: ${songId}`);
        const songResponse = await getSongById(songId);

        console.log('Song API response:', songResponse);

        if (!songResponse.success) {
          throw new Error(`API error: ${songResponse.message || 'Unknown error'}`);
        }

        if (!songResponse.data || !songResponse.data[0]) {
          throw new Error('No song data returned from API');
        }

        // The API returns an array with a single song object
        const songDetails = songResponse.data[0];
        console.log('Full song details:', songDetails);

        setCurrentSongDetails(songDetails);

        // Step 2: Extract artist ID from the song details
        // Check if artists object exists and has primary artists
        if (!songDetails.artists || !songDetails.artists.primary || songDetails.artists.primary.length === 0) {
          throw new Error('No artist information found for this song');
        }

        // Get the first primary artist's ID and name
        const primaryArtist = songDetails.artists.primary[0];
        const artistId = primaryArtist.id;
        const artistName = primaryArtist.name;

        if (!artistId) {
          throw new Error('Artist ID not found');
        }

        console.log(`Found artist ID: ${artistId} for artist: ${artistName}`);

        // Step 3: Search for songs by this artist
        console.log(`Searching for songs by artist: ${artistName} (ID: ${artistId})`);
        const artistResponse = await searchSongs(artistName, 0, 10);

        if (!artistResponse.success || !artistResponse.data || !artistResponse.data.results) {
          throw new Error('Failed to fetch songs by the same artist');
        }

        // Filter out the current song and limit to 6 songs
        const artistSongs = artistResponse.data.results
          .filter(song => song.id !== songId)
          .slice(0, 6)
          .map(song => ({ ...song, matchType: 'artist' }));

        console.log(`Found ${artistSongs.length} songs by the same artist`);

        if (artistSongs.length > 0) {
          setRelatedSongs(artistSongs);
        } else {
          console.log('No related songs found by the same artist');
          setRelatedSongs([]);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching related songs:', err);
        setError(err.message || 'Failed to load related songs');
        setIsLoading(false);
      }
    };

    if (songId) {
      console.log(`Starting related songs fetch for song ID: ${songId}`);
      fetchRelatedSongs();
    } else {
      console.warn('No song ID provided to RelatedSongs component');
      setIsLoading(false);
    }

    // Cleanup function
    return () => {
      // Cancel any pending requests if needed
    };
  }, [songId, retryCount]); // Include retryCount to trigger refetch when retry button is clicked

  // Render loading state
  if (isLoading) {
    return (
      <div className="related-songs mt-4">
        <h4 className="mb-3">Related Songs</h4>
        <div className="text-center py-4">
          <LoadingSpinner />
          <p className="mt-3 text-muted">Finding related songs...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="related-songs mt-4">
        <h4 className="mb-3">Related Songs</h4>
        <div className="alert alert-danger">
          <h5>Oops! Something went wrong</h5>
          <p className="mb-0">We're having trouble finding related songs right now.</p>
          <div className="d-flex justify-content-between align-items-center mt-3">
            <small className="text-muted">Technical details: {error}</small>
            <button
              className="btn btn-sm btn-outline-danger"
              onClick={handleRetry}
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render empty state
  if (!relatedSongs || relatedSongs.length === 0) {
    return (
      <div className="related-songs mt-4">
        <h4 className="mb-3">Related Songs</h4>
        <div className="alert alert-info">
          <h5>No Related Songs Found</h5>
          <p className="mb-0">
            We couldn't find any related songs at this time.
            Try exploring our library for more music!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="related-songs mt-4 mb-5">
      <motion.h4
        className="mb-3"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        Related Songs
      </motion.h4>

      <div className="row row-cols-2 row-cols-md-3 g-3 g-md-4">
        {relatedSongs.map((song, index) => (
          <motion.div
            key={song.id}
            className="col"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <div
              className="card h-100 border-0 shadow-sm cursor-pointer"
              onClick={() => navigate(`/song/${song.id}`)}
              style={{
                borderRadius: '0.5rem',
                backgroundColor: 'rgba(15, 15, 15, 0.7)',
                transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                fontSize: '0.9rem', // Smaller font size for better mobile display
                border: '1px solid rgba(255, 255, 255, 0.05)'
              }}
              onMouseOver={(e) => {
                if (window.innerWidth > 768) { // Only apply hover effects on larger screens
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = '0 10px 20px rgba(0,0,0,0.3)';
                }
              }}
              onMouseOut={(e) => {
                if (window.innerWidth > 768) {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '';
                }
              }}
            >
              <div className="row g-0">
                <div className="col-4 position-relative">
                  <img
                    src={
                      song.image && song.image.length > 0
                        ? (song.image.find(img => img.quality === '500x500')?.url || song.image[0].url)
                        : 'https://via.placeholder.com/100'
                    }
                    alt={song.name}
                    className="img-fluid rounded-start"
                    style={{
                      height: '100%',
                      objectFit: 'cover',
                      borderTopLeftRadius: '0.5rem',
                      borderBottomLeftRadius: '0.5rem',
                      minHeight: '80px', // Ensure minimum height on mobile
                      boxShadow: 'inset 0 0 15px rgba(0, 0, 0, 0.3)'
                    }}
                  />
                  <div className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                    <button
                      className={`btn ${currentSong && currentSong.id === song.id && isPlaying ? 'btn-danger' : 'btn-primary'} rounded-circle shadow-sm`}
                      style={{
                        width: window.innerWidth < 576 ? '30px' : '36px',
                        height: window.innerWidth < 576 ? '30px' : '36px',
                        padding: '0',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (currentSong && currentSong.id === song.id && isPlaying) {
                          pauseSong();
                        } else {
                          playSong(song);
                          navigate(`/song/${song.id}`);
                        }
                      }}
                    >
                      {currentSong && currentSong.id === song.id && isPlaying ? (
                        <FaPause size={14} />
                      ) : (
                        <FaPlay size={14} className="ms-1" />
                      )}
                    </button>
                  </div>
                </div>

                <div className="col-8">
                  <div className="card-body py-2 px-2 px-md-3">
                    <Link to={`/song/${song.id}`} className="text-decoration-none">
                      <h6 className="card-title mb-1 text-truncate" style={{ color: '#ffffff' }}>{song.name}</h6>
                    </Link>
                    <p className="card-text mb-1 small text-truncate" style={{ color: '#e0e0e0' }}>
                      {song.artists && song.artists.primary && song.artists.primary.map(artist => artist.name).join(', ')}
                    </p>
                    <div className="d-flex justify-content-between align-items-center">
                      <small style={{ color: '#cccccc' }}>
                        {song.duration ? `${Math.floor(song.duration / 60)}:${String(Math.floor(song.duration % 60)).padStart(2, '0')}` : ''}
                      </small>
                      <small style={{ color: '#cccccc' }} className="d-flex align-items-center">
                        <i className="bi bi-headphones me-1" style={{ fontSize: '0.8rem' }}></i>
                        {formatPlayCount(song.playCount)}
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default RelatedSongs;
