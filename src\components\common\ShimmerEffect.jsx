import React from 'react';
import { motion } from 'framer-motion';

/**
 * ShimmerEffect component for displaying loading states
 * 
 * @param {Object} props
 * @param {string} props.type - Type of shimmer (card, text, circle, image, etc.)
 * @param {number} props.width - Width of the shimmer element
 * @param {number} props.height - Height of the shimmer element
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.style - Additional inline styles
 * @param {number} props.count - Number of shimmer elements to display
 * @param {boolean} props.rounded - Whether to apply rounded corners
 * @param {boolean} props.circle - Whether to make the shimmer a circle
 */
const ShimmerEffect = ({
  type = 'text',
  width,
  height,
  className = '',
  style = {},
  count = 1,
  rounded = true,
  circle = false,
}) => {
  // Base shimmer style
  const baseStyle = {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: rounded ? '0.5rem' : '0',
    ...(circle && { borderRadius: '50%' }),
    ...style,
  };

  // Determine dimensions based on type
  const getDimensions = () => {
    switch (type) {
      case 'card':
        return { width: width || '100%', height: height || '200px' };
      case 'text':
        return { width: width || '100%', height: height || '1rem' };
      case 'circle':
        return { width: width || '40px', height: height || '40px', borderRadius: '50%' };
      case 'image':
        return { width: width || '100%', height: height || '200px' };
      case 'avatar':
        return { width: width || '50px', height: height || '50px', borderRadius: '50%' };
      case 'button':
        return { width: width || '100px', height: height || '40px' };
      case 'table-row':
        return { width: width || '100%', height: height || '50px' };
      default:
        return { width: width || '100%', height: height || '1rem' };
    }
  };

  // Animation for the shimmer effect
  const shimmerAnimation = {
    initial: { x: '-100%' },
    animate: { x: '100%' },
    transition: {
      repeat: Infinity,
      repeatType: 'loop',
      duration: 1.5,
      ease: 'linear',
    },
  };

  // Render multiple shimmer elements if count > 1
  if (count > 1) {
    return (
      <div className={`shimmer-container ${className}`}>
        {Array.from({ length: count }).map((_, index) => (
          <div
            key={index}
            className={`shimmer-item ${type} mb-2`}
            style={{ ...baseStyle, ...getDimensions() }}
          >
            <motion.div
              className="shimmer-animation"
              initial={shimmerAnimation.initial}
              animate={shimmerAnimation.animate}
              transition={shimmerAnimation.transition}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent)',
                zIndex: 1,
              }}
            />
          </div>
        ))}
      </div>
    );
  }

  // Render a single shimmer element
  return (
    <div
      className={`shimmer-item ${type} ${className}`}
      style={{ ...baseStyle, ...getDimensions() }}
    >
      <motion.div
        className="shimmer-animation"
        initial={shimmerAnimation.initial}
        animate={shimmerAnimation.animate}
        transition={shimmerAnimation.transition}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent)',
          zIndex: 1,
        }}
      />
    </div>
  );
};

export default ShimmerEffect;
