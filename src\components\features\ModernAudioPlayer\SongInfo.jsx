import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaExpand, FaCompress, FaMusic } from 'react-icons/fa';

const SongInfo = ({ song, isExpanded, onExpandToggle }) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Handle image load error
  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  // Get song image URL
  const getImageUrl = () => {
    if (imageError) return null;
    return song?.image || song?.thumbnail || song?.albumArt || null;
  };

  // Truncate text for display
  const truncateText = (text, maxLength) => {
    if (!text) return '';
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  return (
    <div className={`song-info ${isExpanded ? 'expanded' : 'collapsed'}`}>
      {/* Song artwork */}
      <motion.div 
        className="song-artwork"
        layout
        transition={{ duration: 0.3 }}
      >
        {getImageUrl() ? (
          <motion.img
            src={getImageUrl()}
            alt={song?.name || 'Song artwork'}
            onError={handleImageError}
            onLoad={handleImageLoad}
            className={`artwork-image ${imageLoaded ? 'loaded' : ''}`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: imageLoaded ? 1 : 0, scale: imageLoaded ? 1 : 0.8 }}
            transition={{ duration: 0.3 }}
          />
        ) : (
          <div className="artwork-placeholder">
            <FaMusic />
          </div>
        )}
        
        {/* Expand/collapse button overlay */}
        {!isExpanded && (
          <motion.button
            className="expand-btn"
            onClick={onExpandToggle}
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.05 }}
            aria-label="Expand player"
          >
            <FaExpand />
          </motion.button>
        )}
        
        {/* Loading overlay */}
        {!imageLoaded && getImageUrl() && (
          <div className="artwork-loading">
            <motion.div
              className="loading-spinner"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
          </div>
        )}
      </motion.div>

      {/* Song details */}
      <motion.div 
        className="song-details"
        layout
        transition={{ duration: 0.3 }}
      >
        <div className="song-text">
          {/* Song title */}
          <motion.h3 
            className="song-title"
            layout
            title={song?.name}
          >
            {isExpanded 
              ? song?.name || 'Unknown Title'
              : truncateText(song?.name || 'Unknown Title', 30)
            }
          </motion.h3>

          {/* Artist name */}
          <motion.p 
            className="song-artist"
            layout
            title={song?.artist || song?.primaryArtists}
          >
            {isExpanded 
              ? song?.artist || song?.primaryArtists || 'Unknown Artist'
              : truncateText(song?.artist || song?.primaryArtists || 'Unknown Artist', 25)
            }
          </motion.p>

          {/* Additional info in expanded mode */}
          {isExpanded && (
            <motion.div 
              className="song-metadata"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              {song?.album && (
                <p className="song-album" title={song.album}>
                  <span className="metadata-label">Album:</span>
                  <span className="metadata-value">{song.album}</span>
                </p>
              )}
              
              {song?.year && (
                <p className="song-year">
                  <span className="metadata-label">Year:</span>
                  <span className="metadata-value">{song.year}</span>
                </p>
              )}
              
              {song?.duration && (
                <p className="song-duration">
                  <span className="metadata-label">Duration:</span>
                  <span className="metadata-value">{song.duration}</span>
                </p>
              )}
              
              {song?.language && (
                <p className="song-language">
                  <span className="metadata-label">Language:</span>
                  <span className="metadata-value">{song.language}</span>
                </p>
              )}
            </motion.div>
          )}
        </div>

        {/* Expand button for collapsed mode */}
        {!isExpanded && (
          <motion.button
            className="expand-btn-text"
            onClick={onExpandToggle}
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.05 }}
            aria-label="Expand player"
          >
            <FaExpand />
          </motion.button>
        )}
      </motion.div>

      {/* Collapse button for expanded mode */}
      {isExpanded && (
        <motion.button
          className="collapse-btn"
          onClick={onExpandToggle}
          whileTap={{ scale: 0.95 }}
          whileHover={{ scale: 1.05 }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          aria-label="Collapse player"
        >
          <FaCompress />
        </motion.button>
      )}
    </div>
  );
};

export default SongInfo;
