import { FaGithub, FaLinkedin, FaInstagram, FaHeadphones, FaHeart } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      className="mt-5 py-5 d-none d-lg-block"
      style={{
        backgroundColor: 'rgba(15, 15, 15, 0.95)',
        borderTop: '1px solid rgba(255, 255, 255, 0.05)',
        color: 'var(--dark-text)'
      }}
    >
      <div className="container">
        <div className="row g-4">
          <motion.div
            className="col-lg-5 col-md-6 mb-4 mb-md-0"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="d-flex align-items-center mb-3">
              <FaHeadphones className="me-2" style={{ color: 'var(--dark-accent)', fontSize: '1.5rem' }} />
              <h4 className="mb-0">
                <span style={{ color: 'var(--dark-accent)' }}>Musafir</span>Music
              </h4>
            </div>
            <p className="text-muted mb-3">
              A modern music player web app built with React, Firebase, and Bootstrap.
              Discover and enjoy your favorite music with a sleek, responsive interface.
            </p>
            <p className="mb-0 d-flex align-items-center">
              <span className="me-2">Made with</span>
              <FaHeart className="me-2" style={{ color: 'var(--dark-accent)' }} />
              <span>by <span style={{ color: 'var(--dark-accent)' }}>Abhinav Kumar Jha</span></span>
            </p>
            <p className="mt-2 small text-muted">
              B.Tech CSE Student at IPU | Full-stack Developer
            </p>
          </motion.div>

          <motion.div
            className="col-lg-3 col-md-6 mb-4 mb-md-0"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h5 className="mb-3" style={{ color: 'var(--dark-accent)' }}>Quick Links</h5>
            <ul className="list-unstyled">
              <li className="mb-2">
                <Link to="/" className="text-decoration-none text-muted hover-link">
                  Home
                </Link>
              </li>
              <li className="mb-2">
                <Link to="/playlists" className="text-decoration-none text-muted hover-link">
                  Playlists
                </Link>
              </li>
              <li className="mb-2">
                <Link to="/search" className="text-decoration-none text-muted hover-link">
                  Search
                </Link>
              </li>
            </ul>
          </motion.div>

          <motion.div
            className="col-lg-4 col-md-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h5 className="mb-3" style={{ color: 'var(--dark-accent)' }}>Connect</h5>
            <div className="d-flex gap-4">
              <a
                href="https://github.com/infinityabhinav"
                target="_blank"
                rel="noopener noreferrer"
                className="social-icon-link"
                aria-label="GitHub"
              >
                <div className="social-icon">
                  <FaGithub />
                </div>
              </a>

              <a
                href="https://www.linkedin.com/in/infinityabhinav"
                target="_blank"
                rel="noopener noreferrer"
                className="social-icon-link"
                aria-label="LinkedIn"
              >
                <div className="social-icon">
                  <FaLinkedin />
                </div>
              </a>

              <a
                href="https://www.instagram.com/avkr5104"
                target="_blank"
                rel="noopener noreferrer"
                className="social-icon-link"
                aria-label="Instagram"
              >
                <div className="social-icon">
                  <FaInstagram />
                </div>
              </a>
            </div>
          </motion.div>
        </div>

        <motion.div
          className="mt-5 pt-4 border-top text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          style={{ borderColor: 'rgba(255, 255, 255, 0.05)' }}
        >
          <p className="mb-0 text-muted">
            &copy; {currentYear} Musafir Music. All rights reserved.
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
