import { Component } from 'react';
import logger from '../../utils/logger';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // You can log the error to an error reporting service here
    logger.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="container py-5 text-center">
          <div className="row justify-content-center">
            <div className="col-md-8">
              <h2 className="mb-4">Something went wrong</h2>
              <div className="alert alert-danger mb-4">
                <p className="mb-0">We're sorry, but an error occurred while rendering this component.</p>
              </div>

              {this.state.error && (
                <div className="card mb-4">
                  <div className="card-header">Error Details</div>
                  <div className="card-body">
                    <p className="text-danger">{this.state.error.toString()}</p>
                  </div>
                </div>
              )}

              <div className="d-flex justify-content-center gap-3">
                <button
                  className="btn btn-primary"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </button>
                <a href="/" className="btn btn-outline-secondary">
                  Go to Home
                </a>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
