import { useState, useEffect, useRef, useCallback } from 'react';

export const useAudioPlayer = (song, isPlaying, pauseSong, resumeSong) => {
  const audioRef = useRef(null);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(() => {
    const savedVolume = localStorage.getItem('audioVolume');
    return savedVolume ? parseFloat(savedVolume) : 0.7;
  });
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [buffered, setBuffered] = useState(0);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [error, setError] = useState(null);

  // Get audio URL from song object with robust fallback
  const getAudioUrl = useCallback(() => {
    if (!song) {
      console.log('No song provided to getAudioUrl');
      return '';
    }

    console.log('Getting audio URL for song:', song.name, 'Song data:', song);

    // Handle different possible audio URL formats
    if (song.audioUrl) {
      console.log('Using song.audioUrl:', song.audioUrl);
      return song.audioUrl;
    }

    if (song.audio_url) {
      console.log('Using song.audio_url:', song.audio_url);
      return song.audio_url;
    }

    if (song.url) {
      console.log('Using song.url:', song.url);
      return song.url;
    }

    // Handle downloadUrl array
    if (song.downloadUrl && Array.isArray(song.downloadUrl) && song.downloadUrl.length > 0) {
      console.log('Available download URLs:', song.downloadUrl);

      // Try to find the best quality URL that actually has a valid URL
      const validUrls = song.downloadUrl.filter(item => item && item.url && item.url.trim() !== '');

      if (validUrls.length === 0) {
        console.error('No valid URLs found in downloadUrl array');
        return '';
      }

      // Prefer higher quality
      const highQuality = validUrls.find(url => url.quality === '320kbps');
      if (highQuality) {
        console.log('Using 320kbps quality:', highQuality.url);
        return highQuality.url;
      }

      const mediumQuality = validUrls.find(url => url.quality === '160kbps');
      if (mediumQuality) {
        console.log('Using 160kbps quality:', mediumQuality.url);
        return mediumQuality.url;
      }

      const firstValid = validUrls[0];
      console.log('Using first valid URL:', firstValid.url);
      return firstValid.url;
    }

    console.error('No valid audio URL found for song:', song.name);

    // Fallback for testing - use a sample audio file
    console.log('Using fallback test audio');
    return 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav';
  }, [song]);

  // Format time helper
  const formatTime = useCallback((time) => {
    if (!time || isNaN(time)) return '0:00';
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Seek to specific time
  const seek = useCallback((time) => {
    if (audioRef.current && !isNaN(time)) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  }, []);

  // Toggle play/pause
  const togglePlay = useCallback(() => {
    if (!audioRef.current) {
      console.error('Audio ref not available');
      return;
    }

    const audio = audioRef.current;

    if (isPlaying) {
      console.log('Pausing audio');
      audio.pause();
      pauseSong();
    } else {
      console.log('Playing audio');
      if (!audio.src) {
        console.error('No audio source available');
        setError('No audio source');
        return;
      }

      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('Play successful');
            resumeSong();
            setError(null);
          })
          .catch((error) => {
            console.error('Play failed:', error);
            setError('Failed to play audio');

            // Try to reload and play again
            setTimeout(() => {
              console.log('Retrying play after error');
              audio.load();
              audio.play()
                .then(() => {
                  console.log('Retry successful');
                  resumeSong();
                  setError(null);
                })
                .catch((retryError) => {
                  console.error('Retry failed:', retryError);
                });
            }, 500);
          });
      }
    }
  }, [isPlaying, pauseSong, resumeSong]);

  // Handle volume changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
    }
    // Save volume to localStorage
    localStorage.setItem('audioVolume', volume.toString());
  }, [volume, isMuted]);

  // Handle playback rate changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.playbackRate = playbackRate;
    }
  }, [playbackRate]);

  // Handle song changes
  useEffect(() => {
    if (!audioRef.current || !song) return;

    const audio = audioRef.current;
    setIsLoading(true);
    setError(null);
    setCurrentTime(0);
    setDuration(0);
    setBuffered(0);

    const audioUrl = getAudioUrl();
    console.log('Loading audio URL:', audioUrl);

    if (!audioUrl) {
      setError('No audio URL available');
      setIsLoading(false);
      return;
    }

    // Reset audio element
    audio.pause();
    audio.currentTime = 0;
    audio.src = audioUrl;
    audio.load();

    const handleLoadStart = () => {
      console.log('Audio load started');
      setIsLoading(true);
    };

    const handleCanPlay = () => {
      console.log('Audio can play');
      setIsLoading(false);
    };

    const handleLoadedMetadata = () => {
      console.log('Audio metadata loaded, duration:', audio.duration);
      setDuration(audio.duration || 0);
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      if (!isNaN(audio.currentTime)) {
        setCurrentTime(audio.currentTime);
      }
    };

    const handleProgress = () => {
      if (audio.buffered.length > 0) {
        const bufferedEnd = audio.buffered.end(audio.buffered.length - 1);
        const duration = audio.duration;
        if (duration > 0) {
          setBuffered((bufferedEnd / duration) * 100);
        }
      }
    };

    const handleError = (e) => {
      console.error('Audio error:', e);
      const errorMessage = e.target?.error?.message || 'Failed to load audio';
      console.error('Detailed error:', e.target?.error);

      // Try to provide more specific error messages
      if (e.target?.error?.code === 4) {
        setError('Audio format not supported');
      } else if (e.target?.error?.code === 3) {
        setError('Audio file corrupted');
      } else if (e.target?.error?.code === 2) {
        setError('Network error loading audio');
      } else {
        setError('Failed to load audio');
      }

      setIsLoading(false);
    };

    const handleEnded = () => {
      console.log('Audio ended');
      setCurrentTime(0);
      pauseSong();
    };

    const handlePlay = () => {
      console.log('Audio play event');
    };

    const handlePause = () => {
      console.log('Audio pause event');
    };

    // Add event listeners
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('progress', handleProgress);
    audio.addEventListener('error', handleError);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);

    // Cleanup
    return () => {
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('progress', handleProgress);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
    };
  }, [song, getAudioUrl, pauseSong]);

  // Sync playing state with audio element
  useEffect(() => {
    if (!audioRef.current) return;

    const audio = audioRef.current;

    if (isPlaying && audio.paused && audio.src) {
      console.log('Syncing play state - starting playback');
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('Play sync successful');
          })
          .catch((error) => {
            console.error('Play sync failed:', error);
            setError('Playback failed');
          });
      }
    } else if (!isPlaying && !audio.paused) {
      console.log('Syncing pause state - pausing playback');
      audio.pause();
    }
  }, [isPlaying]);

  return {
    audioRef,
    duration,
    currentTime,
    volume,
    isMuted,
    isLoading,
    buffered,
    playbackRate,
    error,
    setVolume,
    setIsMuted,
    setPlaybackRate,
    seek,
    togglePlay,
    formatTime,
    getAudioUrl
  };
};
