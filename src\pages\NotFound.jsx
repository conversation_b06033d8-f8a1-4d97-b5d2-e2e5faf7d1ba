import { Link, useLocation } from 'react-router-dom';
import { FaExclamationTriangle, FaHome, FaSearch, FaMusic } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useEffect } from 'react';

const NotFound = () => {
  const location = useLocation();

  // Log the 404 error for debugging
  useEffect(() => {
    console.error('404 Error: Page not found at path:', location.pathname);
  }, [location.pathname]);

  // Check if this is a song page that wasn't found
  const isSongPage = location.pathname.includes('/song/');
  const songId = isSongPage ? location.pathname.split('/song/')[1] : null;

  return (
    <motion.div
      className="text-center py-5"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <FaExclamationTriangle size={64} className="text-warning mb-4" />
      <h1 className="display-4 mb-4">404</h1>
      <h2 className="mb-4">Page Not Found</h2>

      {isSongPage ? (
        <div className="mb-5">
          <p className="lead">
            The song you're looking for couldn't be found or is no longer available.
          </p>
          <p className="text-muted">
            Song ID: {songId}
          </p>
        </div>
      ) : (
        <p className="lead mb-5">
          The page you are looking for doesn't exist or has been moved.
        </p>
      )}

      <div className="d-flex justify-content-center gap-3 flex-wrap">
        <Link to="/" className="btn btn-primary px-4 py-2 d-flex align-items-center">
          <FaHome className="me-2" /> Back to Home
        </Link>

        {isSongPage && (
          <Link to="/search" className="btn btn-outline-light px-4 py-2 d-flex align-items-center">
            <FaSearch className="me-2" /> Search for Music
          </Link>
        )}

        <Link to="/playlists" className="btn btn-outline-light px-4 py-2 d-flex align-items-center">
          <FaMusic className="me-2" /> Your Playlists
        </Link>
      </div>
    </motion.div>
  );
};

export default NotFound;
