import React, { useState, useRef, useEffect } from 'react';
import { Link, NavLink, useNavigate } from 'react-router-dom';
import { FaUser, FaSearch, FaHeadphones, FaList, FaSignOutAlt, FaSignInAlt, FaCog, FaUserCircle, FaTimes, FaBars } from 'react-icons/fa';
import { Dropdown } from 'react-bootstrap';
import { Collapse } from 'bootstrap';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'framer-motion';
import AuthModals from '../auth/AuthModals';

// Custom Dropdown Toggle
const UserDropdownToggle = React.forwardRef(({ children, onClick }, ref) => (
  <motion.div
    className="d-flex align-items-center cursor-pointer"
    ref={ref}
    onClick={(e) => {
      e.preventDefault();
      onClick(e);
    }}
    whileHover={{ scale: 1.05 }}
    style={{
      backgroundColor: 'rgba(157, 78, 221, 0.15)',
      color: 'var(--dark-text)',
      border: 'none',
      borderRadius: '50rem',
      padding: '0.4rem 0.8rem'
    }}
  >
    <FaUserCircle className="me-2" size={16} style={{ color: 'var(--dark-accent)' }} />
    {children}
  </motion.div>
));

UserDropdownToggle.displayName = 'UserDropdownToggle';

const Navbar = () => {
  const { darkMode } = useTheme();
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const [showAuthorTooltip, setShowAuthorTooltip] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [isNavExpanded, setIsNavExpanded] = useState(false);
  const authorRef = useRef(null);
  const navbarRef = useRef(null);
  const navbarTogglerRef = useRef(null);

  const handleLogout = async () => {
    try {
      // Close the navbar if it's open
      closeNavbar();

      console.log("Attempting to log out");
      await logout();
      console.log("Logout successful");
      navigate('/');
    } catch (error) {
      console.error('Failed to log out:', error);
    }
  };

  // Log authentication state for debugging
  useEffect(() => {
    console.log("Navbar auth state:", currentUser ? `User: ${currentUser.email}` : "No user");
  }, [currentUser]);

  // Function to close the navbar
  const closeNavbar = () => {
    if (isNavExpanded && navbarRef.current) {
      const bsCollapse = new Collapse(navbarRef.current);
      bsCollapse.hide();
      setIsNavExpanded(false);
    }
  };

  // Handle clicks outside the navbar to close it
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Skip if navbar is not expanded
      if (!isNavExpanded) return;

      // Check if click is outside navbar and not on the toggler button
      if (
        navbarRef.current &&
        !navbarRef.current.contains(event.target) &&
        navbarTogglerRef.current &&
        !navbarTogglerRef.current.contains(event.target)
      ) {
        // Close the navbar
        closeNavbar();
      }
    };

    // Add event listener
    document.addEventListener('click', handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isNavExpanded]);

  // Track navbar collapse/expand state
  useEffect(() => {
    if (!navbarRef.current) return;

    const handleNavbarShow = () => setIsNavExpanded(true);
    const handleNavbarHide = () => setIsNavExpanded(false);

    navbarRef.current.addEventListener('show.bs.collapse', handleNavbarShow);
    navbarRef.current.addEventListener('hide.bs.collapse', handleNavbarHide);

    return () => {
      if (navbarRef.current) {
        navbarRef.current.removeEventListener('show.bs.collapse', handleNavbarShow);
        navbarRef.current.removeEventListener('hide.bs.collapse', handleNavbarHide);
      }
    };
  }, []);

  return (
    <motion.nav
      className="navbar navbar-expand-lg sticky-top mb-4"
      style={{
        backgroundColor: 'rgba(15, 15, 15, 0.95)',
        backdropFilter: 'blur(10px)',
        boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.05)',
        zIndex: 1040 // Higher than mobile nav but lower than modals
      }}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container py-2">
        <Link className="navbar-brand fw-bold d-flex align-items-center" to="/">
          <FaHeadphones className="me-2" style={{ color: 'var(--dark-accent)' }} />
          <span className="accent-text" style={{ color: 'var(--dark-accent)' }}>Musafir</span>
          <span style={{ color: 'var(--dark-text)' }}>Music</span>
        </Link>

        {/* Only show the toggle button on larger screens where we don't have the bottom nav */}
        <motion.button
          className="navbar-toggler border-0 d-lg-none"
          type="button"
          onClick={() => {
            if (isNavExpanded) {
              // If already expanded, manually close it
              closeNavbar();
            } else {
              // If collapsed, let Bootstrap handle the toggle
              const bsCollapse = new Collapse(navbarRef.current);
              bsCollapse.toggle();
            }
          }}
          ref={navbarTogglerRef}
          whileTap={{ scale: 0.95 }}
          style={{
            color: 'var(--dark-text)',
            backgroundColor: 'rgba(157, 78, 221, 0.1)',
            borderRadius: '0.5rem',
            padding: '0.5rem',
            boxShadow: 'none'
          }}
        >
          {isNavExpanded ? (
            <FaTimes size={20} style={{ color: 'var(--dark-accent)' }} />
          ) : (
            <FaBars size={20} style={{ color: 'var(--dark-text)' }} />
          )}
        </motion.button>

        <div className="collapse navbar-collapse" id="navbarNav" ref={navbarRef}>
          <ul className="navbar-nav ms-auto align-items-center">
            {/* Hide these nav items on mobile since we have the bottom nav */}
            <li className="nav-item mx-1 d-none d-lg-block">
              <NavLink
                className={({ isActive }) =>
                  isActive
                    ? "nav-link active fw-medium px-3 py-2 rounded-pill"
                    : "nav-link px-3 py-2 rounded-pill"
                }
                to="/"
                onClick={closeNavbar}
                style={({ isActive }) => ({
                  backgroundColor: isActive ? 'rgba(157, 78, 221, 0.15)' : 'transparent',
                  color: isActive ? 'var(--dark-accent)' : 'var(--dark-text)',
                  transition: 'all 0.3s ease'
                })}
              >
                Home
              </NavLink>
            </li>

            <li className="nav-item mx-1 d-none d-lg-block">
              <NavLink
                className={({ isActive }) =>
                  isActive
                    ? "nav-link active fw-medium px-3 py-2 rounded-pill"
                    : "nav-link px-3 py-2 rounded-pill"
                }
                to="/search"
                onClick={closeNavbar}
                style={({ isActive }) => ({
                  backgroundColor: isActive ? 'rgba(157, 78, 221, 0.15)' : 'transparent',
                  color: isActive ? 'var(--dark-accent)' : 'var(--dark-text)',
                  transition: 'all 0.3s ease'
                })}
              >
                <FaSearch className="me-1" /> Search
              </NavLink>
            </li>
            <li className="nav-item mx-1 d-none d-lg-block">
              <NavLink
                className={({ isActive }) =>
                  isActive
                    ? "nav-link active fw-medium px-3 py-2 rounded-pill"
                    : "nav-link px-3 py-2 rounded-pill"
                }
                to="/playlists"
                onClick={closeNavbar}
                style={({ isActive }) => ({
                  backgroundColor: isActive ? 'rgba(157, 78, 221, 0.15)' : 'transparent',
                  color: isActive ? 'var(--dark-accent)' : 'var(--dark-text)',
                  transition: 'all 0.3s ease'
                })}
              >
                <FaList className="me-1" /> Playlists
              </NavLink>
            </li>
            <li className="nav-item mx-1">
              <div
                className="nav-link cursor-pointer px-3 py-2 rounded-pill"
                ref={authorRef}
                onClick={closeNavbar}
                onMouseEnter={() => setShowAuthorTooltip(true)}
                onMouseLeave={() => setShowAuthorTooltip(false)}
                style={{ color: 'var(--dark-text)', transition: 'all 0.3s ease' }}
              >
                <FaUser className="me-1" /> About Developer

                {showAuthorTooltip && (
                  <div
                    className={`author-tooltip ${showAuthorTooltip ? 'visible' : ''}`}
                    style={{
                      top: '100%',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      backgroundColor: 'rgba(15, 15, 15, 0.95)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.05)',
                      borderRadius: '0.75rem',
                      width: '300px',
                      padding: '1.25rem'
                    }}
                  >
                    <h5 className="mb-2" style={{ color: 'var(--dark-accent)' }}>Abhinav Kumar Jha</h5>
                    <p className="mb-2">B.Tech CSE Student at IPU with a passion for creating modern web applications.</p>
                    <div className="mb-2">
                      <strong style={{ color: 'var(--dark-accent)' }}>Expertise:</strong>
                      <ul className="mb-0 ps-3 mt-1">
                        <li>Full-stack development with MERN</li>
                        <li>UI/UX design for web applications</li>
                        <li>Data Structures & Algorithms in C++</li>
                      </ul>
                    </div>
                    <p className="mb-0 small text-muted">Connect with me on LinkedIn, GitHub, or Instagram using the links in the footer!</p>
                  </div>
                )}
              </div>
            </li>

            {/* Authentication */}
            <li className="nav-item ms-2">
              {currentUser ? (
                <Dropdown>
                  <Dropdown.Toggle as={UserDropdownToggle} id="user-dropdown">
                    <span className="text-truncate" style={{ maxWidth: '120px', display: 'inline-block' }}>
                      {currentUser.displayName || currentUser.email.split('@')[0]}
                    </span>
                  </Dropdown.Toggle>

                  <Dropdown.Menu
                    align="end"
                    style={{
                      backgroundColor: 'rgba(15, 15, 15, 0.95)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.05)',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                      borderRadius: '0.75rem',
                      marginTop: '0.5rem',
                      padding: '0.5rem'
                    }}
                  >
                    <div className="px-3 py-2 mb-2 border-bottom border-secondary">
                      <div className="d-flex align-items-center">
                        <FaUserCircle size={24} className="me-2" style={{ color: 'var(--dark-accent)' }} />
                        <div>
                          <div className="fw-medium" style={{ color: 'var(--dark-text)' }}>
                            {currentUser.displayName || 'User'}
                          </div>
                          <div className="small text-muted text-truncate" style={{ maxWidth: '180px' }}>
                            {currentUser.email}
                          </div>
                        </div>
                      </div>
                    </div>

                    <Dropdown.Item
                      as={Link}
                      to="/profile"
                      onClick={closeNavbar}
                      style={{
                        color: 'var(--dark-text)',
                        borderRadius: '0.5rem',
                        margin: '0.2rem 0',
                        padding: '0.5rem 1rem'
                      }}
                      className="d-flex align-items-center"
                    >
                      <FaUser className="me-2" size={14} />
                      My Profile
                    </Dropdown.Item>

                    <Dropdown.Item
                      as={Link}
                      to="/playlists"
                      onClick={closeNavbar}
                      style={{
                        color: 'var(--dark-text)',
                        borderRadius: '0.5rem',
                        margin: '0.2rem 0',
                        padding: '0.5rem 1rem'
                      }}
                      className="d-flex align-items-center"
                    >
                      <FaList className="me-2" size={14} />
                      My Playlists
                    </Dropdown.Item>

                    <Dropdown.Divider className="my-2 border-secondary" />

                    <Dropdown.Item
                      onClick={handleLogout}
                      style={{
                        color: '#ff6b6b',
                        borderRadius: '0.5rem',
                        margin: '0.2rem 0',
                        padding: '0.5rem 1rem'
                      }}
                      className="d-flex align-items-center"
                    >
                      <FaSignOutAlt className="me-2" size={14} />
                      Logout
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              ) : (
                <motion.button
                  className="btn btn-sm d-flex align-items-center justify-content-center"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowLoginModal(true)}
                  style={{
                    backgroundColor: 'var(--dark-accent)',
                    color: '#0f0f0f',
                    border: 'none',
                    borderRadius: '50rem',
                    padding: '0.4rem 0.8rem'
                  }}
                >
                  <FaSignInAlt className="me-1" size={12} /> Sign In
                </motion.button>
              )}
            </li>
          </ul>
        </div>
      </div>

      {/* Auth Modals */}
      <AuthModals
        showLogin={showLoginModal}
        showSignup={showSignupModal}
        onHide={() => {
          setShowLoginModal(false);
          setShowSignupModal(false);
        }}
      />
    </motion.nav>
  );
};

export default Navbar;
