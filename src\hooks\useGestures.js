import { useState, useEffect, useRef, useCallback } from 'react';

export const useGestures = (audioRef, onSeek) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragProgress, setDragProgress] = useState(null);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const dragStartTime = useRef(null);
  const wasPlaying = useRef(false);

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  // Handle mouse/touch start on seekbar
  const handleSeekStart = useCallback((event, seekbarElement) => {
    if (!audioRef.current || !seekbarElement) return;

    setIsDragging(true);
    wasPlaying.current = !audioRef.current.paused;
    
    // Pause audio while dragging for better UX
    if (wasPlaying.current) {
      audioRef.current.pause();
    }

    const rect = seekbarElement.getBoundingClientRect();
    const clientX = event.type.includes('touch') ? event.touches[0].clientX : event.clientX;
    const progress = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    
    setDragProgress(progress);
    dragStartTime.current = audioRef.current.duration * progress;
  }, [audioRef]);

  // Handle mouse/touch move during seeking
  const handleSeekMove = useCallback((event, seekbarElement) => {
    if (!isDragging || !audioRef.current || !seekbarElement) return;

    const rect = seekbarElement.getBoundingClientRect();
    const clientX = event.type.includes('touch') ? event.touches[0].clientX : event.clientX;
    const progress = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    
    setDragProgress(progress);
    
    // Update audio current time for real-time preview
    const newTime = audioRef.current.duration * progress;
    if (!isNaN(newTime)) {
      audioRef.current.currentTime = newTime;
    }
  }, [isDragging, audioRef]);

  // Handle mouse/touch end
  const handleSeekEnd = useCallback(() => {
    if (!isDragging || !audioRef.current) return;

    setIsDragging(false);
    
    if (dragProgress !== null) {
      const newTime = audioRef.current.duration * dragProgress;
      onSeek?.(newTime);
      
      // Resume playing if it was playing before
      if (wasPlaying.current) {
        audioRef.current.play().catch(console.error);
      }
    }
    
    setDragProgress(null);
    dragStartTime.current = null;
    wasPlaying.current = false;
  }, [isDragging, dragProgress, audioRef, onSeek]);

  // Touch gesture handlers for swipe navigation
  const handleTouchStart = useCallback((event) => {
    setTouchEnd(null);
    setTouchStart({
      x: event.targetTouches[0].clientX,
      y: event.targetTouches[0].clientY,
      time: Date.now()
    });
  }, []);

  const handleTouchMove = useCallback((event) => {
    setTouchEnd({
      x: event.targetTouches[0].clientX,
      y: event.targetTouches[0].clientY,
      time: Date.now()
    });
  }, []);

  const handleTouchEnd = useCallback((onNext, onPrevious, onTogglePlay) => {
    if (!touchStart || !touchEnd) return;

    const distanceX = touchStart.x - touchEnd.x;
    const distanceY = touchStart.y - touchEnd.y;
    const timeElapsed = touchEnd.time - touchStart.time;
    
    // Check if it's a swipe (not a tap)
    const isSwipe = Math.abs(distanceX) > minSwipeDistance && timeElapsed < 300;
    const isTap = Math.abs(distanceX) < 10 && Math.abs(distanceY) < 10 && timeElapsed < 200;
    
    if (isSwipe) {
      // Horizontal swipe
      if (Math.abs(distanceX) > Math.abs(distanceY)) {
        if (distanceX > 0) {
          // Swipe left - next track
          onNext?.();
        } else {
          // Swipe right - previous track
          onPrevious?.();
        }
      }
    } else if (isTap) {
      // Single tap - toggle play/pause
      onTogglePlay?.();
    }

    // Reset touch state
    setTouchStart(null);
    setTouchEnd(null);
  }, [touchStart, touchEnd, minSwipeDistance]);

  // Pinch to zoom gesture for volume control
  const [pinchStart, setPinchStart] = useState(null);
  
  const handlePinchStart = useCallback((event) => {
    if (event.touches.length === 2) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      setPinchStart(distance);
    }
  }, []);

  const handlePinchMove = useCallback((event, onVolumeChange, currentVolume) => {
    if (event.touches.length === 2 && pinchStart) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      const scale = distance / pinchStart;
      const volumeChange = (scale - 1) * 0.5; // Sensitivity adjustment
      const newVolume = Math.max(0, Math.min(1, currentVolume + volumeChange));
      
      onVolumeChange?.(newVolume);
      setPinchStart(distance); // Update for next calculation
    }
  }, [pinchStart]);

  const handlePinchEnd = useCallback(() => {
    setPinchStart(null);
  }, []);

  // Global mouse event handlers for seeking
  useEffect(() => {
    const handleGlobalMouseMove = (event) => {
      if (isDragging) {
        // Find the seekbar element
        const seekbar = document.querySelector('.seekbar-container');
        if (seekbar) {
          handleSeekMove(event, seekbar);
        }
      }
    };

    const handleGlobalMouseUp = () => {
      if (isDragging) {
        handleSeekEnd();
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.addEventListener('touchmove', handleGlobalMouseMove);
      document.addEventListener('touchend', handleGlobalMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('touchmove', handleGlobalMouseMove);
      document.removeEventListener('touchend', handleGlobalMouseUp);
    };
  }, [isDragging, handleSeekMove, handleSeekEnd]);

  return {
    isDragging,
    dragProgress,
    handleSeekStart,
    handleSeekMove,
    handleSeekEnd,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handlePinchStart,
    handlePinchMove,
    handlePinchEnd
  };
};
