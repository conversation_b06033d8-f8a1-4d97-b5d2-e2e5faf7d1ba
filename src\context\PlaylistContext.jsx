import { createContext, useContext, useState, useEffect } from 'react';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  arrayUnion,
  arrayRemove,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuth } from './AuthContext';

// Create the context
const PlaylistContext = createContext();

// Custom hook to use the playlist context
export const usePlaylist = () => {
  return useContext(PlaylistContext);
};

// Provider component
export const PlaylistProvider = ({ children }) => {
  const [playlists, setPlaylists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { currentUser } = useAuth();

  // Fetch user's playlists
  useEffect(() => {
    const fetchPlaylists = async () => {
      if (!currentUser) {
        console.log("No user logged in, clearing playlists");
        setPlaylists([]);
        setLoading(false);
        return;
      }

      try {
        console.log("Fetching playlists for user:", currentUser.uid);
        setLoading(true);

        // Create a collection reference with a unique name for each user
        // This ensures we don't have permission issues with the security rules
        const userPlaylistsRef = collection(db, `users/${currentUser.uid}/playlists`);
        const querySnapshot = await getDocs(userPlaylistsRef);

        const playlistsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        console.log("Fetched playlists:", playlistsData.length);
        setPlaylists(playlistsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching playlists:', err);
        // Handle the permission error gracefully
        if (err.code === 'permission-denied') {
          console.log('Permission denied. Using local storage as fallback.');
          // Use local storage as a fallback
          const localPlaylists = localStorage.getItem('userPlaylists');
          if (localPlaylists) {
            setPlaylists(JSON.parse(localPlaylists));
          } else {
            setPlaylists([]);
          }
        } else {
          setError('Failed to load playlists');
        }
        setLoading(false);
      }
    };

    fetchPlaylists();
  }, [currentUser]);

  // Create a new playlist
  const createPlaylist = async (name, description = '') => {
    if (!currentUser) {
      console.error("Create playlist attempted without authentication");
      setError('You must be logged in to create a playlist');
      throw new Error('Authentication required');
    }

    try {
      console.log("Creating new playlist:", name);
      setError('');

      // Use the user-specific collection path
      const userPlaylistsRef = collection(db, `users/${currentUser.uid}/playlists`);
      const playlistRef = await addDoc(userPlaylistsRef, {
        name,
        description,
        userId: currentUser.uid,
        songs: [],
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      console.log("Playlist created with ID:", playlistRef.id);

      const newPlaylist = {
        id: playlistRef.id,
        name,
        description,
        userId: currentUser.uid,
        songs: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Update both Firestore and local state
      setPlaylists(prev => {
        const updatedPlaylists = [...prev, newPlaylist];
        // Also store in localStorage as a backup
        localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
        return updatedPlaylists;
      });

      return newPlaylist;
    } catch (err) {
      console.error("Error creating playlist:", err);
      setError(err.message);

      // Try to create locally if Firestore fails
      if (err.code === 'permission-denied') {
        console.log('Permission denied. Creating playlist locally.');
        const newPlaylist = {
          id: Date.now().toString(), // Use timestamp as ID
          name,
          description,
          userId: currentUser.uid,
          songs: [],
          createdAt: new Date(),
          updatedAt: new Date()
        };

        setPlaylists(prev => {
          const updatedPlaylists = [...prev, newPlaylist];
          localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
          return updatedPlaylists;
        });

        return newPlaylist;
      }

      throw err;
    }
  };

  // Update a playlist
  const updatePlaylist = async (playlistId, updates) => {
    if (!currentUser) {
      setError('You must be logged in to update a playlist');
      throw new Error('Authentication required');
    }

    try {
      setError('');
      const playlistRef = doc(db, `users/${currentUser.uid}/playlists`, playlistId);

      // Check if the playlist exists
      const playlistDoc = await getDoc(playlistRef);
      if (!playlistDoc.exists()) {
        setError('Playlist not found');
        throw new Error('Playlist not found');
      }

      await updateDoc(playlistRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });

      // Update both Firestore and local state
      setPlaylists(prev => {
        const updatedPlaylists = prev.map(playlist =>
          playlist.id === playlistId
            ? { ...playlist, ...updates, updatedAt: new Date() }
            : playlist
        );
        // Also update localStorage
        localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
        return updatedPlaylists;
      });
    } catch (err) {
      console.error("Error updating playlist:", err);
      setError(err.message);

      // Try to update locally if Firestore fails
      if (err.code === 'permission-denied') {
        console.log('Permission denied. Updating playlist locally.');
        setPlaylists(prev => {
          const updatedPlaylists = prev.map(playlist =>
            playlist.id === playlistId
              ? { ...playlist, ...updates, updatedAt: new Date() }
              : playlist
          );
          localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
          return updatedPlaylists;
        });
      } else {
        throw err;
      }
    }
  };

  // Delete a playlist
  const deletePlaylist = async (playlistId) => {
    if (!currentUser) {
      setError('You must be logged in to delete a playlist');
      throw new Error('Authentication required');
    }

    try {
      setError('');
      const playlistRef = doc(db, `users/${currentUser.uid}/playlists`, playlistId);

      // Check if the playlist exists
      const playlistDoc = await getDoc(playlistRef);
      if (!playlistDoc.exists()) {
        setError('Playlist not found');
        throw new Error('Playlist not found');
      }

      await deleteDoc(playlistRef);

      // Update both Firestore and local state
      setPlaylists(prev => {
        const updatedPlaylists = prev.filter(playlist => playlist.id !== playlistId);
        // Also update localStorage
        localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
        return updatedPlaylists;
      });
    } catch (err) {
      console.error("Error deleting playlist:", err);
      setError(err.message);

      // Try to delete locally if Firestore fails
      if (err.code === 'permission-denied') {
        console.log('Permission denied. Deleting playlist locally.');
        setPlaylists(prev => {
          const updatedPlaylists = prev.filter(playlist => playlist.id !== playlistId);
          localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
          return updatedPlaylists;
        });
      } else {
        throw err;
      }
    }
  };

  // Add a song to a playlist
  const addSongToPlaylist = async (playlistId, song) => {
    if (!currentUser) {
      setError('You must be logged in to add songs to a playlist');
      throw new Error('Authentication required');
    }

    try {
      setError('');
      const playlistRef = doc(db, `users/${currentUser.uid}/playlists`, playlistId);

      // Check if the playlist exists
      const playlistDoc = await getDoc(playlistRef);
      if (!playlistDoc.exists()) {
        setError('Playlist not found');
        throw new Error('Playlist not found');
      }

      // Check if song already exists in the playlist
      const playlistData = playlistDoc.data();
      const existingSongs = playlistData.songs || [];
      const songExists = existingSongs.some(s => s.id === song.id);

      if (songExists) {
        console.log(`Song ${song.id} already exists in playlist ${playlistId}`);
        setError('This song is already in the playlist');
        return;
      }

      // Add the song to the playlist in Firestore
      await updateDoc(playlistRef, {
        songs: arrayUnion(song),
        updatedAt: serverTimestamp()
      });

      // Update local state
      setPlaylists(prev => {
        const updatedPlaylists = prev.map(playlist => {
          if (playlist.id === playlistId) {
            // Create a new array of songs
            const currentSongs = playlist.songs || [];

            // Check if the song already exists in the array
            if (currentSongs.some(s => s.id === song.id)) {
              // Song already exists, don't add it again
              return playlist;
            }

            // Add the song to the array
            return {
              ...playlist,
              songs: [...currentSongs, song],
              updatedAt: new Date()
            };
          }
          return playlist;
        });

        // Also update localStorage
        localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
        return updatedPlaylists;
      });
    } catch (err) {
      console.error("Error adding song to playlist:", err);
      setError(err.message);

      // Try to add song locally if Firestore fails
      if (err.code === 'permission-denied') {
        console.log('Permission denied. Adding song to playlist locally.');
        setPlaylists(prev => {
          const updatedPlaylists = prev.map(playlist => {
            if (playlist.id === playlistId) {
              // Create a new array of songs
              const currentSongs = playlist.songs || [];

              // Check if the song already exists in the array
              if (currentSongs.some(s => s.id === song.id)) {
                // Song already exists, don't add it again
                return playlist;
              }

              // Add the song to the array
              return {
                ...playlist,
                songs: [...currentSongs, song],
                updatedAt: new Date()
              };
            }
            return playlist;
          });
          localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
          return updatedPlaylists;
        });
      } else {
        throw err;
      }
    }
  };

  // Remove a song from a playlist
  const removeSongFromPlaylist = async (playlistId, songId) => {
    if (!currentUser) {
      setError('You must be logged in to remove songs from a playlist');
      throw new Error('Authentication required');
    }

    try {
      setError('');
      const playlistRef = doc(db, `users/${currentUser.uid}/playlists`, playlistId);

      // Check if the playlist exists
      const playlistDoc = await getDoc(playlistRef);
      if (!playlistDoc.exists()) {
        setError('Playlist not found');
        throw new Error('Playlist not found');
      }

      const playlistData = playlistDoc.data();
      const songToRemove = playlistData.songs && playlistData.songs.find(s => s.id === songId);

      if (!songToRemove) {
        setError('This song is not in the playlist');
        return;
      }

      await updateDoc(playlistRef, {
        songs: arrayRemove(songToRemove),
        updatedAt: serverTimestamp()
      });

      // Update both Firestore and local state
      setPlaylists(prev => {
        const updatedPlaylists = prev.map(playlist =>
          playlist.id === playlistId
            ? {
                ...playlist,
                songs: (playlist.songs || []).filter(s => s.id !== songId),
                updatedAt: new Date()
              }
            : playlist
        );
        // Also update localStorage
        localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
        return updatedPlaylists;
      });
    } catch (err) {
      console.error("Error removing song from playlist:", err);
      setError(err.message);

      // Try to remove song locally if Firestore fails
      if (err.code === 'permission-denied') {
        console.log('Permission denied. Removing song from playlist locally.');
        setPlaylists(prev => {
          const updatedPlaylists = prev.map(playlist =>
            playlist.id === playlistId
              ? {
                  ...playlist,
                  songs: (playlist.songs || []).filter(s => s.id !== songId),
                  updatedAt: new Date()
                }
              : playlist
          );
          localStorage.setItem('userPlaylists', JSON.stringify(updatedPlaylists));
          return updatedPlaylists;
        });
      } else {
        throw err;
      }
    }
  };

  // Get or create the "Liked Songs" playlist
  const getLikedSongsPlaylist = async () => {
    if (!currentUser) {
      setError('You must be logged in to manage liked songs');
      throw new Error('Authentication required');
    }

    try {
      // Check if "Liked Songs" playlist already exists
      let likedPlaylist = playlists.find(p => p.name === "Liked Songs");

      if (likedPlaylist) {
        console.log("Found existing Liked Songs playlist:", likedPlaylist.id);
        return likedPlaylist;
      }

      // Double-check by fetching from Firestore directly
      try {
        const userPlaylistsRef = collection(db, `users/${currentUser.uid}/playlists`);
        const q = query(userPlaylistsRef, where('name', '==', 'Liked Songs'));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          const doc = querySnapshot.docs[0];
          likedPlaylist = {
            id: doc.id,
            ...doc.data()
          };
          console.log("Found Liked Songs playlist in Firestore:", likedPlaylist.id);

          // Update the playlists state to include this playlist
          setPlaylists(prevPlaylists => {
            // Check if it's already in the array
            if (!prevPlaylists.some(p => p.id === likedPlaylist.id)) {
              return [...prevPlaylists, likedPlaylist];
            }
            return prevPlaylists;
          });

          return likedPlaylist;
        }
      } catch (fetchErr) {
        console.error("Error fetching Liked Songs playlist:", fetchErr);
        // Continue to create a new playlist
      }

      // Create "Liked Songs" playlist if it doesn't exist
      console.log("Creating Liked Songs playlist");
      const newPlaylist = await createPlaylist("Liked Songs", "Songs you've liked");
      return newPlaylist;
    } catch (err) {
      console.error("Error getting/creating Liked Songs playlist:", err);
      setError(err.message);
      throw err;
    }
  };

  // Add a song to Liked Songs playlist
  const addToLikedSongs = async (song) => {
    if (!currentUser) {
      console.log("User not logged in, can't add to Liked Songs");
      return;
    }

    try {
      const likedPlaylist = await getLikedSongsPlaylist();

      // Check if song is already in the playlist
      if (likedPlaylist.songs && likedPlaylist.songs.some(s => s.id === song.id)) {
        console.log("Song already in Liked Songs playlist");
        return;
      }

      // Add the song to the playlist
      await addSongToPlaylist(likedPlaylist.id, song);
      console.log("Song added to Liked Songs playlist:", song.name);

      // Update the playlists state to reflect the change immediately
      setPlaylists(prevPlaylists => {
        return prevPlaylists.map(p => {
          if (p.id === likedPlaylist.id) {
            // Create a new array of songs with the new song added
            const updatedSongs = [...(p.songs || [])];
            // Only add if not already present
            if (!updatedSongs.some(s => s.id === song.id)) {
              updatedSongs.push(song);
            }
            return { ...p, songs: updatedSongs };
          }
          return p;
        });
      });
    } catch (err) {
      console.error("Error adding song to Liked Songs:", err);
      // Don't throw error to prevent disrupting the like functionality
    }
  };

  // Remove a song from Liked Songs playlist
  const removeFromLikedSongs = async (songId) => {
    if (!currentUser) {
      console.log("User not logged in, can't remove from Liked Songs");
      return;
    }

    try {
      const likedPlaylist = await getLikedSongsPlaylist();

      // Check if song is in the playlist
      if (!likedPlaylist.songs || !likedPlaylist.songs.some(s => s.id === songId)) {
        console.log("Song not in Liked Songs playlist");
        return;
      }

      // Remove the song from the playlist
      await removeSongFromPlaylist(likedPlaylist.id, songId);
      console.log("Song removed from Liked Songs playlist");

      // Update the playlists state to reflect the change immediately
      setPlaylists(prevPlaylists => {
        return prevPlaylists.map(p => {
          if (p.id === likedPlaylist.id) {
            // Create a new array of songs without the removed song
            const updatedSongs = (p.songs || []).filter(s => s.id !== songId);
            return { ...p, songs: updatedSongs };
          }
          return p;
        });
      });
    } catch (err) {
      console.error("Error removing song from Liked Songs:", err);
      // Don't throw error to prevent disrupting the unlike functionality
    }
  };

  // Context value
  const value = {
    playlists,
    loading,
    error,
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    addSongToPlaylist,
    removeSongFromPlaylist,
    getLikedSongsPlaylist,
    addToLikedSongs,
    removeFromLikedSongs
  };

  return (
    <PlaylistContext.Provider value={value}>
      {children}
    </PlaylistContext.Provider>
  );
};

export default PlaylistContext;
