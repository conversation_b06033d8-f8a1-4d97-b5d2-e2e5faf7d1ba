import { Link, useNavigate } from 'react-router-dom';
import { FaPlay } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useSong } from '../../context/SongContext';

const FeaturedSection = ({ song }) => {
  const { playSong } = useSong();
  const navigate = useNavigate();

  if (!song) return null;

  // Get high quality image
  const getImage = () => {
    if (!song.image || song.image.length === 0) {
      return 'https://via.placeholder.com/1200x400';
    }

    // Find the highest quality image
    const highQualityImage = song.image.find(img => img.quality === '500x500');
    if (highQualityImage) return highQualityImage.url;

    return song.image[song.image.length - 1].url;
  };

  // Get artist names
  const getArtists = () => {
    if (song.artists && song.artists.primary) {
      return song.artists.primary.map(artist => artist.name).join(', ');
    }
    return '';
  };

  return (
    <motion.div
      className="featured-section mb-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="position-relative rounded-4 overflow-hidden" style={{
        boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
        border: '1px solid rgba(255, 255, 255, 0.05)'
      }}>
        <img
          src={getImage()}
          alt={song.name}
          className="img-fluid w-100"
          style={{
            height: '300px',
            objectFit: 'contain',
            objectPosition: 'center',
            backgroundColor: 'rgba(15, 15, 15, 0.9)',
            filter: 'brightness(0.9)'
          }}
        />

        {/* Gradient overlay */}
        <div className="position-absolute top-0 start-0 w-100 h-100"
          style={{
            background: 'linear-gradient(to right, rgba(15, 15, 15, 0.9) 0%, rgba(15, 15, 15, 0.6) 50%, rgba(15, 15, 15, 0.3) 100%)',
            zIndex: 1
          }}
        ></div>

        <div className="featured-overlay" style={{ zIndex: 2 }}>
          <div className="container">
            <div className="row">
              <div className="col-md-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                >
                  <span className="badge mb-2 px-2 py-1 small" style={{
                    backgroundColor: 'rgba(0, 255, 209, 0.2)',
                    color: 'var(--dark-accent)',
                    backdropFilter: 'blur(4px)',
                    border: '1px solid rgba(0, 255, 209, 0.1)'
                  }}>
                    Featured Hindi Track
                  </span>
                  <h3 className="fw-bold text-white mb-1">{song.name}</h3>
                  <p className="text-white-50 mb-3 small">{getArtists()}</p>

                  <div className="d-flex gap-2">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="btn rounded-pill px-3 py-1"
                      style={{
                        backgroundColor: 'var(--dark-accent)',
                        color: '#0f0f0f',
                        fontWeight: '600',
                        boxShadow: '0 4px 10px rgba(0, 255, 209, 0.3)',
                        fontSize: '0.85rem'
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Featured Play button clicked for song:', song.name);
                        // First play the song, then navigate to the song page
                        playSong(song);
                        navigate(`/song/${song.id}`);
                      }}
                    >
                      <FaPlay className="me-1" /> Play Now
                    </motion.button>

                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link
                        to={`/song/${song.id}`}
                        className="btn rounded-pill px-3 py-1"
                        style={{
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          backdropFilter: 'blur(4px)',
                          color: 'white',
                          border: '1px solid rgba(255, 255, 255, 0.1)',
                          fontSize: '0.85rem'
                        }}
                      >
                        View Details
                      </Link>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default FeaturedSection;
