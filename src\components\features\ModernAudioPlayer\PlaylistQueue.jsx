import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaPlay, FaPause, FaMusic, FaTrash, FaGripVertical } from 'react-icons/fa';

const PlaylistQueue = ({ playlist, currentIndex, onClose, onSongSelect, onRemoveFromQueue, onReorderQueue }) => {
  const [draggedItem, setDraggedItem] = useState(null);
  const [dragOverIndex, setDragOverIndex] = useState(null);

  // Handle drag start
  const handleDragStart = (e, index) => {
    setDraggedItem(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e, index) => {
    e.preventDefault();
    setDragOverIndex(index);
  };

  // Handle drop
  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    
    if (draggedItem !== null && draggedItem !== dropIndex) {
      onReorderQueue?.(draggedItem, dropIndex);
    }
    
    setDraggedItem(null);
    setDragOverIndex(null);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOverIndex(null);
  };

  // Get song image
  const getSongImage = (song) => {
    return song?.image || song?.thumbnail || song?.albumArt || null;
  };

  // Truncate text
  const truncateText = (text, maxLength = 30) => {
    if (!text) return '';
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  if (!playlist || !playlist.songs || playlist.songs.length === 0) {
    return (
      <motion.div
        className="playlist-queue empty"
        initial={{ opacity: 0, x: 300 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 300 }}
        transition={{ duration: 0.3 }}
      >
        <div className="queue-header">
          <h3>Queue</h3>
          <button className="close-btn" onClick={onClose}>
            <FaTimes />
          </button>
        </div>
        <div className="empty-queue">
          <FaMusic />
          <p>No songs in queue</p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="playlist-queue"
      initial={{ opacity: 0, x: 300 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 300 }}
      transition={{ duration: 0.3 }}
    >
      {/* Queue header */}
      <div className="queue-header">
        <div className="queue-info">
          <h3>Queue</h3>
          <span className="queue-count">{playlist.songs.length} songs</span>
        </div>
        <button className="close-btn" onClick={onClose} aria-label="Close queue">
          <FaTimes />
        </button>
      </div>

      {/* Queue list */}
      <div className="queue-list">
        <AnimatePresence>
          {playlist.songs.map((song, index) => (
            <motion.div
              key={`${song.id}-${index}`}
              className={`queue-item ${index === currentIndex ? 'current' : ''} ${
                draggedItem === index ? 'dragging' : ''
              } ${dragOverIndex === index ? 'drag-over' : ''}`}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={(e) => handleDragOver(e, index)}
              onDrop={(e) => handleDrop(e, index)}
              onDragEnd={handleDragEnd}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2, delay: index * 0.05 }}
              layout
            >
              {/* Drag handle */}
              <div className="drag-handle">
                <FaGripVertical />
              </div>

              {/* Song artwork */}
              <div className="queue-item-artwork">
                {getSongImage(song) ? (
                  <img
                    src={getSongImage(song)}
                    alt={song.name}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                ) : null}
                <div className="artwork-placeholder" style={{ display: getSongImage(song) ? 'none' : 'flex' }}>
                  <FaMusic />
                </div>
                
                {/* Current playing indicator */}
                {index === currentIndex && (
                  <div className="playing-indicator">
                    <motion.div
                      className="playing-bars"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                    >
                      {[...Array(3)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="bar"
                          animate={{
                            scaleY: [0.3, 1, 0.3],
                          }}
                          transition={{
                            duration: 0.8,
                            repeat: Infinity,
                            delay: i * 0.1,
                          }}
                        />
                      ))}
                    </motion.div>
                  </div>
                )}
              </div>

              {/* Song info */}
              <div className="queue-item-info" onClick={() => onSongSelect?.(song, index)}>
                <div className="song-details">
                  <h4 className="song-title" title={song.name}>
                    {truncateText(song.name)}
                  </h4>
                  <p className="song-artist" title={song.artist || song.primaryArtists}>
                    {truncateText(song.artist || song.primaryArtists || 'Unknown Artist')}
                  </p>
                </div>
                
                {song.duration && (
                  <div className="song-duration">
                    {song.duration}
                  </div>
                )}
              </div>

              {/* Queue item actions */}
              <div className="queue-item-actions">
                <button
                  className="action-btn play-btn"
                  onClick={() => onSongSelect?.(song, index)}
                  aria-label={index === currentIndex ? "Currently playing" : "Play song"}
                >
                  {index === currentIndex ? <FaPause /> : <FaPlay />}
                </button>
                
                <button
                  className="action-btn remove-btn"
                  onClick={() => onRemoveFromQueue?.(index)}
                  aria-label="Remove from queue"
                >
                  <FaTrash />
                </button>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Queue actions */}
      <div className="queue-actions">
        <button className="queue-action-btn clear-btn">
          Clear Queue
        </button>
        <button className="queue-action-btn shuffle-btn">
          Shuffle Queue
        </button>
      </div>
    </motion.div>
  );
};

export default PlaylistQueue;
