import { useState, useEffect, useRef } from 'react';
import { FaSearch } from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';
import { motion } from 'framer-motion';
import { searchSongs } from '../../services/api';
import SuggestionsPortal from './SuggestionsPortal';

const SearchBar = ({ onSearch, placeholder = "Search for songs, artists, or albums..." }) => {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recentSearches, setRecentSearches] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const searchRef = useRef(null);
  const { darkMode } = useTheme();

  // Load recent searches from localStorage
  useEffect(() => {
    const savedSearches = localStorage.getItem('recentSearches');
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches).slice(0, 3)); // Only keep last 3 searches
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = (searchTerm) => {
    const updatedSearches = [
      searchTerm,
      ...recentSearches.filter(term => term !== searchTerm)
    ].slice(0, 3); // Only keep last 3 searches

    setRecentSearches(updatedSearches);
    localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
  };

  // Handle clicks outside the search component
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch suggestions when the user types
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (query.trim().length < 2) {
        setSuggestions([]);
        return;
      }

      try {
        setIsLoading(true);
        const response = await searchSongs(query.trim(), 0, 5);
        if (response.success && response.data && response.data.results) {
          setSuggestions(response.data.results);
        }
      } catch (error) {
        console.error('Error fetching suggestions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(() => {
      if (query.trim()) {
        fetchSuggestions();
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [query]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim()) {
      saveRecentSearch(query.trim());
      onSearch(query.trim());
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    console.log('Suggestion clicked:', suggestion.name);
    setQuery(suggestion.name);
    saveRecentSearch(suggestion.name);
    onSearch(suggestion.name);
    setShowSuggestions(false);
  };

  const handleRecentSearchClick = (term) => {
    console.log('Recent search clicked:', term);
    setQuery(term);
    saveRecentSearch(term);
    onSearch(term);
    setShowSuggestions(false);
  };

  // Function to remove a recent search
  const removeRecentSearch = (e, termToRemove) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent triggering the parent click

    const updatedSearches = recentSearches.filter(term => term !== termToRemove);
    setRecentSearches(updatedSearches);
    localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
  };

  // Function to remove a suggestion from the list
  const removeSuggestion = (e, suggestionId) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent triggering the parent click

    // Filter out the suggestion with the matching ID
    const updatedSuggestions = suggestions.filter(suggestion => suggestion.id !== suggestionId);
    setSuggestions(updatedSuggestions);
  };

  // Function to highlight matching text
  const highlightMatch = (text, query) => {
    if (!query.trim() || !text) return text;

    try {
      const regex = new RegExp(`(${query.trim()})`, 'gi');
      const parts = text.split(regex);

      return parts.map((part, index) =>
        regex.test(part) ?
          <span key={index} style={{ color: 'var(--dark-accent)', fontWeight: 'bold' }}>{part}</span> :
          part
      );
    } catch (e) {
      // If regex fails (e.g., with special characters), return the original text
      return text;
    }
  };

  // Create a ref for the input element
  const inputRef = useRef(null);

  return (
    <div className="position-relative w-100" ref={searchRef} style={{ zIndex: 1000 }}>
      <motion.form
        onSubmit={handleSubmit}
        className="mb-2 w-100"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        style={{ position: 'relative', zIndex: 1000, width: '100%' }}
        onClick={(e) => {
          // Keep the form click from closing suggestions
          e.stopPropagation();
        }}
      >
        <div className="input-group custom-search-bar">
          <input
            ref={inputRef}
            type="text"
            className="form-control"
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => {
              setIsFocused(true);
              setShowSuggestions(true);
            }}
            onClick={(e) => {
              // Prevent input click from closing suggestions
              e.stopPropagation();
            }}
            aria-label="Search"
            style={{
              height: window.innerWidth < 768 ? '45px' : '48px',
              fontSize: window.innerWidth < 768 ? '0.9rem' : '0.95rem'
            }}
          />
          <motion.button
            className="btn search-button"
            type="submit"
            disabled={!query.trim()}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              height: window.innerWidth < 768 ? '45px' : '48px',
              minWidth: window.innerWidth < 768 ? '100px' : 'auto'
            }}
          >
            <FaSearch className="search-icon" style={{
              color: '#ffffff',
              marginRight: window.innerWidth < 768 ? '0.5rem' : '0.75rem',
              fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'
            }} />
            <span className="search-text" style={{
              color: '#ffffff',
              fontSize: window.innerWidth < 768 ? '0.85rem' : '0.9rem',
              fontWeight: window.innerWidth < 768 ? '600' : '500'
            }}>Search</span>
          </motion.button>
        </div>
      </motion.form>

      {/* Use the Portal for suggestions */}
      <SuggestionsPortal
        isVisible={showSuggestions && isFocused && (
          query.trim().length >= 2 ||
          (query.trim().length < 2 && recentSearches.length > 0)
        )}
        suggestions={suggestions}
        recentSearches={recentSearches}
        query={query}
        inputRef={inputRef}
        onSuggestionClick={handleSuggestionClick}
        onRecentSearchClick={handleRecentSearchClick}
        onRemoveSuggestion={(id) => {
          const updatedSuggestions = suggestions.filter(suggestion => suggestion.id !== id);
          setSuggestions(updatedSuggestions);
        }}
        onRemoveRecentSearch={(term) => {
          const updatedSearches = recentSearches.filter(t => t !== term);
          setRecentSearches(updatedSearches);
          localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
        }}
        onClose={() => setShowSuggestions(false)}
        isLoading={isLoading}
        highlightMatch={highlightMatch}
      />
    </div>
  );
};

export default SearchBar;
