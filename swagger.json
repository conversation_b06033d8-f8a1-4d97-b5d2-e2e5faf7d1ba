{"openapi": "3.1.0", "info": {"version": "1.0.0", "title": "JioSaavn API", "description": "# Introduction \n        \nJioSaavn API, accessible at [saavn.dev](https://saavn.dev), is an unofficial API that allows users to download high-quality songs from [JioSaavn](https://jiosaavn.com). \n        It offers a fast, reliable, and easy-to-use API for developers. \n"}, "servers": [{"url": "https://saavn.dev", "description": "Current environment"}], "components": {"schemas": {}, "parameters": {}}, "paths": {"/api/search": {"get": {"tags": ["Search"], "summary": "Global search", "description": "Search for songs, albums, artists, and playlists based on the provided query string.", "operationId": "globalSearch", "parameters": [{"schema": {"type": "string", "description": "Search query", "title": "Search query", "example": "Imagine Dragons"}, "required": true, "name": "query", "in": "query"}], "responses": {"200": {"description": "Successful global search", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the search was successful", "example": true}, "data": {"type": "object", "properties": {"albums": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "artist": {"type": "string"}, "url": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "year": {"type": "string"}, "language": {"type": "string"}, "songIds": {"type": "string"}}, "required": ["id", "title", "image", "artist", "url", "type", "description", "year", "language", "songIds"]}}, "position": {"type": "number"}}, "required": ["results", "position"]}, "songs": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "album": {"type": "string"}, "url": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "primaryArtists": {"type": "string"}, "singers": {"type": "string"}, "language": {"type": "string"}}, "required": ["id", "title", "image", "album", "url", "type", "description", "primaryArtists", "singers", "language"]}}, "position": {"type": "number"}}, "required": ["results", "position"]}, "artists": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "type": {"type": "string"}, "description": {"type": "string"}, "position": {"type": "number"}}, "required": ["id", "title", "image", "type", "description", "position"]}}, "position": {"type": "number"}}, "required": ["results", "position"]}, "playlists": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}, "language": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}}, "required": ["id", "title", "image", "url", "language", "type", "description"]}}, "position": {"type": "number"}}, "required": ["results", "position"]}, "topQuery": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "album": {"type": "string"}, "url": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "primaryArtists": {"type": "string"}, "singers": {"type": "string"}, "language": {"type": "string"}}, "required": ["id", "title", "image", "album", "url", "type", "description", "primaryArtists", "singers", "language"]}}, "position": {"type": "number"}}, "required": ["results", "position"]}}, "required": ["albums", "songs", "artists", "playlists", "top<PERSON>uery"], "description": "Search results including songs, albums, artists, and playlists"}}, "required": ["success", "data"]}}}}}}}, "/api/search/songs": {"get": {"tags": ["Search"], "summary": "Search for songs", "description": "Search for songs based on the provided query", "operationId": "searchSongs", "parameters": [{"schema": {"type": "string", "description": "Search query for songs", "title": "Search query", "example": "Believer"}, "required": true, "name": "query", "in": "query"}, {"schema": {"type": "integer", "description": "The page number of the search results to retrieve", "title": "Page Number", "example": "0", "default": "0"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "integer", "description": "Number of search results per page", "title": "Limit", "example": "10", "default": "10"}, "required": false, "name": "limit", "in": "query"}], "responses": {"200": {"description": "Successful response with song search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the song search was successful", "example": true}, "data": {"type": "object", "properties": {"total": {"type": "number"}, "start": {"type": "number"}, "results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}}, "required": ["total", "start", "results"], "description": "Search results for songs"}}, "required": ["success", "data"]}}}}}}}, "/api/search/albums": {"get": {"tags": ["Search"], "summary": "Search for albums", "description": "Search for albums based on the provided query", "operationId": "searchAlbums", "parameters": [{"schema": {"type": "string", "description": "Search query for albums", "example": "Evolve"}, "required": true, "name": "query", "in": "query"}, {"schema": {"type": "integer", "description": "The page number of the search results to retrieve", "example": "0", "default": "0"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "integer", "description": "The number of search results per page", "example": "10", "default": "10"}, "required": false, "name": "limit", "in": "query"}], "responses": {"200": {"description": "Successful response with album search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the album search was successful", "example": true}, "data": {"type": "object", "properties": {"total": {"type": "number"}, "start": {"type": "number"}, "results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "year": {"type": ["number", "null"]}, "type": {"type": "string"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "explicitContent": {"type": "boolean"}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "url": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "description", "year", "type", "playCount", "language", "explicitContent", "artists", "url", "image"]}}}, "required": ["total", "start", "results"], "description": "Search results for albums"}}, "required": ["success", "data"]}}}}}}}, "/api/search/artists": {"get": {"tags": ["Search"], "summary": "Search for artists", "description": "Search for artists based on the provided query", "operationId": "searchArtists", "parameters": [{"schema": {"type": "string", "description": "Search query for artists", "title": "Search query", "example": "<PERSON>"}, "required": true, "name": "query", "in": "query"}, {"schema": {"type": "integer", "description": "The page number of the search results to retrieve", "title": "Page Number", "example": "0", "default": "0"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "integer", "description": "Number of search results per page", "title": "Limit", "example": "10", "default": "10"}, "required": false, "name": "limit", "in": "query"}], "responses": {"200": {"description": "Successful response with artist search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the artist search was successful", "example": true}, "data": {"type": "object", "properties": {"total": {"type": "number"}, "start": {"type": "number"}, "results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["total", "start", "results"], "description": "Search results for artists"}}, "required": ["success", "data"]}}}}}}}, "/api/search/playlists": {"get": {"tags": ["Search"], "summary": "Search for playlists", "description": "Search for playlists based on the provided query", "operationId": "searchPlaylists", "parameters": [{"schema": {"type": "string", "description": "Search query for playlists", "title": "Search query", "example": "Indie"}, "required": true, "name": "query", "in": "query"}, {"schema": {"type": "integer", "description": "The page number of the search results to retrieve", "title": "Page Number", "example": "0", "default": "0"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "integer", "description": "Number of search results per page", "title": "Limit", "example": "10", "default": "10"}, "required": false, "name": "limit", "in": "query"}], "responses": {"200": {"description": "Successful response with playlist search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the playlist search was successful", "example": true}, "data": {"type": "object", "properties": {"total": {"type": "number"}, "start": {"type": "number"}, "results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}, "songCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "explicitContent": {"type": "boolean"}}, "required": ["id", "name", "type", "image", "url", "songCount", "language", "explicitContent"]}}}, "required": ["total", "start", "results"], "description": "Search results for playlist"}}, "required": ["success", "data"]}}}}}}}, "/api/songs": {"get": {"tags": ["Songs"], "summary": "Retrieve songs by ID or link", "description": "Retrieve songs by a comma-separated list of IDs or by a direct link to the song on JioSaavn.", "operationId": "getSongByIdsOrLink", "parameters": [{"schema": {"type": "string", "description": "Comma-separated list of song IDs", "title": "Song IDs", "example": "3IoDK8qI,4IoDK8qI,5IoDK8qI"}, "required": false, "name": "ids", "in": "query"}, {"schema": {"type": "string", "description": "A direct link to the song on JioSaavn", "title": "<PERSON>", "example": "https://www.jiosaavn.com/song/houdini/OgwhbhtDRwM"}, "required": false, "name": "link", "in": "query"}], "responses": {"200": {"description": "Successful response with song details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the request was successful", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}, "description": "Array of song details", "title": "Song Details"}}, "required": ["success", "data"]}}}}, "400": {"description": "Bad request when query parameters are missing or invalid"}, "404": {"description": "Song not found with the given ID or link"}}}}, "/api/songs/{id}": {"get": {"tags": ["Songs"], "summary": "Retrieve song by <PERSON>", "description": "Retrieve a song by its ID. Optionally, include lyrics in the response.", "operationId": "getSongById", "parameters": [{"schema": {"type": "string", "description": "ID of the song to retrieve", "title": "Song ID", "example": "3IoDK8qI"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "Successful response with song details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the request was successful", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}, "description": "Array of songs"}}, "required": ["success", "data"]}}}}, "400": {"description": "Bad request when query parameters are missing or invalid"}, "404": {"description": "Song not found for the given ID"}}}}, "/api/songs/{id}/suggestions": {"get": {"tags": ["Songs"], "summary": "Retrieve song suggestions", "description": "Retrieve song suggestions based on the given song ID. This can be used to get similar songs to the one provided for infinite playback.", "operationId": "getSongSuggestions", "parameters": [{"schema": {"type": "string", "description": "ID of the song to retrieve suggestions for", "example": "yDeAS8Eh"}, "required": true, "name": "id", "in": "path"}, {"schema": {"type": "number", "description": "Limit the number of suggestions to retrieve", "title": "Limit", "example": "10", "default": "10"}, "required": false, "name": "limit", "in": "query"}], "responses": {"200": {"description": "Successful response with song suggestions", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the request was successful", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}, "description": "Array of song suggestions"}}, "required": ["success", "data"]}}}}}}}, "/api/albums": {"get": {"tags": ["Album"], "summary": "Retrieve an album by ID or link", "description": "Retrieve an album by providing either an ID or a direct link to the album on JioSaavn.", "operationId": "getAlbumByIdOrLink", "parameters": [{"schema": {"type": "string", "description": "The unique ID of the album", "title": "Album ID", "example": "23241654", "default": "23241654"}, "required": false, "name": "id", "in": "query"}, {"schema": {"type": "string", "description": "A direct link to the album on JioSaavn", "title": "Album Link", "example": "https://www.jiosaavn.com/album/future-nostalgia/ITIyo-GDr7A_", "default": "https://www.jiosaavn.com/album/future-nostalgia/ITIyo-GDr7A_"}, "required": false, "name": "link", "in": "query"}], "responses": {"200": {"description": "Successful response with album details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates the success status of the request.", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "year": {"type": ["number", "null"]}, "type": {"type": "string"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "explicitContent": {"type": "boolean"}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "songCount": {"type": ["number", "null"]}, "url": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "songs": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}}, "required": ["id", "name", "description", "year", "type", "playCount", "language", "explicitContent", "artists", "songCount", "url", "image", "songs"], "description": "The detailed information of the album.", "title": "Album Details"}}, "required": ["success", "data"]}}}}, "400": {"description": "Bad request due to missing or invalid query parameters."}, "404": {"description": "The album could not be found with the provided ID or link."}}}}, "/api/artists": {"get": {"tags": ["Artists"], "summary": "Retrieve artists by ID or link", "description": "Retrieve artists by ID or by a direct artist link.", "operationId": "getArtistByIdOrLink", "parameters": [{"schema": {"type": "string", "description": "Artist ID", "title": "Artist ID", "example": "1274170"}, "required": false, "name": "id", "in": "query"}, {"schema": {"type": "string", "description": "A direct link to the artist on JioSaavn", "title": "Artist Link", "example": "https://www.jiosaavn.com/artist/dua-lipa-songs/r-OWIKgpX2I_"}, "required": false, "name": "link", "in": "query"}, {"schema": {"type": "number", "description": "page number", "title": "Page number", "example": "1"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "number", "description": "Number of songs to fetch", "title": "Song count", "example": "10"}, "required": false, "name": "songCount", "in": "query"}, {"schema": {"type": "number", "description": "Number of albums to fetch", "title": "Album count", "example": "10"}, "required": false, "name": "albumCount", "in": "query"}, {"schema": {"type": "string", "description": "sort by", "title": "Sort by", "example": "popularity"}, "required": false, "name": "sortBy", "in": "query"}, {"schema": {"type": "string", "description": "sort order", "title": "Sort order", "example": "desc", "default": "desc"}, "required": false, "name": "sortOrder", "in": "query"}], "responses": {"200": {"description": "Successful response with artist details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the request was successful", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "followerCount": {"type": ["number", "null"]}, "fanCount": {"type": ["string", "null"]}, "isVerified": {"type": ["boolean", "null"]}, "dominantLanguage": {"type": ["string", "null"]}, "dominantType": {"type": ["string", "null"]}, "bio": {"type": ["array", "null"], "items": {"type": "object", "properties": {"text": {"type": ["string", "null"]}, "title": {"type": ["string", "null"]}, "sequence": {"type": ["number", "null"]}}, "required": ["text", "title", "sequence"]}}, "dob": {"type": ["string", "null"]}, "fb": {"type": ["string", "null"]}, "twitter": {"type": ["string", "null"]}, "wiki": {"type": ["string", "null"]}, "availableLanguages": {"type": "array", "items": {"type": "string"}}, "isRadioPresent": {"type": ["boolean", "null"]}, "topSongs": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}, "topAlbums": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "year": {"type": ["number", "null"]}, "type": {"type": "string"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "explicitContent": {"type": "boolean"}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "songCount": {"type": ["number", "null"]}, "url": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "songs": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}}, "required": ["id", "name", "description", "year", "type", "playCount", "language", "explicitContent", "artists", "songCount", "url", "image", "songs"]}}, "singles": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}, "similarArtists": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "languages": {"type": ["object", "null"], "additionalProperties": {"type": "string"}}, "wiki": {"type": "string"}, "dob": {"type": "string"}, "fb": {"type": "string"}, "twitter": {"type": "string"}, "isRadioPresent": {"type": "boolean"}, "type": {"type": "string"}, "dominantType": {"type": "string"}, "aka": {"type": "string"}, "bio": {"type": ["string", "null"]}, "similarArtists": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}}}, "required": ["id", "name", "url", "image", "languages", "wiki", "dob", "fb", "twitter", "isRadioPresent", "type", "dominantType", "aka", "bio", "similarArtists"]}}}, "required": ["id", "name", "url", "type", "image", "followerCount", "fanCount", "isVerified", "dominantLanguage", "dominantType", "bio", "dob", "fb", "twitter", "wiki", "availableLanguages", "isRadioPresent", "topSongs", "topAlbums", "singles", "similarArtists"], "description": "Artist details"}}, "required": ["success", "data"]}}}}}}}, "/api/artists/{id}": {"get": {"tags": ["Artists"], "summary": "Retrieve artist by ID", "description": "Retrieve artist by ID", "operationId": "getArtistById", "parameters": [{"schema": {"type": "string", "description": "ID of the artist to retrieve", "title": "Artist ID", "example": "1274170"}, "required": true, "name": "id", "in": "path"}, {"schema": {"type": "integer", "description": "The page number of the results to retrieve", "title": "Page number", "example": "0"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "integer", "description": "The number of songs to retrieve for the artist", "title": "Song count", "example": "10"}, "required": false, "name": "songCount", "in": "query"}, {"schema": {"type": "integer", "description": "The number of albums to retrieve for the artist", "title": "Album count", "example": "10"}, "required": false, "name": "albumCount", "in": "query"}, {"schema": {"type": "string", "description": "The field to sort the results by", "title": "Sort by", "example": "popularity", "enum": ["popularity", "latest", "alphabetical"]}, "required": false, "name": "sortBy", "in": "query"}, {"schema": {"type": "string", "description": "The order to sort the results by", "title": "Sort order", "example": "desc", "enum": ["asc", "desc"]}, "required": false, "name": "sortOrder", "in": "query"}], "responses": {"200": {"description": "Successful response with artist details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the request was successful", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "followerCount": {"type": ["number", "null"]}, "fanCount": {"type": ["string", "null"]}, "isVerified": {"type": ["boolean", "null"]}, "dominantLanguage": {"type": ["string", "null"]}, "dominantType": {"type": ["string", "null"]}, "bio": {"type": ["array", "null"], "items": {"type": "object", "properties": {"text": {"type": ["string", "null"]}, "title": {"type": ["string", "null"]}, "sequence": {"type": ["number", "null"]}}, "required": ["text", "title", "sequence"]}}, "dob": {"type": ["string", "null"]}, "fb": {"type": ["string", "null"]}, "twitter": {"type": ["string", "null"]}, "wiki": {"type": ["string", "null"]}, "availableLanguages": {"type": "array", "items": {"type": "string"}}, "isRadioPresent": {"type": ["boolean", "null"]}, "topSongs": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}, "topAlbums": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "year": {"type": ["number", "null"]}, "type": {"type": "string"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "explicitContent": {"type": "boolean"}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "songCount": {"type": ["number", "null"]}, "url": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "songs": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}}, "required": ["id", "name", "description", "year", "type", "playCount", "language", "explicitContent", "artists", "songCount", "url", "image", "songs"]}}, "singles": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}, "similarArtists": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "languages": {"type": ["object", "null"], "additionalProperties": {"type": "string"}}, "wiki": {"type": "string"}, "dob": {"type": "string"}, "fb": {"type": "string"}, "twitter": {"type": "string"}, "isRadioPresent": {"type": "boolean"}, "type": {"type": "string"}, "dominantType": {"type": "string"}, "aka": {"type": "string"}, "bio": {"type": ["string", "null"]}, "similarArtists": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}}}, "required": ["id", "name", "url", "image", "languages", "wiki", "dob", "fb", "twitter", "isRadioPresent", "type", "dominantType", "aka", "bio", "similarArtists"]}}}, "required": ["id", "name", "url", "type", "image", "followerCount", "fanCount", "isVerified", "dominantLanguage", "dominantType", "bio", "dob", "fb", "twitter", "wiki", "availableLanguages", "isRadioPresent", "topSongs", "topAlbums", "singles", "similarArtists"]}}, "required": ["success", "data"]}}}}, "404": {"description": "Artist not found for the given ID"}}}}, "/api/artists/{id}/songs": {"get": {"tags": ["Artists"], "summary": "Retrieve artist's songs", "description": "Retrieve a list of songs for a given artist by their ID, with optional sorting and pagination.", "operationId": "getArtistSongs", "parameters": [{"schema": {"type": "string", "description": "ID of the artist to retrieve the songs for", "example": "1274170", "default": "1274170"}, "required": true, "name": "id", "in": "path"}, {"schema": {"type": "number", "description": "The page number of the results to retrieve", "example": "0", "default": "0"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "description": "The criterion to sort the songs by", "example": "popularity", "enum": ["popularity", "latest", "alphabetical"], "default": "popularity"}, "required": false, "name": "sortBy", "in": "query"}, {"schema": {"type": "string", "description": "The order to sort the songs", "example": "desc", "enum": ["asc", "desc"], "default": "desc"}, "required": false, "name": "sortOrder", "in": "query"}], "responses": {"200": {"description": "Successful response with a list of songs for the artist", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the request was successful", "example": true}, "data": {"type": "object", "properties": {"total": {"type": "number"}, "songs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}}, "required": ["total", "songs"], "description": "An array of songs associated with the artist"}}, "required": ["success", "data"]}}}}, "404": {"description": "Artist not found for the given ID"}}}}, "/api/artists/{id}/albums": {"get": {"tags": ["Artists"], "summary": "Retrieve artist's albums", "description": "Retrieve a list of albums for a given artist by their ID, with optional sorting and pagination.", "operationId": "getArtistAlbums", "parameters": [{"schema": {"type": "string", "description": "ID of the artist to retrieve the albums for", "example": "1274170", "default": "1274170"}, "required": true, "name": "id", "in": "path"}, {"schema": {"type": "number", "description": "The page number of the results to retrieve", "example": "0", "default": "0"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "string", "description": "The criterion to sort the albums by", "example": "popularity", "enum": ["popularity", "latest", "alphabetical"], "default": "popularity"}, "required": false, "name": "sortBy", "in": "query"}, {"schema": {"type": "string", "description": "The order to sort the albums", "example": "desc", "enum": ["asc", "desc"], "default": "desc"}, "required": false, "name": "sortOrder", "in": "query"}], "responses": {"200": {"description": "Successful response with a list of albums for the artist", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the request was successful", "example": true}, "data": {"type": "object", "properties": {"total": {"type": "number"}, "albums": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "year": {"type": ["number", "null"]}, "type": {"type": "string"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "explicitContent": {"type": "boolean"}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "songCount": {"type": ["number", "null"]}, "url": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "songs": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}}, "required": ["id", "name", "description", "year", "type", "playCount", "language", "explicitContent", "artists", "songCount", "url", "image", "songs"]}}}, "required": ["total", "albums"], "description": "An array of albums associated with the artist"}}, "required": ["success", "data"]}}}}, "404": {"description": "Artist not found for the given ID"}}}}, "/api/playlists": {"get": {"tags": ["Playlist"], "summary": "Retrieve a playlist by ID or link", "description": "Retrieve a playlist by providing either an ID or a direct link to the playlist on JioSaavn.", "operationId": "getPlaylistByIdOrLink", "parameters": [{"schema": {"type": "string", "description": "The unique ID of the playlist", "title": "Playlist ID", "example": "82914609", "default": "82914609"}, "required": false, "name": "id", "in": "query"}, {"schema": {"type": "string", "description": "A direct link to the playlist on JioSaavn", "title": "Playlist Link", "example": "https://www.jiosaavn.com/featured/its-indie-english/AMoxtXyKHoU_", "default": "https://www.jiosaavn.com/featured/its-indie-english/AMoxtXyKHoU_"}, "required": false, "name": "link", "in": "query"}, {"schema": {"type": "integer", "description": "The page number of the songs to retrieve from the playlist", "title": "Page Number", "example": "0", "default": "0"}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "integer", "description": "Number of songs to retrieve per page", "title": "Limit", "example": "10", "default": "10"}, "required": false, "name": "limit", "in": "query"}], "responses": {"200": {"description": "Successful response with playlist details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates the success status of the request.", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": ["string", "null"]}, "year": {"type": ["number", "null"]}, "type": {"type": "string"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "explicitContent": {"type": "boolean"}, "songCount": {"type": ["number", "null"]}, "url": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "songs": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "year": {"type": ["string", "null"]}, "releaseDate": {"type": ["string", "null"]}, "duration": {"type": ["number", "null"]}, "label": {"type": ["string", "null"]}, "explicitContent": {"type": "boolean"}, "playCount": {"type": ["number", "null"]}, "language": {"type": "string"}, "hasLyrics": {"type": "boolean"}, "lyricsId": {"type": ["string", "null"]}, "url": {"type": "string"}, "copyright": {"type": ["string", "null"]}, "album": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "url": {"type": ["string", "null"]}}, "required": ["id", "name", "url"]}, "artists": {"type": "object", "properties": {"primary": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "featured": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}, "all": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["primary", "featured", "all"]}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "downloadUrl": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}}, "required": ["id", "name", "type", "year", "releaseDate", "duration", "label", "explicitContent", "playCount", "language", "hasLyrics", "lyricsId", "url", "copyright", "album", "artists", "image", "downloadUrl"]}}, "artists": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "image": {"type": "array", "items": {"type": "object", "properties": {"quality": {"type": "string"}, "url": {"type": "string"}}, "required": ["quality", "url"]}}, "url": {"type": "string"}}, "required": ["id", "name", "role", "type", "image", "url"]}}}, "required": ["id", "name", "description", "year", "type", "playCount", "language", "explicitContent", "songCount", "url", "image", "songs", "artists"], "description": "The detailed information of the playlist.", "title": "Playlist Details"}}, "required": ["success", "data"]}}}}, "400": {"description": "Bad request due to missing or invalid query parameters."}, "404": {"description": "The playlist could not be found with the provided ID or link."}}}}}, "webhooks": {}}