<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Favicon Export</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    .preview {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin: 20px 0;
    }
    .preview-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 8px;
    }
    .preview-item canvas {
      border: 1px solid #ddd;
      margin-bottom: 10px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin: 5px;
    }
    button:hover {
      background-color: #45a049;
    }
    .instructions {
      background-color: #fffde7;
      padding: 15px;
      border-left: 4px solid #ffd600;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Favicon Export Tool</h1>
    
    <div class="instructions">
      <h3>Instructions:</h3>
      <p>1. Review the favicon previews below at different sizes</p>
      <p>2. Click the "Download" buttons to save each size</p>
      <p>3. Place the downloaded files in your project's public directory</p>
    </div>
    
    <div class="preview" id="preview"></div>
    
    <script>
      // Original SVG content
      const svgContent = `
      <svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- Dark background with rounded corners -->
        <rect width="512" height="512" rx="128" fill="#0F0F0F"/>
        
        <!-- Headphones design with neon aqua accent -->
        <path d="M256 96C158.016 96 78 176.016 78 274v66c0 13.255 10.745 24 24 24h24v-96c0-71.682 58.318-130 130-130s130 58.318 130 130v96h24c13.255 0 24-10.745 24-24v-66c0-97.984-80.016-178-178-178z" stroke="#00FFD1" stroke-width="16" stroke-linecap="round"/>
        
        <!-- Left earpiece -->
        <path d="M126 274v96c0 13.255 10.745 24 24 24h32c13.255 0 24-10.745 24-24v-96c0-13.255-10.745-24-24-24h-32c-13.255 0-24 10.745-24 24z" fill="#00FFD1" fill-opacity="0.2" stroke="#00FFD1" stroke-width="12"/>
        
        <!-- Right earpiece -->
        <path d="M306 274v96c0 13.255 10.745 24 24 24h32c13.255 0 24-10.745 24-24v-96c0-13.255-10.745-24-24-24h-32c-13.255 0-24 10.745-24 24z" fill="#00FFD1" fill-opacity="0.2" stroke="#00FFD1" stroke-width="12"/>
        
        <!-- Music note -->
        <path d="M256 96v178" stroke="#00FFD1" stroke-width="16" stroke-linecap="round"/>
        <circle cx="256" cy="274" r="32" fill="#00FFD1"/>
      </svg>
      `;
      
      // Sizes to generate
      const sizes = [
        { width: 16, height: 16, name: 'favicon-16x16.png' },
        { width: 32, height: 32, name: 'favicon-32x32.png' },
        { width: 64, height: 64, name: 'favicon-64x64.png' },
        { width: 180, height: 180, name: 'apple-touch-icon.png' },
        { width: 192, height: 192, name: 'android-chrome-192x192.png' },
        { width: 512, height: 512, name: 'android-chrome-512x512.png' }
      ];
      
      // Create preview for each size
      const previewContainer = document.getElementById('preview');
      
      sizes.forEach(size => {
        // Create container for this size
        const previewItem = document.createElement('div');
        previewItem.className = 'preview-item';
        
        // Create canvas
        const canvas = document.createElement('canvas');
        canvas.width = size.width;
        canvas.height = size.height;
        canvas.style.width = Math.min(size.width, 150) + 'px';
        canvas.style.height = Math.min(size.height, 150) + 'px';
        
        // Draw SVG on canvas
        const ctx = canvas.getContext('2d');
        const img = new Image();
        img.onload = function() {
          ctx.drawImage(img, 0, 0, size.width, size.height);
        };
        img.src = 'data:image/svg+xml;base64,' + btoa(svgContent);
        
        // Create label
        const label = document.createElement('div');
        label.textContent = `${size.width}x${size.height}`;
        
        // Create download button
        const downloadBtn = document.createElement('button');
        downloadBtn.textContent = 'Download';
        downloadBtn.onclick = function() {
          // Create a download link
          const link = document.createElement('a');
          link.download = size.name;
          link.href = canvas.toDataURL('image/png');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        };
        
        // Add elements to container
        previewItem.appendChild(canvas);
        previewItem.appendChild(label);
        previewItem.appendChild(downloadBtn);
        previewContainer.appendChild(previewItem);
      });
    </script>
  </div>
</body>
</html>
