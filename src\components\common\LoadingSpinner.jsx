import { useTheme } from '../../context/ThemeContext';
import { Row, Col } from 'react-bootstrap';
import {
  SongCardShimmer,
  SongListShimmer,
  PlaylistShimmer,
  SongDetailShimmer
} from './ShimmerLoaders';
import ShimmerEffect from './ShimmerEffect';
import ShimmerGrid from './ShimmerGrid';

/**
 * Enhanced LoadingSpinner component that uses shimmer effects
 *
 * @param {Object} props
 * @param {string} props.size - Size of the spinner (small, medium, large)
 * @param {boolean} props.fullPage - Whether to display the spinner full page
 * @param {string} props.type - Type of content being loaded (songs, playlists, songDetail, etc.)
 * @param {number} props.count - Number of shimmer items to display
 * @param {boolean} props.useSpinner - Whether to use the traditional spinner instead of shimmer
 */
const LoadingSpinner = ({
  size = 'medium',
  fullPage = false,
  type = 'default',
  count = 6,
  useSpinner = false
}) => {
  const { darkMode } = useTheme();

  // If traditional spinner is requested, use that
  if (useSpinner) {
    const getSpinnerSize = () => {
      switch (size) {
        case 'small': return 'spinner-border-sm';
        case 'large': return 'spinner-border spinner-border-lg';
        default: return '';
      }
    };

    if (fullPage) {
      return (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '70vh' }}>
          <div
            className={`spinner-border ${getSpinnerSize()}`}
            role="status"
            style={{ color: 'var(--dark-accent)' }}
          >
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      );
    }

    return (
      <div className="d-flex justify-content-center my-4">
        <div
          className={`spinner-border ${getSpinnerSize()}`}
          role="status"
          style={{ color: darkMode ? 'var(--dark-accent)' : 'var(--light-primary)' }}
        >
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  // Use shimmer effects based on content type
  const renderShimmer = () => {
    switch (type) {
      case 'songs':
      case 'songGrid':
        return <ShimmerGrid type="songs" count={count} xs={2} sm={2} md={3} lg={4} xl={5} />;
      case 'albums':
        return <ShimmerGrid type="albums" count={count} xs={2} sm={2} md={3} lg={4} xl={5} />;
      case 'artists':
        return <ShimmerGrid type="artists" count={count} xs={2} sm={2} md={3} lg={4} xl={5} />;
      case 'playlists':
        return <ShimmerGrid type="playlists" count={count} xs={2} sm={2} md={3} lg={4} xl={5} />;
      case 'songList':
        return <SongListShimmer count={count} />;
      case 'songDetail':
        return <SongDetailShimmer />;
      case 'text':
        return (
          <div className="my-3">
            <ShimmerEffect type="text" count={count} className="mb-2" />
          </div>
        );
      case 'featured':
        // Featured section with larger first item
        return (
          <div className="featured-shimmer">
            <Row className="g-4">
              <Col xs={12} md={6}>
                <div className="position-relative rounded overflow-hidden" style={{ aspectRatio: '16/9' }}>
                  <ShimmerEffect type="image" height="100%" />
                  <div className="position-absolute bottom-0 start-0 w-100 p-3" style={{ background: 'linear-gradient(transparent, rgba(0,0,0,0.8))' }}>
                    <ShimmerEffect type="text" height="1.5rem" className="mb-2" width="70%" />
                    <ShimmerEffect type="text" height="1rem" width="50%" />
                  </div>
                </div>
              </Col>
              <Col xs={12} md={6}>
                <Row className="g-3">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <Col key={index} xs={6}>
                      <div className="position-relative rounded overflow-hidden" style={{ aspectRatio: '1/1' }}>
                        <ShimmerEffect type="image" height="100%" />
                        <div className="position-absolute bottom-0 start-0 w-100 p-2" style={{ background: 'linear-gradient(transparent, rgba(0,0,0,0.8))' }}>
                          <ShimmerEffect type="text" height="0.9rem" width="80%" />
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </Col>
            </Row>
          </div>
        );
      default:
        // Default shimmer for unknown content types
        if (fullPage) {
          return (
            <div className="d-flex flex-column align-items-center justify-content-center" style={{ minHeight: '70vh' }}>
              <div className="position-relative" style={{ width: '60px', height: '60px' }}>
                <ShimmerEffect type="circle" width="100%" height="100%" />
              </div>
              <div className="mt-3" style={{ width: '200px' }}>
                <ShimmerEffect type="text" width="100%" height="1rem" />
              </div>
            </div>
          );
        }
        return (
          <div className="d-flex justify-content-center my-4">
            <div className="position-relative" style={{ width: '40px', height: '40px' }}>
              <ShimmerEffect type="circle" width="100%" height="100%" />
            </div>
          </div>
        );
    }
  };

  return renderShimmer();
};

export default LoadingSpinner;
