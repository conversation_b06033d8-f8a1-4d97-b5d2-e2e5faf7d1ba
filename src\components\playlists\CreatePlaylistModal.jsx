import { useState } from 'react';
import { <PERSON>dal, Button, Form, Alert } from 'react-bootstrap';
import { FaMusic, FaPlus, FaListUl } from 'react-icons/fa';
import { usePlaylist } from '../../context/PlaylistContext';
import { motion } from 'framer-motion';

const CreatePlaylistModal = ({ show, onHide }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { createPlaylist } = usePlaylist();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!name.trim()) {
      setError('Please enter a playlist name');
      return;
    }

    try {
      setError('');
      setLoading(true);
      await createPlaylist(name, description);
      onHide();

      // Reset form
      setName('');
      setDescription('');
    } catch (err) {
      setError('Failed to create playlist: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      size="md"
      contentClassName="bg-dark text-light"
      backdropClassName="backdrop-blur"
    >
      <Modal.Header
        closeButton
        closeVariant="white"
        className="border-0"
        style={{
          background: 'linear-gradient(145deg, rgba(15, 15, 15, 0.95) 0%, rgba(30, 30, 30, 0.9) 100%)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.05)'
        }}
      >
        <Modal.Title className="d-flex align-items-center">
          <FaPlus className="me-2" style={{ color: 'var(--dark-accent)' }} />
          Create New Playlist
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ padding: '1.5rem' }}>
        {/* Playlist Icon */}
        <div className="text-center mb-4">
          <div
            className="d-inline-flex align-items-center justify-content-center rounded-circle mb-3"
            style={{
              width: '80px',
              height: '80px',
              backgroundColor: 'rgba(0, 255, 209, 0.1)',
              border: '1px solid rgba(0, 255, 209, 0.2)'
            }}
          >
            <FaMusic size={30} style={{ color: 'var(--dark-accent)' }} />
          </div>
          <h5 style={{ color: 'var(--dark-text)' }}>New Playlist</h5>
        </div>

        {error && (
          <Alert
            variant="danger"
            className="mb-4 py-2"
            style={{ backgroundColor: 'rgba(220, 53, 69, 0.15)', borderColor: 'rgba(220, 53, 69, 0.3)' }}
          >
            {error}
          </Alert>
        )}

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label className="d-flex align-items-center">
              <FaListUl className="me-2" size={14} style={{ color: 'var(--dark-accent)', opacity: 0.8 }} />
              Playlist Name
            </Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter playlist name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.2)',
                color: 'var(--dark-text)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: '8px',
                padding: '0.75rem',
                boxShadow: 'none'
              }}
              className="focus-ring-dark"
            />
          </Form.Group>

          <Form.Group className="mb-4">
            <Form.Label className="d-flex align-items-center">
              <FaMusic className="me-2" size={14} style={{ color: 'var(--dark-accent)', opacity: 0.8 }} />
              Description (optional)
            </Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              placeholder="Enter description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.2)',
                color: 'var(--dark-text)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: '8px',
                padding: '0.75rem',
                boxShadow: 'none'
              }}
              className="focus-ring-dark custom-scrollbar"
            />
          </Form.Group>

          <div className="d-flex justify-content-between align-items-center mt-4">
            <small className="text-muted">
              Create a playlist to organize your favorite songs
            </small>

            <div className="d-flex gap-2">
              <Button
                variant="outline-secondary"
                onClick={onHide}
                disabled={loading}
                style={{ borderRadius: '8px' }}
              >
                Cancel
              </Button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={loading}
                className="btn"
                style={{
                  backgroundColor: 'var(--dark-accent)',
                  color: '#2c3e50',
                  border: 'none',
                  fontWeight: '600',
                  borderRadius: '8px',
                  padding: '0.5rem 1rem'
                }}
              >
                {loading ? (
                  <div className="d-flex align-items-center">
                    <div className="me-2 position-relative" style={{ width: '16px', height: '16px', overflow: 'hidden' }}>
                      <div className="shimmer-item" style={{ width: '100%', height: '100%', backgroundColor: 'rgba(44, 62, 80, 0.2)', borderRadius: '50%' }}>
                        <div className="shimmer-animation" />
                      </div>
                    </div>
                    Creating...
                  </div>
                ) : (
                  <>Create Playlist</>
                )}
              </motion.button>
            </div>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default CreatePlaylistModal;
