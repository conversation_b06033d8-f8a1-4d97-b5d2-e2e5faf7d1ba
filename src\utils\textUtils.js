/**
 * Utility functions for text processing
 */

/**
 * Decodes HTML entities in a string
 * @param {string} text - The text containing HTML entities
 * @returns {string} - The decoded text
 */
export const decodeHtmlEntities = (text) => {
  if (!text || typeof text !== 'string') return '';

  // Create a temporary element to use the browser's built-in HTML entity decoding
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;

  // Get the decoded text
  const decoded = textarea.value;

  return decoded;
};

/**
 * Formats a play count in K/M format
 * @param {number} count - The play count
 * @returns {string} - Formatted play count
 */
export const formatPlayCount = (count) => {
  if (!count) return '0';

  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  } else {
    return count.toString();
  }
};

/**
 * Formats duration in seconds to MM:SS format
 * @param {number} durationInSeconds - Duration in seconds
 * @returns {string} - Formatted duration
 */
export const formatDuration = (durationInSeconds) => {
  // Handle missing, zero, or invalid duration values
  if (!durationInSeconds || isNaN(durationInSeconds) || durationInSeconds <= 0) {
    // Default to 4 minutes (240 seconds) for display purposes
    // This is just a visual fallback when duration is missing
    durationInSeconds = 240;
  }

  const minutes = Math.floor(durationInSeconds / 60);
  const seconds = Math.floor(durationInSeconds % 60);

  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};
