import { useState, useEffect } from 'react';
import { getSongsByIds } from '../services/api';
import SongGrid from '../components/features/SongGrid';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { FaHeart } from 'react-icons/fa';
import { useSong } from '../context/SongContext';

const Library = () => {
  const [favorites, setFavorites] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { currentSong } = useSong();

  useEffect(() => {
    const fetchFavorites = async () => {
      try {
        setIsLoading(true);

        // Get favorites from localStorage
        const storedFavorites = localStorage.getItem('favorites');
        if (storedFavorites) {
          const parsedFavorites = JSON.parse(storedFavorites);

          if (parsedFavorites.length > 0) {
            // If we have IDs, fetch the full song details
            const songIds = parsedFavorites.map(song => song.id).join(',');
            const response = await getSongsByIds(songIds);

            if (response.success && response.data) {
              setFavorites(response.data);
            } else {
              // If API fails, use the stored data
              setFavorites(parsedFavorites);
            }
          } else {
            setFavorites([]);
          }
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching favorites:', err);
        setError('Failed to load your library. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchFavorites();
  }, []);



  if (isLoading) {
    return <LoadingSpinner fullPage />;
  }

  return (
    <div className="library-page">
      <div className="d-flex align-items-center mb-4">
        <FaHeart className="text-danger me-2" size={24} />
        <h1 className="mb-0">Your Library</h1>
      </div>

      {error && (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      )}

      {!isLoading && favorites.length === 0 ? (
        <div className="text-center py-5">
          <p className="mb-3">You haven't added any songs to your library yet.</p>
          <a href="/" className="btn btn-primary">Discover Music</a>
        </div>
      ) : (
        <SongGrid
          songs={favorites}
          isLoading={isLoading}
        />
      )}


    </div>
  );
};

export default Library;
