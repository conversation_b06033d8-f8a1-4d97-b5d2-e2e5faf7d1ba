import { useEffect, useCallback } from 'react';

export const useKeyboardShortcuts = ({
  onTogglePlay,
  onNext,
  onPrevious,
  onVolumeUp,
  onVolumeDown,
  onMute,
  onSeek,
  audioRef
}) => {
  const handleKeyPress = useCallback((event) => {
    // Don't trigger shortcuts if user is typing in an input field
    if (event.target.tagName === 'INPUT' || 
        event.target.tagName === 'TEXTAREA' || 
        event.target.isContentEditable) {
      return;
    }

    // Prevent default behavior for our shortcuts
    const preventDefault = () => {
      event.preventDefault();
      event.stopPropagation();
    };

    switch (event.code) {
      case 'Space':
        preventDefault();
        onTogglePlay?.();
        break;
        
      case 'ArrowRight':
        if (event.shiftKey) {
          // Shift + Right Arrow: Next track
          preventDefault();
          onNext?.();
        } else {
          // Right Arrow: Seek forward 10 seconds
          preventDefault();
          if (audioRef?.current) {
            const newTime = audioRef.current.currentTime + 10;
            onSeek?.(newTime);
          }
        }
        break;
        
      case 'ArrowLeft':
        if (event.shiftKey) {
          // Shift + Left Arrow: Previous track
          preventDefault();
          onPrevious?.();
        } else {
          // Left Arrow: Seek backward 10 seconds
          preventDefault();
          if (audioRef?.current) {
            const newTime = Math.max(0, audioRef.current.currentTime - 10);
            onSeek?.(newTime);
          }
        }
        break;
        
      case 'ArrowUp':
        preventDefault();
        onVolumeUp?.();
        break;
        
      case 'ArrowDown':
        preventDefault();
        onVolumeDown?.();
        break;
        
      case 'KeyM':
        preventDefault();
        onMute?.();
        break;
        
      case 'KeyJ':
        // J: Seek backward 10 seconds (YouTube style)
        preventDefault();
        if (audioRef?.current) {
          const newTime = Math.max(0, audioRef.current.currentTime - 10);
          onSeek?.(newTime);
        }
        break;

      case 'KeyL':
        // L: Seek forward 10 seconds (YouTube style)
        preventDefault();
        if (audioRef?.current) {
          const newTime = audioRef.current.currentTime + 10;
          onSeek?.(newTime);
        }
        break;
        
      case 'KeyK':
        // K: Toggle play/pause (YouTube style)
        preventDefault();
        onTogglePlay?.();
        break;
        
      case 'Comma':
        // , (comma): Previous frame/seek backward 1 second
        if (event.shiftKey) {
          preventDefault();
          if (audioRef?.current) {
            const newTime = Math.max(0, audioRef.current.currentTime - 1);
            onSeek?.(newTime);
          }
        }
        break;

      case 'Period':
        // . (period): Next frame/seek forward 1 second
        if (event.shiftKey) {
          preventDefault();
          if (audioRef?.current) {
            const newTime = audioRef.current.currentTime + 1;
            onSeek?.(newTime);
          }
        }
        break;
        
      case 'Digit0':
      case 'Digit1':
      case 'Digit2':
      case 'Digit3':
      case 'Digit4':
      case 'Digit5':
      case 'Digit6':
      case 'Digit7':
      case 'Digit8':
      case 'Digit9':
        // Number keys: Seek to percentage of track
        preventDefault();
        if (audioRef?.current && audioRef.current.duration) {
          const digit = parseInt(event.code.replace('Digit', ''));
          const percentage = digit * 10; // 0-90%
          const newTime = (percentage / 100) * audioRef.current.duration;
          onSeek?.(newTime);
        }
        break;
        
      default:
        // Don't prevent default for unhandled keys
        break;
    }
  }, [onTogglePlay, onNext, onPrevious, onVolumeUp, onVolumeDown, onMute, onSeek]);

  useEffect(() => {
    // Add event listener for keydown events
    document.addEventListener('keydown', handleKeyPress);
    
    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);

  // Return keyboard shortcut info for display
  return {
    shortcuts: [
      { key: 'Space / K', action: 'Play/Pause' },
      { key: '← / J', action: 'Seek backward 10s' },
      { key: '→ / L', action: 'Seek forward 10s' },
      { key: 'Shift + ←', action: 'Previous track' },
      { key: 'Shift + →', action: 'Next track' },
      { key: '↑', action: 'Volume up' },
      { key: '↓', action: 'Volume down' },
      { key: 'M', action: 'Mute/Unmute' },
      { key: '0-9', action: 'Seek to % of track' },
      { key: 'Shift + ,', action: 'Seek backward 1s' },
      { key: 'Shift + .', action: 'Seek forward 1s' }
    ]
  };
};
