import { useState } from 'react';
import { <PERSON><PERSON>, Button, Form, Alert } from 'react-bootstrap';
import { FaGoogle } from 'react-icons/fa';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'framer-motion';

const SignupModal = ({ show, onHide, switchToLogin }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { signup, signInWithGoogle } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !password || !confirmPassword || !displayName) {
      setError('Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    try {
      setError('');
      setLoading(true);
      console.log("Attempting to create account for:", email);
      const user = await signup(email, password, displayName);
      console.log("Account creation successful:", user);
      onHide();
    } catch (err) {
      console.error("Signup error:", err);
      setError('Failed to create an account: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setError('');
      setLoading(true);
      console.log("Attempting to sign in with Google");
      const user = await signInWithGoogle();
      console.log("Google sign-in successful:", user);
      onHide();
    } catch (err) {
      console.error("Google sign-in error:", err);

      // Display a more user-friendly error message
      if (err.code === 'auth/popup-closed-by-user') {
        setError('Sign-in was cancelled. Please try again.');
      } else if (err.code === 'auth/popup-blocked') {
        setError('Sign-in popup was blocked by your browser. Please allow popups for this site.');
      } else if (err.code === 'auth/cancelled-popup-request') {
        setError('Another sign-in attempt is in progress. Please wait.');
      } else if (err.code === 'auth/internal-error') {
        setError('Authentication service encountered an error. This might be due to network issues or browser settings. Please try again later.');
      } else {
        setError('Failed to sign in with Google: ' + (err.message || 'Unknown error'));
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      contentClassName="bg-dark text-light"
      backdropClassName="backdrop-blur"
    >
      <Modal.Header closeButton closeVariant="white" className="border-0">
        <Modal.Title>Create Account</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Display Name</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter your name"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              required
              className="bg-dark text-light border-secondary"
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Email address</Form.Label>
            <Form.Control
              type="email"
              placeholder="Enter email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="bg-dark text-light border-secondary"
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Password</Form.Label>
            <Form.Control
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="bg-dark text-light border-secondary"
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Confirm Password</Form.Label>
            <Form.Control
              type="password"
              placeholder="Confirm password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              className="bg-dark text-light border-secondary"
            />
          </Form.Group>

          <div className="d-grid gap-2 mb-3">
            <motion.div whileTap={{ scale: 0.95 }}>
              <Button
                variant="primary"
                type="submit"
                disabled={loading}
                className="w-100"
                style={{ backgroundColor: 'var(--dark-accent)', color: '#2c3e50', border: 'none', fontWeight: '600' }}
              >
                {loading ? 'Creating account...' : 'Sign Up'}
              </Button>
            </motion.div>
          </div>

          <div className="d-grid gap-2">
            <motion.div whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline-light"
                onClick={handleGoogleSignIn}
                disabled={loading}
                className="w-100 d-flex align-items-center justify-content-center"
              >
                <FaGoogle className="me-2" /> Sign up with Google
              </Button>
            </motion.div>
          </div>
        </Form>

        <div className="mt-3 text-center">
          <p className="mb-0">
            Already have an account?{' '}
            <Button
              variant="link"
              onClick={switchToLogin}
              className="p-0"
              style={{ color: 'var(--dark-accent)' }}
            >
              Sign in
            </Button>
          </p>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default SignupModal;
