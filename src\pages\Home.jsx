import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { searchSongs, getTrendingSongs, getLatestSongs, getFeaturedHindiSong } from '../services/api';
import SearchBar from '../components/common/SearchBar';
import FeaturedSection from '../components/features/FeaturedSection';
import SongGrid from '../components/features/SongGrid';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ShimmerEffect from '../components/common/ShimmerEffect';
import { useSong } from '../context/SongContext';
import { motion } from 'framer-motion';
import { FaHeadphones, FaMusic, FaFire, FaCalendarAlt } from 'react-icons/fa';

const Home = () => {
  const [featuredSong, setFeaturedSong] = useState(null);
  const [trendingSongs, setTrendingSongs] = useState([]);
  const [newReleases, setNewReleases] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const { playSong } = useSong();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setIsLoading(true);

        // Fetch featured Hindi song (latest released)
        const featuredResponse = await getFeaturedHindiSong();
        if (featuredResponse.success && featuredResponse.data && featuredResponse.data.results && featuredResponse.data.results.length > 0) {
          setFeaturedSong(featuredResponse.data.results[0]);
        }

        // Fetch trending Hindi songs using the dedicated function
        const trendingResponse = await getTrendingSongs(12);
        if (trendingResponse.success && trendingResponse.data && trendingResponse.data.results) {
          setTrendingSongs(trendingResponse.data.results);

          // If no featured song was found, use the first trending song
          if (!featuredResponse.success || !featuredResponse.data || !featuredResponse.data.results || featuredResponse.data.results.length === 0) {
            if (trendingResponse.data.results.length > 0) {
              setFeaturedSong(trendingResponse.data.results[0]);
            }
          }
        }

        // Fetch latest Hindi songs using the dedicated function
        const newReleasesResponse = await getLatestSongs(12);
        if (newReleasesResponse.success && newReleasesResponse.data && newReleasesResponse.data.results) {
          setNewReleases(newReleasesResponse.data.results);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching initial data:', err);
        setError('Failed to load content. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  const handleSearch = (query) => {
    navigate(`/search?q=${encodeURIComponent(query)}`);
  };

  if (isLoading) {
    return (
      <div className="home-page-loading">
        {/* Hero Section Shimmer */}
        <div className="mb-5 py-5 rounded-4" style={{
          background: 'linear-gradient(135deg, rgba(157, 78, 221, 0.1) 0%, rgba(0, 255, 209, 0.05) 100%)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.05)',
          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)'
        }}>
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-lg-8 text-center">
                <div className="mb-3">
                  <ShimmerEffect type="text" height="2.5rem" width="60%" style={{ margin: '0 auto' }} />
                </div>
                <div className="mb-4">
                  <ShimmerEffect type="text" height="1rem" width="80%" style={{ margin: '0 auto' }} />
                  <ShimmerEffect type="text" height="1rem" width="70%" className="mt-2" style={{ margin: '0 auto' }} />
                </div>
                <div style={{ maxWidth: '600px', margin: '0 auto' }}>
                  <ShimmerEffect type="text" height="3rem" width="100%" style={{ borderRadius: '2rem' }} />
                </div>
                <div className="mt-4 d-flex justify-content-center gap-3">
                  <ShimmerEffect type="text" height="2rem" width="100px" style={{ borderRadius: '2rem' }} />
                  <ShimmerEffect type="text" height="2rem" width="120px" style={{ borderRadius: '2rem' }} />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section Shimmer */}
        <div className="mb-5 mt-4">
          <div className="row g-3 justify-content-center">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="col-6 col-md-3">
                <ShimmerEffect
                  type="text"
                  height="80px"
                  style={{
                    borderRadius: '0.75rem',
                    backgroundColor: 'rgba(15, 15, 15, 0.5)'
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Featured Section Shimmer */}
        <LoadingSpinner type="featured" className="mb-5 mt-4" />

        {/* Trending Songs Shimmer */}
        <div className="mb-5 mt-4">
          <div className="d-flex align-items-center mb-4">
            <ShimmerEffect type="circle" width="38px" height="38px" className="me-3" />
            <ShimmerEffect type="text" height="1.5rem" width="200px" />
          </div>
          <LoadingSpinner type="songs" count={8} />
        </div>

        {/* New Releases Shimmer */}
        <div className="mb-5 mt-4">
          <div className="d-flex align-items-center mb-4">
            <ShimmerEffect type="circle" width="38px" height="38px" className="me-3" />
            <ShimmerEffect type="text" height="1.5rem" width="200px" />
          </div>
          <LoadingSpinner type="songs" count={8} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        {error}
      </div>
    );
  }

  return (
    <div className="home-page">
      {/* Hero Section */}
      <motion.div
        className="hero-section mb-5 py-5 rounded-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        style={{
          background: 'linear-gradient(135deg, rgba(157, 78, 221, 0.2) 0%, rgba(0, 255, 209, 0.1) 100%)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.05)',
          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background decorative elements */}
        <div
          className="position-absolute"
          style={{
            width: '300px',
            height: '300px',
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(0, 255, 209, 0.1) 0%, rgba(0, 0, 0, 0) 70%)',
            top: '-150px',
            right: '-100px',
            zIndex: 0
          }}
        ></div>

        <div
          className="position-absolute"
          style={{
            width: '250px',
            height: '250px',
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(157, 78, 221, 0.1) 0%, rgba(0, 0, 0, 0) 70%)',
            bottom: '-100px',
            left: '-50px',
            zIndex: 0
          }}
        ></div>

        {/* Music note decorations */}
        <div className="position-absolute" style={{ top: '20px', left: '10%', zIndex: 0, opacity: 0.5 }}>
          <motion.div
            animate={{
              y: [0, -10, 0],
              rotate: [0, 5, 0]
            }}
            transition={{
              repeat: Infinity,
              duration: 3,
              ease: "easeInOut"
            }}
          >
            ♪
          </motion.div>
        </div>

        <div className="position-absolute" style={{ bottom: '30px', right: '15%', zIndex: 0, opacity: 0.5 }}>
          <motion.div
            animate={{
              y: [0, -15, 0],
              rotate: [0, -5, 0]
            }}
            transition={{
              repeat: Infinity,
              duration: 4,
              ease: "easeInOut",
              delay: 1
            }}
          >
            ♫
          </motion.div>
        </div>

        <div className="container position-relative" style={{ zIndex: 1 }}>
          <div className="row justify-content-center">
            <div className="col-lg-8 text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
              >
                <h2 className="display-5 fw-bold mb-3" style={{ color: '#ffffff' }}>
                  <span style={{ color: 'var(--dark-accent)' }}>Discover</span> Your Perfect Sound
                </h2>
                <p className="mb-4 text-muted mx-auto" style={{ maxWidth: '600px', fontSize: '1rem', color: '#ffffff !important' }}>
                  Explore the latest hits, discover new artists, and create your perfect playlist with our modern music experience.
                </p>
                <div className="search-container home-search">
                  <SearchBar onSearch={handleSearch} placeholder="Search for songs, artists, or albums..." />
                </div>
                <div className="mt-4 d-flex justify-content-center gap-3">
                  <motion.span
                    className="badge rounded-pill px-3 py-2"
                    whileHover={{ scale: 1.05 }}
                    style={{
                      backgroundColor: 'rgba(157, 78, 221, 0.15)',
                      color: 'var(--dark-accent)',
                      border: '1px solid rgba(157, 78, 221, 0.1)'
                    }}
                  >
                    Trending
                  </motion.span>
                  <motion.span
                    className="badge rounded-pill px-3 py-2"
                    whileHover={{ scale: 1.05 }}
                    style={{
                      backgroundColor: 'rgba(0, 255, 209, 0.15)',
                      color: 'var(--dark-accent)',
                      border: '1px solid rgba(0, 255, 209, 0.1)'
                    }}
                  >
                    New Releases
                  </motion.span>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Section */}
      <motion.div
        className="stats-section mb-5 mt-4 d-flex justify-content-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.5 }}
      >
        <div className="stats-buttons-container d-flex flex-wrap justify-content-center gap-3">
          <div className="row g-3 w-100 justify-content-center">
            <div className="col-6 col-md-3">
              <motion.div
                className="stat-button h-100"
                whileHover={{ y: -5, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.2)' }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7, duration: 0.3 }}
              >
                <div className="stat-content d-flex align-items-center">
                  <div className="stat-icon">
                    <FaHeadphones />
                  </div>
                  <div className="stat-text">
                    <div className="stat-value">10M+</div>
                    <div className="stat-label">Songs</div>
                  </div>
                </div>
              </motion.div>
            </div>

            <div className="col-6 col-md-3">
              <motion.div
                className="stat-button h-100"
                whileHover={{ y: -5, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.2)' }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8, duration: 0.3 }}
              >
                <div className="stat-content d-flex align-items-center">
                  <div className="stat-icon">
                    <FaMusic />
                  </div>
                  <div className="stat-text">
                    <div className="stat-value">5K+</div>
                    <div className="stat-label">Artists</div>
                  </div>
                </div>
              </motion.div>
            </div>

            <div className="col-6 col-md-3">
              <motion.div
                className="stat-button h-100"
                whileHover={{ y: -5, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.2)' }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.9, duration: 0.3 }}
              >
                <div className="stat-content d-flex align-items-center">
                  <div className="stat-icon">
                    <FaFire />
                  </div>
                  <div className="stat-text">
                    <div className="stat-value">100+</div>
                    <div className="stat-label">Genres</div>
                  </div>
                </div>
              </motion.div>
            </div>

            <div className="col-6 col-md-3">
              <motion.div
                className="stat-button h-100"
                whileHover={{ y: -5, boxShadow: '0 10px 20px rgba(0, 0, 0, 0.2)' }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.0, duration: 0.3 }}
              >
                <div className="stat-content d-flex align-items-center">
                  <div className="stat-icon">
                    <FaCalendarAlt />
                  </div>
                  <div className="stat-text">
                    <div className="stat-value">24/7</div>
                    <div className="stat-label">Streaming</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Featured Section */}
      {featuredSong && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="mt-4 mb-4"
        >
          <FeaturedSection song={featuredSong} />
        </motion.div>
      )}

      {/* Trending Songs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0, duration: 0.5 }}
        className="mb-5 mt-4"
      >
        <div className="section-header d-flex align-items-center mb-4 mt-2">
          <div className="section-icon me-3 rounded-circle d-flex align-items-center justify-content-center"
            style={{
              width: '38px',
              height: '38px',
              backgroundColor: 'rgba(157, 78, 221, 0.15)'
            }}
          >
            <FaFire style={{ color: 'var(--dark-accent)', fontSize: '16px' }} />
          </div>
          <h4 className="mb-0 fw-bold" style={{ color: '#ffffff' }}>Trending Hindi Songs</h4>
        </div>
        <SongGrid
          songs={trendingSongs}
          isLoading={isLoading}
          mobileColumns={2}
        />
      </motion.div>

      {/* New Releases */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2, duration: 0.5 }}
        className="mb-5 mt-4"
      >
        <div className="section-header d-flex align-items-center mb-4 mt-2">
          <div className="section-icon me-3 rounded-circle d-flex align-items-center justify-content-center"
            style={{
              width: '38px',
              height: '38px',
              backgroundColor: 'rgba(0, 255, 209, 0.15)'
            }}
          >
            <FaCalendarAlt style={{ color: 'var(--dark-accent)', fontSize: '16px' }} />
          </div>
          <h4 className="mb-0 fw-bold" style={{ color: '#ffffff' }}>New Hindi Releases</h4>
        </div>
        <SongGrid
          songs={newReleases}
          isLoading={isLoading}
          mobileColumns={2}
        />
      </motion.div>
    </div>
  );
};

export default Home;
