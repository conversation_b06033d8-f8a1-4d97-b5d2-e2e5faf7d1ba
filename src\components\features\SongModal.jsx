import { useNavigate } from 'react-router-dom';
import { FaPlay, FaPause, FaTimes } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';

const SongModal = ({ song, isPlaying, onPlay, onPause, onClose }) => {
  const navigate = useNavigate();
  const { darkMode } = useTheme();

  if (!song) return null;

  // Get high quality image
  const getImage = () => {
    if (!song.image || song.image.length === 0) {
      return 'https://via.placeholder.com/300';
    }

    // Find the highest quality image
    const highQualityImage = song.image.find(img => img.quality === '500x500');
    if (highQualityImage) return highQualityImage.url;

    return song.image[song.image.length - 1].url;
  };

  // Get artist names
  const getArtists = () => {
    if (song.artists && song.artists.primary) {
      return song.artists.primary.map(artist => artist.name).join(', ');
    }
    return '';
  };

  const handleCardClick = () => {
    navigate(`/song/${song.id}`);
  };

  return (
    <AnimatePresence>
      <motion.div
        className="song-modal"
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 100 }}
        transition={{ duration: 0.3 }}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          width: '350px',
          zIndex: 1050,
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
          backgroundColor: darkMode ? 'var(--dark-card-bg)' : 'var(--light-card-bg)',
          color: darkMode ? 'var(--dark-text)' : 'var(--light-text)',
        }}
      >
        <div className="position-relative">
          {/* Close button */}
          <button
            className="btn btn-sm btn-dark position-absolute end-0 top-0 m-2 rounded-circle"
            onClick={onClose}
            style={{ zIndex: 10 }}
            aria-label="Close"
          >
            <FaTimes />
          </button>

          {/* Song image and info - clickable to go to song page */}
          <div
            className="song-modal-content cursor-pointer"
            onClick={handleCardClick}
          >
            <img
              src={getImage()}
              alt={song.name}
              className="w-100"
              style={{ height: '180px', objectFit: 'cover' }}
            />

            <div className="p-3">
              <h5 className="mb-1">{song.name}</h5>
              <p className="text-muted mb-2">{getArtists()}</p>

              {/* Mini player controls */}
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <small className="text-muted">
                    {song.year && `${song.year} • `}
                    {song.language}
                  </small>
                </div>

                <button
                  className={`btn btn-sm rounded-circle ${darkMode ? 'btn-light' : 'btn-primary'}`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation(); // Prevent navigation
                    console.log('Modal play/pause button clicked for song:', song.name);
                    isPlaying ? onPause() : onPlay(song);
                  }}
                  aria-label={isPlaying ? 'Pause' : 'Play'}
                >
                  {isPlaying ? <FaPause /> : <FaPlay />}
                </button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default SongModal;
