import { createContext, useContext, useState, useEffect } from 'react';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  GoogleAuthProvider,
  signInWithPopup,
  sendPasswordResetEmail,
  updateProfile
} from 'firebase/auth';
import { auth } from '../firebase/config';
import logger from '../utils/logger';

// Create the context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Sign up function
  const signup = async (email, password, displayName) => {
    try {
      setError('');
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);

      // Update the user's profile with the display name
      if (displayName) {
        await updateProfile(userCredential.user, { displayName });
      }

      return userCredential.user;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Login function
  const login = async (email, password) => {
    try {
      setError('');
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Google sign-in function
  const signInWithGoogle = async () => {
    try {
      setError('');
      const provider = new GoogleAuthProvider();

      // Add scopes if needed
      provider.addScope('profile');
      provider.addScope('email');

      // Set custom parameters
      provider.setCustomParameters({
        prompt: 'select_account'
      });

      logger.info("Starting Google sign-in popup");
      const userCredential = await signInWithPopup(auth, provider);
      logger.info("Google sign-in successful");
      return userCredential.user;
    } catch (err) {
      // Log detailed error information
      logger.error("Google sign-in error details:", {
        code: err.code,
        message: err.message,
        email: err.email,
        credential: err.credential ? 'Present' : 'Not present',
        customData: err.customData ? JSON.stringify(err.customData) : 'None'
      });

      // Set a more user-friendly error message
      if (err.code === 'auth/popup-closed-by-user') {
        setError('Sign-in was cancelled. Please try again.');
      } else if (err.code === 'auth/popup-blocked') {
        setError('Sign-in popup was blocked by your browser. Please allow popups for this site.');
      } else if (err.code === 'auth/cancelled-popup-request') {
        setError('Another sign-in attempt is in progress. Please wait.');
      } else if (err.code === 'auth/internal-error') {
        setError('Authentication service encountered an error. This might be due to network issues or browser settings.');
      } else {
        setError(err.message || 'Failed to sign in with Google');
      }

      throw err;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setError('');
      await signOut(auth);
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Password reset function
  const resetPassword = async (email) => {
    try {
      setError('');
      await sendPasswordResetEmail(auth, email);
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Update profile function
  const updateUserProfile = async (profile) => {
    try {
      setError('');
      if (auth.currentUser) {
        await updateProfile(auth.currentUser, profile);
        // Update the local user state to reflect changes
        setCurrentUser({ ...currentUser, ...profile });
      }
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Effect for auth state changes
  useEffect(() => {
    logger.info("Setting up auth state listener");
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      logger.info("Auth state changed:", user ? `User: ${user.email}` : "No user");
      setCurrentUser(user);
      setLoading(false);
    });

    // Cleanup subscription
    return () => {
      logger.info("Cleaning up auth state listener");
      unsubscribe();
    };
  }, []);

  // Context value
  const value = {
    currentUser,
    signup,
    login,
    logout,
    signInWithGoogle,
    resetPassword,
    updateUserProfile,
    error,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
