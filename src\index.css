:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 209, 0.2);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) rgba(0, 0, 0, 0.2);
}

/* Shimmer effect styles */
.shimmer-container {
  width: 100%;
}

.shimmer-item {
  position: relative;
  overflow: hidden;
}

.shimmer-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  z-index: 1;
}

/* Focus ring for dark mode inputs */
.focus-ring-dark:focus {
  box-shadow: 0 0 0 0.25rem rgba(0, 255, 209, 0.25) !important;
  border-color: rgba(0, 255, 209, 0.5) !important;
}

/* User dropdown menu styles */
.dropdown-item:hover, .dropdown-item:focus {
  background-color: rgba(0, 255, 209, 0.1) !important;
  color: var(--dark-accent) !important;
}

.dropdown-menu {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsive styles */
@media (max-width: 991.98px) {
  /* General mobile styles */
  h1, .h1 {
    font-size: 1.75rem !important;
  }

  h2, .h2 {
    font-size: 1.5rem !important;
  }

  h3, .h3 {
    font-size: 1.25rem !important;
  }

  h4, .h4 {
    font-size: 1.1rem !important;
  }

  .display-5 {
    font-size: 1.5rem !important;
  }

  /* Improve spacing on mobile */
  .mb-4 {
    margin-bottom: 1rem !important;
  }

  .mb-5 {
    margin-bottom: 2rem !important;
  }

  .py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  /* Song grid mobile styles */
  .song-grid-container {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 0.75rem !important;
  }

  .song-grid-container.mobile-two-columns {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .song-card {
    border-radius: 0.75rem !important;
  }

  .song-card .card-title {
    font-size: 0.9rem !important;
    font-weight: 500 !important;
  }

  .song-card .card-text {
    font-size: 0.75rem !important;
  }

  /* Song detail page mobile styles */
  .song-detail-container .song-image {
    max-width: 100% !important;
    margin-bottom: 1.5rem;
  }

  .song-detail-container .song-info {
    padding-left: 0 !important;
  }

  .song-detail-container .song-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
  }

  .song-detail-container .song-artist {
    font-size: 1rem !important;
  }

  .song-detail-container .song-info-label {
    font-weight: 500 !important;
  }

  /* Featured section mobile styles */
  .featured-section {
    padding: 1rem !important;
  }

  .featured-section .featured-image {
    height: 200px !important;
  }

  .featured-section .featured-content {
    padding: 1rem !important;
  }
  /* Navbar mobile styles */
  .navbar-collapse {
    background-color: rgba(15, 15, 15, 0.98);
    border-radius: 1rem;
    padding: 1rem;
    margin-top: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  }

  .navbar-nav .nav-item {
    margin: 0.5rem 0;
    width: 100%;
  }

  .navbar-nav .nav-link {
    padding: 0.75rem 1rem !important;
    border-radius: 0.75rem !important;
    display: flex;
    align-items: center;
  }

  /* Adjust dropdown positioning on mobile */
  .dropdown-menu {
    position: static !important;
    float: none;
    width: 100%;
    margin-top: 0.5rem !important;
    box-shadow: none;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
  }

  /* Make user dropdown button full width on mobile */
  .nav-item .dropdown {
    width: 100%;
  }

  .nav-item .dropdown-toggle {
    width: 100%;
    justify-content: center;
  }

  /* Adjust spacing for mobile */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Music player mobile styles */
  .music-player {
    padding: 0.75rem !important;
    border-radius: 0.75rem !important;
  }

  .music-player .player-controls {
    gap: 0.5rem !important;
  }

  .music-player .player-controls button {
    width: 36px !important;
    height: 36px !important;
  }

  .music-player .player-controls .play-pause-btn {
    width: 42px !important;
    height: 42px !important;
  }

  .music-player .song-info {
    max-width: 150px !important;
  }

  .music-player .song-title {
    font-size: 0.85rem !important;
    font-weight: 500 !important;
  }

  .music-player .song-artist {
    font-size: 0.7rem !important;
  }

  /* Modal mobile styles */
  .modal-dialog {
    margin: 0.5rem !important;
  }

  .modal-content {
    border-radius: 1rem !important;
  }

  .modal-body {
    padding: 1rem !important;
  }

  /* Search bar mobile styles */
  .search-bar {
    height: 45px !important;
  }

  /* Playlist mobile styles */
  .playlist-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem !important;
  }

  /* Stats section mobile styles */
  .stat-button {
    padding: 0.75rem !important;
  }

  .stat-icon {
    font-size: 1.25rem !important;
    margin-right: 0.5rem !important;
  }

  .stat-value {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
  }

  .stat-label {
    font-size: 0.7rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
  }

  /* Suggestions portal mobile styles */
  .suggestions-container {
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    border-radius: 0.75rem !important;
  }
}

/* Extra small devices (phones) */
@media (max-width: 575.98px) {
  /* Further optimize for phones */
  .navbar-brand {
    font-size: 1.1rem;
  }

  /* Optimize song cards for very small screens */
  .song-grid-container {
    gap: 0.5rem !important;
  }

  .song-card .card-body {
    padding: 0.5rem !important;
  }

  /* Make action buttons larger on mobile for better touch targets */
  .song-card .song-action-btn {
    width: 44px !important;
    height: 44px !important;
  }

  .song-card .song-action-btn svg {
    font-size: 22px !important;
  }

  /* Optimize player for very small screens */
  .music-player .song-info {
    max-width: 100px !important;
  }

  .music-player .player-controls button {
    width: 32px !important;
    height: 32px !important;
  }

  .music-player .player-controls .play-pause-btn {
    width: 38px !important;
    height: 38px !important;
  }

  /* Optimize song detail page */
  .song-detail-container .song-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .song-detail-container .song-actions button {
    flex: 1 0 auto;
    min-width: 120px;
  }

  /* Optimize modals */
  .modal-header {
    padding: 0.75rem 1rem !important;
  }

  .modal-footer {
    padding: 0.75rem 1rem !important;
  }

  /* Optimize stats section */
  .stat-button {
    padding: 0.5rem !important;
  }

  .stat-icon {
    font-size: 1rem !important;
  }

  /* Optimize featured section */
  .featured-section .featured-image {
    height: 150px !important;
  }

  /* Optimize search suggestions */
  .suggestion-item {
    padding: 0.5rem !important;
  }

  /* Further optimize search bar for very small screens */
  .custom-search-bar .form-control {
    font-size: 0.85rem !important;
    padding-left: 0.75rem !important;
  }

  .custom-search-bar .search-button {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    min-width: 90px !important;
    border-width: 2px !important;
  }

  .search-button .search-icon {
    font-size: 1rem !important;
  }

  .search-button .search-text {
    font-size: 0.8rem !important;
  }
}

/* Search bar styles */
.search-container {
  width: 80% !important;
  max-width: 80% !important;
  margin-left: auto !important;
  margin-right: auto !important;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Custom search bar styling */
.custom-search-bar {
  width: 100%;
  border-radius: 50rem !important;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease;
}

.custom-search-bar:focus-within {
  box-shadow: 0 5px 20px rgba(0, 255, 209, 0.3) !important;
  transform: translateY(-2px);
}

.custom-search-bar .form-control {
  background-color: rgba(15, 15, 15, 0.7) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-right: none !important;
  border-top-left-radius: 50rem !important;
  border-bottom-left-radius: 50rem !important;
  padding-left: 1.25rem !important;
  backdrop-filter: blur(10px) !important;
  font-size: 0.95rem !important;
  height: 48px !important;
}

.custom-search-bar .input-group-text {
  background-color: rgba(15, 15, 15, 0.7) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-right: none !important;
  border-top-left-radius: 50rem !important;
  border-bottom-left-radius: 50rem !important;
  padding-left: 1.25rem !important;
  backdrop-filter: blur(10px) !important;
}

.custom-search-bar .form-control::placeholder {
  color: rgba(255, 255, 255, 0.7) !important;
}

.custom-search-bar .search-button {
  background-color: var(--dark-accent) !important;
  color: #ffffff !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  border-top-right-radius: 50rem !important;
  border-bottom-right-radius: 50rem !important;
  padding-left: 1.25rem !important;
  padding-right: 1.25rem !important;
  font-weight: 600 !important;
  height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 0 10px rgba(0, 255, 209, 0.2) !important;
}

.custom-search-bar .search-button:hover {
  background-color: rgba(0, 255, 209, 0.8) !important;
  box-shadow: 0 0 15px rgba(0, 255, 209, 0.4) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

.custom-search-bar .search-button .search-icon {
  color: #ffffff !important;
}

.custom-search-bar .search-button .search-text {
  color: #ffffff !important;
}

/* Ensure search button text is visible on larger screens */
@media (min-width: 768px) {
  .search-button .search-text {
    display: inline-block !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
  }

  .custom-search-bar .search-button {
    min-width: 120px !important;
  }
}

/* Adjust search container width for different screen sizes */
@media (min-width: 1400px) {
  .search-container {
    width: 80% !important;
    max-width: 1000px !important;
  }
}

@media (min-width: 992px) and (max-width: 1399.98px) {
  .search-container {
    width: 80% !important;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .search-container {
    width: 80% !important;
  }
}

@media (max-width: 767.98px) {
  .search-container {
    width: 90% !important;
    max-width: 90% !important;
  }

  .custom-search-bar .form-control {
    height: 45px !important;
    font-size: 0.9rem !important;
    padding-left: 1rem !important;
  }

  .custom-search-bar .search-button {
    height: 45px !important;
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    min-width: 100px !important;
    border-width: 2px !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
  }

  /* Style search text on small screens */
  .search-button .search-text {
    display: inline-block !important;
    color: #ffffff !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  }

  /* Style search icon on small screens */
  .search-button .search-icon {
    margin-right: 0.5rem !important;
    font-size: 1.1rem !important;
    color: #ffffff !important;
  }

  /* Specific fixes for home and search pages */
  .home-search,
  .search-page-search {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    width: 95% !important;
    max-width: 95% !important;
  }
}

/* Global font consistency across all device sizes */
@media (max-width: 991.98px) {
  /* Ensure font family consistency */
  body, button, input, select, textarea {
    font-family: 'Poppins', sans-serif !important;
  }

  /* Ensure proper font weights */
  .font-weight-bold, .fw-bold, strong, b {
    font-weight: 600 !important;
  }

  .font-weight-medium, .fw-medium {
    font-weight: 500 !important;
  }

  .font-weight-normal, .fw-normal {
    font-weight: 400 !important;
  }

  .font-weight-light, .fw-light {
    font-weight: 300 !important;
  }
}

/* Global color styles - consistent across all device sizes */
/* Dark theme text colors */
.dark-theme h1, .dark-theme h2, .dark-theme h3, .dark-theme h4, .dark-theme h5, .dark-theme h6,
.dark-theme .h1, .dark-theme .h2, .dark-theme .h3, .dark-theme .h4, .dark-theme .h5, .dark-theme .h6 {
  color: #ffffff !important;
}

.dark-theme p, .dark-theme span, .dark-theme div {
  color: #ffffff !important;
}

.dark-theme .text-muted, .dark-theme .muted-text {
  color: var(--dark-muted) !important;
}

.dark-theme .accent-text, .dark-theme .text-accent, .dark-theme .accent-color {
  color: var(--dark-accent) !important;
}

/* Light theme text colors */
.light-theme h1, .light-theme h2, .light-theme h3, .light-theme h4, .light-theme h5, .light-theme h6,
.light-theme .h1, .light-theme .h2, .light-theme .h3, .light-theme .h4, .light-theme .h5, .light-theme .h6 {
  color: var(--light-text) !important;
}

.light-theme p, .light-theme span, .light-theme div {
  color: var(--light-text) !important;
}

.light-theme .text-muted, .light-theme .muted-text {
  color: var(--light-muted) !important;
}

.light-theme .accent-text, .light-theme .text-accent, .light-theme .accent-color {
  color: var(--light-accent) !important;
}

/* Song card colors */
.song-card .card-title {
  color: var(--dark-text) !important;
}

.song-card .card-text {
  color: var(--dark-muted) !important;
}

.song-card:hover .card-title {
  color: var(--dark-accent) !important;
}

/* Song detail colors */
.song-detail-container .song-title {
  color: var(--dark-text) !important;
}

.song-detail-container .song-artist {
  color: var(--dark-muted) !important;
}

.song-detail-container .song-info-label {
  color: var(--dark-accent) !important;
}

.song-detail-container .song-info-value {
  color: var(--dark-text) !important;
}

/* Music player colors */
.music-player .song-title {
  color: var(--dark-text) !important;
}

.music-player .song-artist {
  color: var(--dark-muted) !important;
}

.music-player .player-controls button {
  color: var(--dark-text) !important;
}

.music-player .player-controls button:hover,
.music-player .player-controls button:active {
  color: var(--dark-accent) !important;
}

/* Stats section colors */
.stat-value {
  color: var(--dark-text) !important;
}

.stat-label {
  color: var(--dark-muted) !important;
}

.stat-icon {
  color: var(--dark-accent) !important;
}
