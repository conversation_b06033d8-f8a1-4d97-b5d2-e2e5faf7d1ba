:root {
  --primary-color: #00FFD1;
  --dark-bg: #0f0f0f;
  --light-text: #E0E0E0;
  --seekbar-height: 6px;
  --seekbar-hover-height: 8px;
  --thumb-size: 16px;
  --thumb-hover-size: 20px;
}

/* Modern Audio Player */
.modern-audio-player {
  position: relative;
  width: 100%;
  border-radius: 16px;
  padding: 15px;
  background-color: rgba(15, 15, 15, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  color: var(--light-text);
}

.dark-theme {
  background-color: rgba(15, 15, 15, 0.9);
  color: var(--light-text);
}

.light-theme {
  background-color: rgba(15, 15, 15, 0.9);
  color: var(--light-text);
}

/* Close button */
.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(0, 255, 209, 0.3);
}

/* Song info */
.song-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.song-image {
  margin-right: 12px;
  flex-shrink: 0;
}

.song-image img {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.song-details {
  flex-grow: 1;
  overflow: hidden;
}

.song-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #ffffff;
}

.song-artist {
  font-size: 0.9rem;
  color: #e0e0e0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Seekbar */
.seekbar-container {
  position: relative;
  height: 25px;
  display: flex;
  align-items: center;
  cursor: pointer;
  touch-action: none;
  margin-bottom: 5px;
}

.seekbar-bg {
  position: absolute;
  left: 0;
  right: 0;
  height: var(--seekbar-height);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  transition: height 0.2s ease;
}

.seekbar-container:hover .seekbar-bg,
.seekbar-container.active .seekbar-bg {
  height: var(--seekbar-hover-height);
}

.seekbar-progress {
  position: absolute;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, rgba(0, 255, 209, 0.8) 100%);
  border-radius: 10px;
  transition: width 0.1s linear;
}

.seekbar-thumb {
  position: absolute;
  top: 50%;
  width: var(--thumb-size);
  height: var(--thumb-size);
  background-color: #fff;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px rgba(0, 255, 209, 0.5);
  z-index: 10;
  opacity: 0;
  transition: width 0.2s ease, height 0.2s ease, opacity 0.2s ease;
}

.seekbar-container:hover .seekbar-thumb,
.seekbar-container.active .seekbar-thumb {
  opacity: 1;
  width: var(--thumb-hover-size);
  height: var(--thumb-hover-size);
}

/* Time display */
.time-display {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 0.8rem;
  color: #e0e0e0;
  font-weight: 500;
}

.time-display .current-time,
.time-display .duration {
  color: #e0e0e0;
}

/* Controls */
.player-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-btn {
  width: 38px;
  height: 38px;
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(0, 255, 209, 0.3);
}

.control-btn:active {
  transform: scale(0.95);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.play-pause-btn {
  width: 48px;
  height: 48px;
  background-color: var(--primary-color);
  color: #000000;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 12px;
  box-shadow: 0 0 15px rgba(0, 255, 209, 0.5);
  font-size: 1.1rem;
}

.play-pause-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(0, 255, 209, 0.7);
}

.play-pause-btn:active {
  transform: scale(0.95);
}

/* Volume control */
.volume-container {
  display: flex;
  align-items: center;
}

.volume-slider {
  width: 80px;
  height: 4px;
  -webkit-appearance: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  outline: none;
  margin-left: 10px;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  border: none;
  border-radius: 50%;
  cursor: pointer;
}

/* Mobile optimizations */
@media (max-width: 576px) {
  .modern-audio-player {
    padding: 12px;
    background-color: rgba(15, 15, 15, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.4);
  }

  .song-image img {
    width: 45px;
    height: 45px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  }

  .song-title {
    font-size: 0.95rem;
    color: #ffffff !important;
    font-weight: 600;
  }

  .song-artist {
    font-size: 0.75rem;
    color: #e0e0e0 !important;
  }

  .play-pause-btn {
    width: 42px;
    height: 42px;
    margin: 0 8px;
    background-color: var(--primary-color);
    color: #000000;
    box-shadow: 0 0 15px rgba(0, 255, 209, 0.4);
  }

  .control-btn {
    width: 34px;
    height: 34px;
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .volume-slider {
    width: 50px;
  }

  /* Make the seekbar easier to interact with on mobile */
  .seekbar-bg {
    height: 8px;
    background-color: rgba(255, 255, 255, 0.15);
  }

  .seekbar-progress {
    background: linear-gradient(90deg, var(--primary-color) 0%, rgba(0, 255, 209, 0.8) 100%);
  }

  .seekbar-container {
    height: 22px;
  }

  .seekbar-container:hover .seekbar-bg,
  .seekbar-container.active .seekbar-bg {
    height: 10px;
  }

  .seekbar-thumb {
    width: 20px;
    height: 20px;
    opacity: 0.8;
    background-color: #ffffff;
    border: 2px solid var(--primary-color);
  }

  .seekbar-container:hover .seekbar-thumb,
  .seekbar-container.active .seekbar-thumb {
    width: 24px;
    height: 24px;
    opacity: 1;
  }

  .time-display {
    margin-bottom: 8px;
  }

  .time-display .current-time,
  .time-display .duration {
    color: #e0e0e0;
    font-weight: 500;
  }
}
