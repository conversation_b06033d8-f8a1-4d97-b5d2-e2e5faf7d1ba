// Import Bootstrap
@import "bootstrap/scss/bootstrap";

// Import Bootstrap icons
@import "bootstrap-icons/font/bootstrap-icons.css";

// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

// Custom Variables
:root {
  // Light Theme Colors
  --light-bg: #f8f9fa;
  --light-text: #212529;
  --light-primary: #6f42c1;
  --light-secondary: #5e60ce;
  --light-accent: #7b2cbf;
  --light-muted: #6c757d;
  --light-border: #dee2e6;
  --light-card-bg: #ffffff;

  // Dark Theme Colors
  --dark-bg: #0f0f0f;
  --dark-text: #e0e0e0;
  --dark-primary: #9d4edd;
  --dark-secondary: #7b2cbf;
  --dark-accent: #00ffd1; // Neon aqua accent
  --dark-muted: #adb5bd;
  --dark-border: #343a40;
  --dark-card-bg: #1a1a1a;
}

// Base Styles
body {
  font-family: 'Poppins', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

// Light Theme
.light-theme {
  background-color: var(--light-bg);
  color: var(--light-text);

  .navbar {
    background-color: var(--light-card-bg);
  }

  .card {
    background-color: var(--light-card-bg);
    border-color: var(--light-border);
  }

  .btn-primary {
    background-color: var(--light-primary);
    border-color: var(--light-primary);
  }

  .text-muted {
    color: var(--light-muted) !important;
  }
}

// Dark Theme
.dark-theme {
  background-color: var(--dark-bg);
  color: var(--dark-text);

  .navbar {
    background-color: var(--dark-card-bg);
  }

  .card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
  }

  .btn-primary {
    background-color: var(--dark-primary);
    border-color: var(--dark-primary);
  }

  .text-muted {
    color: var(--dark-muted) !important;
  }

  .accent-text {
    color: var(--dark-accent);
  }
}

// Common Styles
.song-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  .card-img-overlay {
    opacity: 0;
    background: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      opacity: 1;
    }
  }
}

.featured-section {
  border-radius: 1rem;
  overflow: hidden;
  position: relative;

  .featured-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 2rem;
  }
}

.audio-player {
  border-radius: 1rem;
  padding: 1rem;

  .progress {
    height: 6px;
    cursor: pointer;
  }

  .controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    button {
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;

      &.play-pause {
        width: 50px;
        height: 50px;
      }
    }
  }
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

// Responsive Adjustments
@media (max-width: 768px) {
  .song-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 576px) {
  .song-grid {
    grid-template-columns: 1fr !important;
  }
}

// Utility Classes
.cursor-pointer {
  cursor: pointer;
}

// Update Alert Styles
.hover-effect {
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
  }
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.hover-scale {
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.hover-bg-light {
  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }

  .dark-theme & {
    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }
  }
}

.transition-all {
  transition: all 0.2s ease;
}

.opacity-0 {
  opacity: 0;
}

.hover-opacity-100 {
  &:hover {
    opacity: 1 !important;
  }
}

// Modern Header & Footer Styles
.social-icon-link {
  display: flex;
  align-items: center;
  color: var(--dark-muted);
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: var(--dark-accent);
  }
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background-color: rgba(157, 78, 221, 0.15);
  border-radius: 50%;
  transition: all 0.3s ease;

  svg {
    font-size: 20px;
  }

  &:hover {
    transform: translateY(-3px);
    background-color: rgba(0, 255, 209, 0.15);
    box-shadow: 0 5px 15px rgba(0, 255, 209, 0.2);
  }
}

.hover-link {
  position: relative;
  transition: color 0.3s ease;

  &:after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: -2px;
    left: 0;
    background-color: var(--dark-accent);
    transition: width 0.3s ease;
  }

  &:hover {
    color: var(--dark-accent) !important;

    &:after {
      width: 100%;
    }
  }
}

// Song details page styling
.song-details-page {
  .rounded-4 {
    border-radius: 1rem !important;
  }

  .rounded-3 {
    border-radius: 0.75rem !important;
  }

  .fw-medium {
    font-weight: 500;
  }

  .fw-semibold {
    font-weight: 600;
  }
}

// Modern Home Page Styling
.home-page {
  .hero-section {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(0, 255, 209, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
      z-index: -1;
      animation: pulse 15s infinite alternate;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.5;
    }
    100% {
      transform: scale(1.2);
      opacity: 0.8;
    }
  }

  .icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(15, 15, 15, 0.5);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    font-size: 1.25rem;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }
  }

  .stats-buttons-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .stat-button {
    background: linear-gradient(135deg, rgba(157, 78, 221, 0.2) 0%, rgba(0, 255, 209, 0.2) 100%);
    border-radius: 50px;
    padding: 10px 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(0, 255, 209, 0.1) 0%, rgba(157, 78, 221, 0.1) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }

    .stat-content {
      position: relative;
      z-index: 1;
    }

    .stat-icon {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: rgba(0, 255, 209, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      color: var(--dark-accent);
      font-size: 16px;
      flex-shrink: 0;
    }

    .stat-text {
      text-align: left;
    }

    .stat-value {
      font-weight: 700;
      font-size: 1.1rem;
      color: var(--dark-text);
      line-height: 1.2;
    }

    .stat-label {
      font-size: 0.8rem;
      color: var(--dark-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .section-header {
    position: relative;
    margin-bottom: 1.5rem;

    &::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 0;
      width: 40px;
      height: 3px;
      background-color: var(--dark-accent);
      border-radius: 3px;
    }
  }
}

// This is now handled inline in the SongGrid component
// Keeping this for any legacy references
.song-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 24px;
  margin-bottom: 2rem;

  @media (max-width: 992px) {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 20px;
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  @media (max-width: 576px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 1.5rem;
  }
}

.author-tooltip {
  position: absolute;
  background-color: var(--dark-card-bg);
  color: var(--dark-text);
  padding: 1.25rem;
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  width: 300px;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  transform: translateY(10px) translateX(-50%);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);

  &.visible {
    opacity: 1;
    transform: translateY(0) translateX(-50%);
  }

  ul {
    margin-bottom: 0;

    li {
      margin-bottom: 0.25rem;
      font-size: 0.9rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Custom scrollbar and styling for suggestions portal
.suggestions-portal-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border: 2px solid transparent;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  // Responsive heights based on screen size
  @media (max-width: 768px) {
    max-height: 250px !important;
  }

  @media (max-width: 576px) {
    max-height: 200px !important;
  }

  // Ensure content is scrollable
  .suggestion-item {
    min-height: 40px;
    padding: 8px 12px;
    border-radius: 8px;
    margin-bottom: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    &:active {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  // Ensure it's always on top of everything
  position: absolute !important;
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch; // Smooth scrolling on iOS
  z-index: 9999999 !important; // Ultra high z-index

  // Add a subtle animation
  animation: fadeIn 0.2s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // Improved typography for better readability
  font-size: 0.95rem;
  line-height: 1.4;
}

// Portal container styles
#suggestions-portal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  z-index: 9999999;
  pointer-events: none;

  // Allow pointer events only on the suggestions container
  .suggestions-portal-container {
    pointer-events: auto;
  }
}

// Song list styles
.song-table {
  border-collapse: separate;
  border-spacing: 0;

  th {
    border-top: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--dark-text);
    font-weight: 500;
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  td {
    border: none;
    padding: 0.75rem;
    vertical-align: middle;
  }

  tbody tr {
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05) !important;
    }

    &.table-active {
      background-color: rgba(0, 255, 209, 0.05) !important;

      &:hover {
        background-color: rgba(0, 255, 209, 0.1) !important;
      }
    }
  }
}

// Playing animation
.playing-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  height: 16px;

  span {
    display: inline-block;
    width: 3px;
    height: 100%;
    background-color: var(--dark-accent);
    border-radius: 1px;
    animation: sound 1.2s ease-in-out infinite;

    &:nth-child(1) {
      animation-delay: 0s;
      height: 8px;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
      height: 16px;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
      height: 10px;
    }
  }

  @keyframes sound {
    0% {
      transform: scaleY(0.5);
    }
    50% {
      transform: scaleY(1);
    }
    100% {
      transform: scaleY(0.5);
    }
  }
}

// Playlist page styles
.playlists-page {
  .hero-section {
    margin-left: 1rem;
    margin-right: 1rem;

    @media (max-width: 768px) {
      margin-left: 0;
      margin-right: 0;
    }
  }

  // Hover zoom effect for images
  .hover-zoom {
    &:hover {
      transform: scale(1.05);
      filter: brightness(1) !important;
    }
  }

  // Card styling
  .card {
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2) !important;
      border: 1px solid rgba(0, 255, 209, 0.1) !important;
    }
  }
}