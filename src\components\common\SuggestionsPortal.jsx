import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaSearch, FaHistory, FaMusic } from 'react-icons/fa';

const SuggestionsPortal = ({
  isVisible,
  suggestions,
  recentSearches,
  query,
  inputRef,
  onSuggestionClick,
  onRecentSearchClick,
  onRemoveSuggestion,
  onRemoveRecentSearch,
  onClose,
  isLoading,
  highlightMatch
}) => {
  const [portalNode, setPortalNode] = useState(null);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });

  // Create portal node on mount
  useEffect(() => {
    // Remove any existing portal to avoid duplicates
    const existingPortal = document.getElementById('suggestions-portal');
    if (existingPortal) {
      document.body.removeChild(existingPortal);
    }

    // Create a new portal container
    const node = document.createElement('div');
    node.id = 'suggestions-portal';
    node.style.position = 'absolute';
    node.style.zIndex = '2147483647'; // Maximum possible z-index
    node.style.pointerEvents = 'auto';
    document.body.appendChild(node);
    setPortalNode(node);

    // Add global styles
    const styleId = 'suggestions-global-style';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        #suggestions-portal {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 0;
          pointer-events: none;
          z-index: 2147483647;
        }

        .suggestions-container {
          position: absolute;
          pointer-events: auto !important;
          cursor: pointer !important;
        }

        .suggestion-item {
          position: relative;
          cursor: pointer !important;
          user-select: none;
          transition: all 0.2s ease;
        }

        .suggestion-item:hover {
          background-color: rgba(255, 255, 255, 0.15) !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        }

        .suggestion-item:active {
          transform: translateY(0) !important;
          background-color: rgba(255, 255, 255, 0.1) !important;
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      if (node && document.body.contains(node)) {
        document.body.removeChild(node);
      }
      const styleEl = document.getElementById(styleId);
      if (styleEl) {
        document.head.removeChild(styleEl);
      }
    };
  }, []);

  // Update position when input reference changes or window resizes
  useEffect(() => {
    const updatePosition = () => {
      if (inputRef && inputRef.current) {
        const rect = inputRef.current.getBoundingClientRect();
        const isMobile = window.innerWidth < 768;

        if (isMobile) {
          // On mobile, make the suggestions take up more width
          setPosition({
            top: rect.bottom + window.scrollY + 5, // Add a small gap
            left: Math.max(10, rect.left + window.scrollX - 10), // Slightly wider than input
            width: Math.min(window.innerWidth - 20, rect.width + 20) // Wider but not wider than screen
          });
        } else {
          // On desktop, keep it aligned with the input
          setPosition({
            top: rect.bottom + window.scrollY + 5,
            left: rect.left + window.scrollX,
            width: rect.width
          });
        }
      }
    };

    if (isVisible) {
      updatePosition();
      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition);
    }

    return () => {
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);
    };
  }, [inputRef, isVisible]);

  if (!portalNode || !isVisible) return null;

  return createPortal(
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="suggestions-container"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          style={{
            position: 'absolute',
            top: `${position.top}px`,
            left: `${position.left}px`,
            width: `${position.width}px`,
            backgroundColor: 'rgba(15, 15, 15, 0.95)',
            backdropFilter: 'blur(10px)',
            borderRadius: '1rem',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)',
            border: '1px solid rgba(255, 255, 255, 0.05)',
            zIndex: 2147483647, // Maximum possible z-index
            maxHeight: window.innerWidth < 768 ? '250px' : '300px',
            overflowY: 'auto',
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(255, 255, 255, 0.1) transparent',
            overscrollBehavior: 'contain',
            pointerEvents: 'auto', // Ensure clicks are captured
            cursor: 'default'
          }}
          onClick={(e) => e.stopPropagation()} // Prevent clicks from bubbling up
        >
          {/* Close button */}
          <button
            className="position-absolute"
            onClick={onClose}
            style={{
              top: '10px',
              right: '10px',
              background: 'transparent',
              border: 'none',
              color: 'rgba(255, 255, 255, 0.5)',
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1
            }}
            onMouseEnter={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.5)'}
          >
            <FaTimes size={14} />
          </button>

          {/* Recent searches - only show when there's no input */}
          {recentSearches.length > 0 && query.trim().length < 2 && (
            <div className="p-3 suggestions-content">
              <div className="d-flex align-items-center mb-3">
                <FaHistory className="me-2 text-muted" size={14} />
                <span className="small text-muted" style={{ color: '#ffffff !important' }}>Recent Searches</span>
              </div>
              {recentSearches.map((term, index) => (
                <div key={`recent-${index}`} className="d-flex align-items-center justify-content-between mb-1">
                  <button
                    className="suggestion-item d-flex align-items-center p-2 rounded w-100"
                    onMouseDown={(e) => {
                      e.preventDefault(); // Prevent blur
                      e.stopPropagation();
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('Recent search clicked:', term);
                      onRecentSearchClick(term);
                    }}
                    style={{
                      transition: 'all 0.2s ease',
                      cursor: 'pointer',
                      position: 'relative',
                      zIndex: 10000000,
                      background: 'transparent',
                      border: 'none',
                      textAlign: 'left',
                      color: 'var(--dark-text)',
                      flexGrow: 1
                    }}
                  >
                    <div className="d-flex align-items-center flex-grow-1 text-truncate">
                      <FaSearch className="me-2 text-muted" size={12} style={{ flexShrink: 0 }} />
                      <span className="text-truncate" style={{ color: '#ffffff' }}>
                        {query.trim() ? highlightMatch(term, query) : term}
                      </span>
                      <span className="ms-2 text-muted" style={{ fontSize: '0.7rem', opacity: 0.7, color: '#ffffff !important' }}>Click to search</span>
                    </div>
                  </button>
                  <button
                    className="btn btn-sm p-0 ms-2"
                    onMouseDown={(e) => {
                      e.preventDefault(); // Prevent blur
                      e.stopPropagation();
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRemoveRecentSearch(term);
                    }}
                    style={{
                      background: 'transparent',
                      border: 'none',
                      color: 'rgba(255, 255, 255, 0.5)',
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexShrink: 0
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.5)'}
                  >
                    <FaTimes size={12} />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Song suggestions - only show when user is typing */}
          {query.trim().length >= 2 && (
            isLoading ? (
              <div className="p-3">
                <div className="d-flex align-items-center mb-3">
                  <FaMusic className="me-2 text-muted" size={14} />
                  <span className="small text-muted" style={{ color: '#ffffff !important' }}>Searching...</span>
                </div>
                {/* Shimmer loading effect for suggestions */}
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="d-flex align-items-center p-2 mb-2">
                    <div className="rounded overflow-hidden me-2" style={{ width: '30px', height: '30px' }}>
                      <div className="shimmer-item" style={{ width: '100%', height: '100%', backgroundColor: 'rgba(255, 255, 255, 0.05)' }}>
                        <div className="shimmer-animation" />
                      </div>
                    </div>
                    <div className="flex-grow-1">
                      <div className="shimmer-item mb-1" style={{ height: '0.9rem', width: '80%', backgroundColor: 'rgba(255, 255, 255, 0.05)' }}>
                        <div className="shimmer-animation" />
                      </div>
                      <div className="shimmer-item" style={{ height: '0.7rem', width: '60%', backgroundColor: 'rgba(255, 255, 255, 0.05)' }}>
                        <div className="shimmer-animation" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              suggestions.length > 0 && (
                <div className="p-3 suggestions-content">
                  <div className="d-flex align-items-center mb-3">
                    <FaMusic className="me-2 text-muted" size={14} />
                    <span className="small text-muted" style={{ color: '#ffffff !important' }}>Suggestions</span>
                  </div>
                  {suggestions.map(suggestion => (
                    <div key={suggestion.id} className="d-flex align-items-center justify-content-between mb-1">
                      <button
                        className="suggestion-item d-flex align-items-center p-2 rounded w-100"
                        onMouseDown={(e) => {
                          e.preventDefault(); // Prevent blur
                          e.stopPropagation();
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          console.log('Suggestion clicked:', suggestion.name);
                          onSuggestionClick(suggestion);
                        }}
                        style={{
                          transition: 'all 0.2s ease',
                          cursor: 'pointer',
                          position: 'relative',
                          zIndex: 10000000,
                          background: 'transparent',
                          border: 'none',
                          textAlign: 'left',
                          color: 'var(--dark-text)',
                          flexGrow: 1
                        }}
                      >
                        <div className="d-flex align-items-center flex-grow-1 text-truncate">
                          {suggestion.image && suggestion.image[0] && (
                            <img
                              src={suggestion.image[0].url}
                              alt={suggestion.name}
                              className="me-2 rounded"
                              style={{ width: '30px', height: '30px', objectFit: 'cover', flexShrink: 0 }}
                            />
                          )}
                          <div className="text-truncate" style={{ maxWidth: '100%' }}>
                            <div className="text-truncate" style={{ color: '#ffffff' }}>
                              {highlightMatch(suggestion.name, query)}
                              <span className="ms-2 badge" style={{
                                backgroundColor: 'rgba(0, 255, 209, 0.2)',
                                color: 'var(--dark-accent)',
                                fontSize: '0.65rem'
                              }}>Click to play</span>
                            </div>
                            <small className="text-muted text-truncate d-block">
                              {suggestion.artists && suggestion.artists.primary &&
                                highlightMatch(suggestion.artists.primary.map(artist => artist.name).join(', '), query)}
                            </small>
                          </div>
                        </div>
                      </button>
                      <button
                        className="btn btn-sm p-0 ms-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          onRemoveSuggestion(suggestion.id);
                        }}
                        style={{
                          background: 'transparent',
                          border: 'none',
                          color: 'rgba(255, 255, 255, 0.5)',
                          width: '24px',
                          height: '24px',
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)'}
                        onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.5)'}
                      >
                        <FaTimes size={12} />
                      </button>
                    </div>
                  ))}
                </div>
              )
            )
          )}

          {!isLoading && query.trim().length >= 2 && suggestions.length === 0 && (
            <div className="p-3 text-center text-muted" style={{ color: '#ffffff !important' }}>
              No suggestions found for "{query}"
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>,
    portalNode
  );
};

export default SuggestionsPortal;
