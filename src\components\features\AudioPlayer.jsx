import { useState, useEffect, useRef } from 'react';
import { FaPlay, FaPause, FaStepForward, FaStepBackward, FaVolumeUp, FaVolumeMute, FaTimes, FaRandom, FaHeart } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';
import { useSong } from '../../context/SongContext';

const AudioPlayer = ({ song, onNext, onPrevious, onClose }) => {
  const {
    isPlaying: contextIsPlaying,
    pauseSong,
    resumeSong
  } = useSong();
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragProgress, setDragProgress] = useState(null);
  const [showVolumeControl, setShowVolumeControl] = useState(false);
  const [previewTime, setPreviewTime] = useState(null);

  const audioRef = useRef(null);
  const progressRef = useRef(null);
  const thumbRef = useRef(null);
  const { darkMode } = useTheme();

  // Use the playing state from context
  const isPlaying = contextIsPlaying;

  // Get audio URL from song
  const getAudioUrl = () => {
    if (!song || !song.downloadUrl || song.downloadUrl.length === 0) {
      return '';
    }

    // Find high quality audio
    const highQualityAudio = song.downloadUrl.find(audio => audio.quality === '320kbps');
    if (highQualityAudio && highQualityAudio.url) return highQualityAudio.url;

    // If 320kbps not found or url is empty, try 160kbps
    const mediumQualityAudio = song.downloadUrl.find(audio => audio.quality === '160kbps');
    if (mediumQualityAudio && mediumQualityAudio.url) return mediumQualityAudio.url;

    // If no specific quality found, use the first available URL
    for (const audio of song.downloadUrl) {
      if (audio.url) return audio.url;
    }

    return '';
  };

  // Effect for handling song changes
  useEffect(() => {
    // Reset player when song changes
    if (audioRef.current && song) {
      // Pause current audio
      audioRef.current.pause();
      setCurrentTime(0);

      // Get the audio URL
      const audioUrl = getAudioUrl();

      if (audioUrl) {
        // Load new audio
        audioRef.current.src = audioUrl;
        audioRef.current.load();

        // Auto play when song changes
        const playPromise = audioRef.current.play();

        if (playPromise !== undefined) {
          playPromise.then(() => {
            console.log('Playing audio:', audioUrl);
          }).catch(error => {
            console.error('Auto play failed:', error);
            // Some browsers require user interaction before autoplay
            // We'll show the play button instead
          });
        }
      } else {
        console.error('No valid audio URL found for song:', song);
      }
    }
  }, [song]);

  // Effect for syncing with isPlaying state from context
  useEffect(() => {
    if (audioRef.current) {
      if (isPlaying) {
        const playPromise = audioRef.current.play();
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.error('Play failed in sync effect:', error);
          });
        }
      } else {
        audioRef.current.pause();
      }
    }
  }, [isPlaying]);

  useEffect(() => {
    // Update audio element when volume changes
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
    }
  }, [volume, isMuted]);

  const togglePlay = () => {
    if (isPlaying) {
      audioRef.current.pause();
      console.log('Paused audio');
      pauseSong();
    } else {
      const playPromise = audioRef.current.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          console.log('Playing audio manually');
          resumeSong();
        }).catch(error => {
          console.error('Play failed:', error);
        });
      }
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleTimeUpdate = () => {
    setCurrentTime(audioRef.current.currentTime);
  };

  const handleLoadedMetadata = () => {
    setDuration(audioRef.current.duration);
  };

  // Calculate progress percentage based on mouse/touch position
  const calculateProgress = (clientX) => {
    const progressRect = progressRef.current.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (clientX - progressRect.left) / progressRect.width));
    return percent;
  };

  // Handle mouse/touch down on progress bar
  const handleProgressMouseDown = (e) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent event bubbling

    // Handle both mouse and touch events
    const clientX = e.clientX || (e.touches && e.touches[0].clientX);
    if (!clientX) return;

    setIsDragging(true);

    // Calculate the new progress percentage
    const percent = calculateProgress(clientX);
    setDragProgress(percent);

    // Update preview time
    const newPreviewTime = percent * duration;
    setPreviewTime(newPreviewTime);

    // Add event listeners for drag and release
    document.addEventListener('mousemove', handleProgressMouseMove, { passive: false });
    document.addEventListener('mouseup', handleProgressMouseUp);
    document.addEventListener('touchmove', handleProgressTouchMove, { passive: false });
    document.addEventListener('touchend', handleProgressTouchEnd);
    document.addEventListener('touchcancel', handleProgressTouchEnd);
  };

  // Handle mouse move during drag
  const handleProgressMouseMove = (e) => {
    if (!isDragging) return;

    const clientX = e.clientX;
    const percent = calculateProgress(clientX);
    setDragProgress(percent);

    // Update preview time
    const newPreviewTime = percent * duration;
    setPreviewTime(newPreviewTime);
  };

  // Handle touch move during drag
  const handleProgressTouchMove = (e) => {
    if (!isDragging || !e.touches[0]) return;

    // Prevent default to avoid scrolling while dragging
    e.preventDefault();
    e.stopPropagation();

    const clientX = e.touches[0].clientX;
    const percent = calculateProgress(clientX);
    setDragProgress(percent);

    // Update preview time
    const newPreviewTime = percent * duration;
    setPreviewTime(newPreviewTime);
  };

  // Handle mouse up after drag
  const handleProgressMouseUp = () => {
    if (!isDragging) return;

    try {
      // Apply the new time
      if (dragProgress !== null && audioRef.current) {
        const newTime = dragProgress * duration;

        // Force update the audio element time
        if (audioRef.current) {
          // Set the current time directly on the audio element
          audioRef.current.currentTime = newTime;

          // Force update the UI state
          setCurrentTime(newTime);

          console.log('Seekbar updated to:', newTime, 'seconds');
        }
      }
    } catch (error) {
      console.error('Error updating audio time:', error);
    } finally {
      // Always clean up event listeners
      document.removeEventListener('mousemove', handleProgressMouseMove);
      document.removeEventListener('mouseup', handleProgressMouseUp);
      document.removeEventListener('touchmove', handleProgressTouchMove);
      document.removeEventListener('touchend', handleProgressTouchEnd);
      document.removeEventListener('touchcancel', handleProgressTouchEnd);

      // Reset dragging state
      setIsDragging(false);
      setDragProgress(null);
      setPreviewTime(null);
    }
  };

  // Handle touch end after drag
  const handleProgressTouchEnd = () => {
    handleProgressMouseUp();
  };

  // Legacy click handler for backward compatibility
  const handleProgressClick = (e) => {
    const progressRect = progressRef.current.getBoundingClientRect();
    const percent = (e.clientX - progressRect.left) / progressRect.width;
    const newTime = percent * duration;

    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (time) => {
    if (isNaN(time)) return '0:00';

    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60).toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
  };

  if (!song) return null;

  // Function to handle swipe gestures
  const handleTouchStart = (e) => {
    const touchStartX = e.touches[0].clientX;
    const touchStartY = e.touches[0].clientY;
    let touchEndX, touchEndY;

    const handleTouchMove = (e) => {
      touchEndX = e.touches[0].clientX;
      touchEndY = e.touches[0].clientY;
    };

    const handleTouchEnd = () => {
      if (!touchEndX || !touchEndY) return;

      const deltaX = touchEndX - touchStartX;
      const deltaY = touchEndY - touchStartY;

      // Only consider horizontal swipes (ignore vertical swipes)
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        if (deltaX > 0 && onPrevious) {
          // Swipe right - previous song
          onPrevious();
        } else if (deltaX < 0 && onNext) {
          // Swipe left - next song
          onNext();
        }
      }

      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };

    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);
  };

  return (
    <div
      className="audio-player card shadow-sm position-relative"
      style={{
        backgroundColor: darkMode ? 'var(--dark-card-bg)' : 'var(--light-card-bg)',
        borderRadius: '1rem',
        padding: window.innerWidth < 768 ? '0.75rem 0.5rem 0.75rem 0.5rem' : '1rem',
        boxShadow: '0 -8px 20px rgba(0, 0, 0, 0.2)'
      }}
      onTouchStart={handleTouchStart}
    >
      {/* Close button */}
      {onClose && (
        <button
          className="btn btn-sm btn-dark position-absolute end-0 top-0 m-2 rounded-circle"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Close button clicked');
            // Call onClose and prevent any default behavior
            if (onClose) {
              onClose();
            }
          }}
          style={{ zIndex: 10 }}
          aria-label="Close player"
        >
          <FaTimes />
        </button>
      )}
      <audio
        ref={audioRef}
        src={getAudioUrl()}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => {
          console.log('Song ended, playing next song');
          if (onNext) {
            onNext();
          }
        }}
        onError={(e) => console.error('Audio error:', e)}
        onCanPlay={() => console.log('Audio can play')}
        onPlay={() => console.log('Audio play event')}
        onPause={() => console.log('Audio pause event')}
        preload="auto"
      />

      {/* Responsive layout with different structures for mobile and desktop */}
      <div className="d-none d-md-flex align-items-center mb-3">
        {/* Desktop layout */}
        <div className="me-3">
          <img
            src={song.image && song.image.length > 0 ? song.image[0].url : 'https://via.placeholder.com/50'}
            alt={song.name}
            width="50"
            height="50"
            className="rounded"
          />
        </div>

        <div>
          <h5 className="mb-0">{song.name}</h5>
          <p className="text-muted mb-0">
            {song.artists && song.artists.primary && song.artists.primary.map(artist => artist.name).join(', ')}
          </p>
        </div>
      </div>

      {/* Mobile layout */}
      <div className="d-flex d-md-none align-items-center mb-3">
        <div className="me-3">
          <img
            src={song.image && song.image.length > 0 ?
              (song.image.find(img => img.quality === '500x500')?.url || song.image[0].url)
              : 'https://via.placeholder.com/50'
            }
            alt={song.name}
            width="50"
            height="50"
            className="rounded-2"
            style={{ boxShadow: '0 4px 10px rgba(0, 0, 0, 0.2)' }}
          />
        </div>

        <div className="song-info overflow-hidden flex-grow-1">
          <h6 className="mb-0 text-truncate song-title fw-bold" style={{ fontSize: '0.95rem' }}>
            {song.name}
          </h6>
          <p className="text-muted mb-0 text-truncate song-artist" style={{ fontSize: '0.8rem' }}>
            {song.artists && song.artists.primary && song.artists.primary.map(artist => artist.name).join(', ')}
          </p>
        </div>
      </div>

      <div className="mb-2 progress-container">
        {/* Custom progress bar with draggable thumb */}
        <div
          className="custom-progress-bar"
          ref={progressRef}
          onMouseDown={handleProgressMouseDown}
          onTouchStart={handleProgressMouseDown}
          style={{
            height: '8px',
            cursor: 'pointer',
            borderRadius: '4px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            position: 'relative',
            overflow: 'visible',
            touchAction: 'none' // Prevent browser handling of touch events
          }}
        >
          {/* Progress fill */}
          <div
            className="progress-fill"
            style={{
              position: 'absolute',
              left: 0,
              top: 0,
              height: '100%',
              width: `${isDragging && dragProgress !== null ? dragProgress * 100 : (currentTime / duration) * 100}%`,
              backgroundColor: 'var(--dark-accent)',
              borderRadius: '4px',
              transition: isDragging ? 'none' : 'width 0.1s linear'
            }}
          ></div>

          {/* Draggable thumb */}
          <div
            ref={thumbRef}
            className="progress-thumb"
            style={{
              position: 'absolute',
              top: '50%',
              left: `${isDragging && dragProgress !== null ? dragProgress * 100 : (currentTime / duration) * 100}%`,
              transform: 'translate(-50%, -50%)',
              width: '18px',
              height: '18px',
              borderRadius: '50%',
              backgroundColor: 'var(--dark-accent)',
              boxShadow: '0 0 8px rgba(0, 255, 209, 0.5)',
              opacity: isDragging ? 1 : 0,
              transition: isDragging ? 'none' : 'opacity 0.2s, left 0.1s linear, transform 0.2s ease',
              zIndex: 2,
              touchAction: 'none',
              willChange: 'transform, left' // Optimize for animations
            }}
          >
            {/* Time preview tooltip */}
            {isDragging && previewTime !== null && (
              <div
                className="time-preview-tooltip"
                style={{
                  position: 'absolute',
                  bottom: '24px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  color: 'white',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  whiteSpace: 'nowrap',
                  boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)',
                  zIndex: 3
                }}
              >
                {formatTime(previewTime)}
              </div>
            )}
          </div>

          {/* Hover effect */}
          <div
            className="progress-hover-area"
            style={{
              position: 'absolute',
              top: '-15px',
              left: 0,
              right: 0,
              height: '38px',
              zIndex: 1
            }}
            onMouseEnter={() => thumbRef.current && (thumbRef.current.style.opacity = '1')}
            onMouseLeave={() => !isDragging && thumbRef.current && (thumbRef.current.style.opacity = '0')}
            onTouchStart={() => thumbRef.current && (thumbRef.current.style.opacity = '1')}
          ></div>
        </div>

        <div className="d-flex justify-content-between mt-2">
          <small className="text-muted">
            {isDragging && previewTime !== null ? formatTime(previewTime) : formatTime(currentTime)}
          </small>
          <small className="text-muted">{formatTime(duration)}</small>
        </div>
      </div>

      {/* Desktop controls */}
      <div className="controls d-none d-md-flex justify-content-between align-items-center">
        <div className="d-flex align-items-center gap-3">
          <button
            className="btn btn-sm btn-outline-secondary rounded-circle"
            onClick={onPrevious}
            disabled={!onPrevious}
          >
            <FaStepBackward />
          </button>

          <button
            className="btn btn-primary rounded-circle play-pause"
            onClick={togglePlay}
          >
            {isPlaying ? <FaPause /> : <FaPlay />}
          </button>

          <button
            className="btn btn-sm btn-outline-secondary rounded-circle"
            onClick={onNext}
            disabled={!onNext}
          >
            <FaStepForward />
          </button>
        </div>

        <div className="d-flex align-items-center">
          <button
            className="btn btn-sm btn-link text-muted p-0"
            onClick={toggleMute}
          >
            {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
          </button>

          <input
            type="range"
            className="form-range ms-2"
            min="0"
            max="1"
            step="0.01"
            value={volume}
            onChange={(e) => setVolume(parseFloat(e.target.value))}
            style={{ width: '80px' }}
          />
        </div>
      </div>

      {/* Mobile controls - optimized for touch */}
      <div className="mobile-player-controls d-flex d-md-none justify-content-between align-items-center mt-2 px-2">
        <div className="d-flex align-items-center">
          <motion.button
            className="btn-icon-circle"
            whileTap={{ scale: 0.9 }}
            onClick={toggleMute}
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: 'transparent',
              color: isMuted ? 'var(--dark-accent)' : 'var(--dark-text)',
              border: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative'
            }}
          >
            {isMuted ? <FaVolumeMute size={16} /> : <FaVolumeUp size={16} />}
          </motion.button>

          <motion.button
            className="btn-icon-circle ms-3"
            whileTap={{ scale: 0.9 }}
            onClick={onPrevious}
            disabled={!onPrevious}
            style={{
              width: '36px',
              height: '36px',
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              color: 'var(--dark-text)',
              border: 'none',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <FaStepBackward size={14} />
          </motion.button>
        </div>

        <motion.button
          className="btn-play-circle"
          whileTap={{ scale: 0.9 }}
          onClick={togglePlay}
          style={{
            width: '48px',
            height: '48px',
            backgroundColor: 'var(--dark-accent)',
            color: '#0f0f0f',
            border: 'none',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.2)'
          }}
        >
          {isPlaying ? <FaPause size={18} /> : <FaPlay size={18} style={{ marginLeft: '2px' }} />}
        </motion.button>

        <div className="d-flex align-items-center">
          <motion.button
            className="btn-icon-circle me-3"
            whileTap={{ scale: 0.9 }}
            onClick={onNext}
            disabled={!onNext}
            style={{
              width: '36px',
              height: '36px',
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              color: 'var(--dark-text)',
              border: 'none',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <FaStepForward size={14} />
          </motion.button>

          <motion.button
            className="btn-icon-circle"
            whileTap={{ scale: 0.9 }}
            onClick={() => {}}
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: 'transparent',
              color: 'var(--dark-text)',
              border: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <FaHeart size={16} />
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
