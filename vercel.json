{"$schema": "https://openapi.vercel.sh/vercel.json", "version": 2, "buildCommand": "npm run build", "devCommand": "npm run dev", "outputDirectory": "dist", "framework": "vite", "public": false, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "trailingSlash": false, "headers": [{"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/version.json", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(), interest-cohort=()"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).js", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).css", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/favicon.svg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).png", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "github": {"silent": true, "autoJobCancelation": true}}