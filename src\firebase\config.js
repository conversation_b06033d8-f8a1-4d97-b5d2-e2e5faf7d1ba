
import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getAuth } from "firebase/auth";
import logger from '../utils/logger';
import { validateFirebaseConfig } from '../utils/validateConfig';

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Log Firebase initialization in development only
logger.info('Firebase initialized with config:', {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain
});

// Initialize Firebase with error handling
let app, db, storage, auth;

try {
  // Validate Firebase configuration
  if (!validateFirebaseConfig(firebaseConfig)) {
    throw new Error('Invalid Firebase configuration. Check your environment variables.');
  }

  // Initialize Firebase
  app = initializeApp(firebaseConfig);
  db = getFirestore(app);
  storage = getStorage(app);
  auth = getAuth(app);

  logger.info('Firebase services initialized successfully');
} catch (error) {
  logger.error('Error initializing Firebase:', error);

  // Provide fallbacks to prevent app from crashing
  if (!app) app = { name: 'firebase-fallback' };
  if (!db) db = { collection: () => ({ get: async () => ({ docs: [] }) }) };
  if (!storage) storage = { ref: () => ({ put: async () => ({}) }) };
  if (!auth) auth = {
    currentUser: null,
    onAuthStateChanged: (callback) => { callback(null); return () => {}; }
  };
}

export { db, storage, auth };
