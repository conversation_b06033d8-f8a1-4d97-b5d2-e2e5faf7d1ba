import { useState } from 'react';
import { <PERSON>dal, Button, Form, Alert } from 'react-bootstrap';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'framer-motion';

const ResetPasswordModal = ({ show, onHide, switchToLogin }) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');

  const { resetPassword } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email) {
      setError('Please enter your email');
      return;
    }

    try {
      setError('');
      setMessage('');
      setLoading(true);
      console.log("Attempting to reset password for:", email);
      await resetPassword(email);
      console.log("Password reset email sent");
      setMessage('Check your email for further instructions');
    } catch (err) {
      console.error("Password reset error:", err);
      setError('Failed to reset password: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      contentClassName="bg-dark text-light"
      backdropClassName="backdrop-blur"
    >
      <Modal.Header closeButton closeVariant="white" className="border-0">
        <Modal.Title>Reset Password</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}
        {message && <Alert variant="success">{message}</Alert>}

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Email address</Form.Label>
            <Form.Control
              type="email"
              placeholder="Enter email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="bg-dark text-light border-secondary"
            />
            <Form.Text className="text-muted">
              We'll send a password reset link to this email.
            </Form.Text>
          </Form.Group>

          <div className="d-grid gap-2 mb-3">
            <motion.div whileTap={{ scale: 0.95 }}>
              <Button
                variant="primary"
                type="submit"
                disabled={loading}
                className="w-100"
                style={{ backgroundColor: 'var(--dark-accent)', color: '#2c3e50', border: 'none', fontWeight: '600' }}
              >
                {loading ? 'Sending...' : 'Reset Password'}
              </Button>
            </motion.div>
          </div>
        </Form>

        <div className="mt-3 text-center">
          <p className="mb-0">
            Remember your password?{' '}
            <Button
              variant="link"
              onClick={switchToLogin}
              className="p-0"
              style={{ color: 'var(--dark-accent)' }}
            >
              Sign in
            </Button>
          </p>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ResetPasswordModal;
