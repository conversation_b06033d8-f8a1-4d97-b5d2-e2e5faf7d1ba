import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaPlay, FaPause, FaStepForward, FaStepBackward, 
  FaRandom, FaRedoAlt, FaVolumeUp, FaVolumeMute, 
  FaExpand, FaCompress, FaTimes, FaHeart, FaRegHeart,
  FaListUl, FaEllipsisV, FaDownload, FaShare
} from 'react-icons/fa';
import { useTheme } from '../../../context/ThemeContext';
import { useSong } from '../../../context/SongContext';
import PlayerControls from './PlayerControls';
import SeekBar from './SeekBar';
import VolumeControl from './VolumeControl';
import SongInfo from './SongInfo';
import PlaylistQueue from './PlaylistQueue';
import Equalizer from './Equalizer';
import { useAudioPlayer } from '../../../hooks/useAudioPlayer';
import { useKeyboardShortcuts } from '../../../hooks/useKeyboardShortcuts';
import { useGestures } from '../../../hooks/useGestures';
import './ModernAudioPlayer.scss';

const ModernAudioPlayer = ({ song, onNext, onPrevious, onClose }) => {
  // Context and hooks
  const { darkMode } = useTheme();
  const { 
    isPlaying: contextIsPlaying, 
    pauseSong, 
    resumeSong,
    currentPlaylist,
    currentPlaylistIndex 
  } = useSong();

  // Player state
  const [isExpanded, setIsExpanded] = useState(false);
  const [showQueue, setShowQueue] = useState(false);
  const [showEqualizer, setShowEqualizer] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [repeatMode, setRepeatMode] = useState('none'); // none, one, all
  const [shuffleMode, setShuffleMode] = useState(false);
  const [crossfadeEnabled, setCrossfadeEnabled] = useState(false);
  const [audioQuality, setAudioQuality] = useState('high');

  // Custom hooks
  const {
    audioRef,
    duration,
    currentTime,
    volume,
    isMuted,
    isLoading,
    buffered,
    playbackRate,
    error,
    setVolume,
    setIsMuted,
    setPlaybackRate,
    seek,
    togglePlay,
    formatTime,
    getAudioUrl
  } = useAudioPlayer(song, contextIsPlaying, pauseSong, resumeSong);

  const { isDragging, dragProgress } = useGestures(audioRef, seek);
  
  useKeyboardShortcuts({
    onTogglePlay: togglePlay,
    onNext,
    onPrevious,
    onVolumeUp: () => setVolume(Math.min(1, volume + 0.1)),
    onVolumeDown: () => setVolume(Math.max(0, volume - 0.1)),
    onMute: () => setIsMuted(!isMuted),
    onSeek: seek,
    audioRef
  });

  // Player container ref for gestures
  const playerRef = useRef(null);

  // Handle repeat mode cycling
  const handleRepeatMode = useCallback(() => {
    const modes = ['none', 'one', 'all'];
    const currentIndex = modes.indexOf(repeatMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setRepeatMode(modes[nextIndex]);
  }, [repeatMode]);

  // Handle shuffle toggle
  const handleShuffleToggle = useCallback(() => {
    setShuffleMode(!shuffleMode);
  }, [shuffleMode]);

  // Handle like toggle
  const handleLikeToggle = useCallback(() => {
    setIsLiked(!isLiked);
    // TODO: Implement like functionality with backend
  }, [isLiked]);

  // Handle expand/collapse
  const handleExpandToggle = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  // Handle queue toggle
  const handleQueueToggle = useCallback(() => {
    setShowQueue(!showQueue);
  }, [showQueue]);

  // Handle equalizer toggle
  const handleEqualizerToggle = useCallback(() => {
    setShowEqualizer(!showEqualizer);
  }, [showEqualizer]);

  // Animation variants
  const playerVariants = {
    collapsed: {
      height: 'auto',
      transition: { duration: 0.3, ease: 'easeInOut' }
    },
    expanded: {
      height: '100vh',
      transition: { duration: 0.3, ease: 'easeInOut' }
    }
  };

  const contentVariants = {
    collapsed: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.2 }
    },
    expanded: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3, delay: 0.1 }
    }
  };

  if (!song) return null;

  return (
    <motion.div
      ref={playerRef}
      className={`modern-audio-player ${darkMode ? 'dark-theme' : 'light-theme'} ${isExpanded ? 'expanded' : 'collapsed'}`}
      variants={playerVariants}
      animate={isExpanded ? 'expanded' : 'collapsed'}
      initial="collapsed"
    >
      {/* Background with blur effect */}
      <div 
        className="player-background"
        style={{
          backgroundImage: song.image ? `url(${song.image})` : 'none'
        }}
      />
      
      {/* Main player content */}
      <motion.div 
        className="player-content"
        variants={contentVariants}
      >
        {/* Top bar with controls */}
        <div className="player-top-bar">
          <div className="player-actions-left">
            {isExpanded && (
              <button 
                className="action-btn"
                onClick={handleExpandToggle}
                aria-label="Collapse player"
              >
                <FaCompress />
              </button>
            )}
          </div>
          
          <div className="player-actions-right">
            <button 
              className="action-btn"
              onClick={handleLikeToggle}
              aria-label={isLiked ? "Unlike song" : "Like song"}
            >
              {isLiked ? <FaHeart className="liked" /> : <FaRegHeart />}
            </button>
            
            <button 
              className="action-btn"
              onClick={handleQueueToggle}
              aria-label="Show queue"
            >
              <FaListUl />
            </button>
            
            <button 
              className="action-btn"
              onClick={handleEqualizerToggle}
              aria-label="Show equalizer"
            >
              <FaEllipsisV />
            </button>
            
            {onClose && (
              <button 
                className="action-btn close-btn"
                onClick={onClose}
                aria-label="Close player"
              >
                <FaTimes />
              </button>
            )}
          </div>
        </div>

        {/* Song information */}
        <SongInfo 
          song={song}
          isExpanded={isExpanded}
          onExpandToggle={handleExpandToggle}
        />

        {/* Seek bar */}
        <SeekBar
          currentTime={currentTime}
          duration={duration}
          buffered={buffered}
          isDragging={isDragging}
          dragProgress={dragProgress}
          onSeek={seek}
          formatTime={formatTime}
          isExpanded={isExpanded}
        />

        {/* Player controls */}
        <PlayerControls
          isPlaying={contextIsPlaying}
          isLoading={isLoading}
          repeatMode={repeatMode}
          shuffleMode={shuffleMode}
          onTogglePlay={togglePlay}
          onNext={onNext}
          onPrevious={onPrevious}
          onRepeatMode={handleRepeatMode}
          onShuffleToggle={handleShuffleToggle}
          isExpanded={isExpanded}
        />

        {/* Volume control */}
        <VolumeControl
          volume={volume}
          isMuted={isMuted}
          onVolumeChange={setVolume}
          onMuteToggle={() => setIsMuted(!isMuted)}
          isExpanded={isExpanded}
        />

        {/* Error display */}
        {error && (
          <motion.div
            className="error-message"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <span className="error-text">{error}</span>
            <button
              className="error-retry"
              onClick={() => {
                console.log('Retrying audio playback');
                if (audioRef.current) {
                  audioRef.current.load();
                  if (contextIsPlaying) {
                    audioRef.current.play().catch(console.error);
                  }
                }
              }}
            >
              Retry
            </button>
          </motion.div>
        )}

        {/* Audio element */}
        <audio
          ref={audioRef}
          preload="auto"
          onError={(e) => console.error('Audio element error:', e)}
          onCanPlay={() => console.log('Audio element can play')}
          onLoadedData={() => console.log('Audio element loaded data')}
        />
      </motion.div>

      {/* Expandable sections */}
      <AnimatePresence>
        {showQueue && (
          <PlaylistQueue
            playlist={currentPlaylist}
            currentIndex={currentPlaylistIndex}
            onClose={() => setShowQueue(false)}
            onSongSelect={(song) => {
              // TODO: Implement song selection from queue
            }}
          />
        )}
        
        {showEqualizer && (
          <Equalizer
            audioRef={audioRef}
            onClose={() => setShowEqualizer(false)}
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default ModernAudioPlayer;
