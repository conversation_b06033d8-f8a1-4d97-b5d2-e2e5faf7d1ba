import { useState } from 'react';
import { Table, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FaPlay, FaPause, FaHeart, FaRegHeart, FaEllipsisH, FaTimes } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useSong } from '../../context/SongContext';
import { usePlaylist } from '../../context/PlaylistContext';
import { useAuth } from '../../context/AuthContext';
import AddToPlaylistModal from '../playlists/AddToPlaylistModal';
import SongListShimmer from './SongListShimmer';
import { decodeHtmlEntities, formatDuration } from '../../utils/textUtils';

const SongList = ({ songs, showIndex = false, showRemoveOption = false, playlistId = null, isLoading = false }) => {
  const [favorites, setFavorites] = useState(() => {
    const storedFavorites = localStorage.getItem('favorites');
    return storedFavorites ? JSON.parse(storedFavorites) : [];
  });
  const [showAddToPlaylistModal, setShowAddToPlaylistModal] = useState(false);
  const [selectedSong, setSelectedSong] = useState(null);

  const {
    currentSong,
    isPlaying,
    playSong,
    pauseSong,
    playPlaylist,
    currentPlaylist,
    currentPlaylistIndex
  } = useSong();
  const { removeSongFromPlaylist, addToLikedSongs, removeFromLikedSongs } = usePlaylist();
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  const handlePlayPause = (song, index) => {
    console.log('handlePlayPause called for song:', song?.name, 'at index:', index);
    console.log('Current song:', currentSong?.name, 'isPlaying:', isPlaying);
    console.log('In playlist context:', !!playlistId, 'Playlist ID:', playlistId);

    if (currentSong && currentSong.id === song.id && isPlaying) {
      // If this song is currently playing, pause it
      console.log('Pausing currently playing song');
      pauseSong();
    } else {
      // If we're in a playlist view and have a playlist ID
      if (playlistId && songs) {
        console.log('Playing song in playlist context');

        // Enhance songs with required properties for playback
        const enhancedSongs = songs.map(s => {
          return {
            ...s,
            // If duration is missing or 0, set a default (4 minutes)
            duration: s.duration || 240,
            // Make sure downloadUrl is present
            downloadUrl: s.downloadUrl || []
          };
        });

        // Get the current playlist object with a valid ID
        const currentPlaylistObj = {
          id: playlistId || `playlist-${Date.now()}`,
          songs: enhancedSongs, // Use the enhanced songs array
          name: "Current Playlist" // This will be overridden if the actual playlist has a name
        };

        console.log('Created playlist object with', enhancedSongs.length, 'songs');

        // Play the playlist starting from this song's index
        console.log('Starting playlist from index:', index);
        playPlaylist(currentPlaylistObj, index);
      } else {
        // Just play the individual song if not in a playlist context
        console.log('Playing individual song (not in playlist)');

        // Ensure the song has all required properties
        const enhancedSong = {
          ...song,
          duration: song.duration || 240,
          downloadUrl: song.downloadUrl || []
        };

        playSong(enhancedSong);
      }
    }
  };

  const handleSongClick = (song) => {
    navigate(`/song/${song.id}`);
  };

  const handleToggleFavorite = async (song) => {
    const isFavorite = favorites.some(fav => fav.id === song.id);
    let updatedFavorites;

    if (isFavorite) {
      updatedFavorites = favorites.filter(fav => fav.id !== song.id);

      // Remove from Liked Songs playlist if user is logged in
      if (currentUser) {
        try {
          await removeFromLikedSongs(song.id);
        } catch (err) {
          console.error("Error removing from Liked Songs playlist:", err);
          // Continue with local favorites even if playlist update fails
        }
      }
    } else {
      updatedFavorites = [...favorites, song];

      // Add to Liked Songs playlist if user is logged in
      if (currentUser) {
        try {
          await addToLikedSongs(song);
        } catch (err) {
          console.error("Error adding to Liked Songs playlist:", err);
          // Continue with local favorites even if playlist update fails
        }
      }
    }

    // Update local storage regardless of playlist operations
    setFavorites(updatedFavorites);
    localStorage.setItem('favorites', JSON.stringify(updatedFavorites));
  };

  const handleAddToPlaylist = (song) => {
    // Make a deep copy of the song object to prevent null reference issues
    if (song) {
      // Create a complete copy of the song with all necessary properties
      const songCopy = {
        id: song.id,
        name: song.name || 'Untitled Song',
        image: song.image ? [...song.image] : [],
        artists: song.artists ? {
          primary: song.artists.primary ? [...song.artists.primary] : []
        } : { primary: [] },
        // Make sure to include duration and downloadUrl for playback
        duration: song.duration || 0,
        downloadUrl: song.downloadUrl ? [...song.downloadUrl] : [],
        // Include album info if available
        album: song.album ? { ...song.album } : null
      };
      console.log("Prepared song for playlist:", songCopy);
      setSelectedSong(songCopy);
    } else {
      setSelectedSong(null);
    }
    setShowAddToPlaylistModal(true);
  };

  const handleRemoveFromPlaylist = async (songId) => {
    if (!playlistId) return;

    try {
      console.log('Removing song with ID:', songId, 'from playlist:', playlistId);
      await removeSongFromPlaylist(playlistId, songId);
      // Show a success message or visual feedback
      // We could add a toast notification here
    } catch (err) {
      console.error('Failed to remove song from playlist:', err);
      // Show an error message
      alert('Failed to remove song from playlist. Please try again.');
    }
  };

  // Using the formatDuration utility function instead of defining it here

  // Show shimmer effect when loading
  if (isLoading) {
    return <SongListShimmer count={5} showIndex={showIndex} />;
  }

  // Show empty state if no songs
  if (!songs || songs.length === 0) {
    return (
      <div className="text-center py-5">
        <p className="mb-0">No songs found.</p>
      </div>
    );
  }

  return (
    <div className="song-list-container">
      <Table responsive hover variant="dark" className="song-table">
        <thead>
          <tr style={{ color: '#ffffff' }}>
            {showIndex && <th className="text-center" style={{ width: '50px', color: '#ffffff' }}>#</th>}
            <th style={{ color: '#ffffff' }}>Title</th>
            <th style={{ color: '#ffffff' }}>Artist</th>
            <th className="text-center" style={{ width: '80px', color: '#ffffff' }}>Duration</th>
            <th className="text-center" style={{ width: '120px', color: '#ffffff' }}>Actions</th>
          </tr>
        </thead>
        <tbody>
          {songs.map((song, index) => {
            const isCurrentSongPlaying = currentSong && currentSong.id === song.id && isPlaying;
            const isFavorite = favorites.some(fav => fav.id === song.id);

            return (
              <tr
                key={song.id}
                className={isCurrentSongPlaying ? 'table-active' : ''}
                style={{ cursor: 'pointer' }}
              >
                {showIndex && (
                  <td className="text-center align-middle">
                    {isCurrentSongPlaying ? (
                      <div className="playing-animation">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    ) : (
                      index + 1
                    )}
                  </td>
                )}
                <td
                  className="align-middle"
                  onClick={() => handleSongClick(song)}
                >
                  <div className="d-flex align-items-center">
                    <div
                      className="me-3"
                      style={{
                        width: '40px',
                        height: '40px',
                        flexShrink: 0,
                        borderRadius: '4px',
                        overflow: 'hidden'
                      }}
                    >
                      <img
                        src={
                          song.image && song.image.length > 0
                            ? (song.image.find(img => img.quality === '500x500')?.url || song.image[0].url)
                            : 'https://via.placeholder.com/40'
                        }
                        alt={song.name}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          borderRadius: '4px',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
                        }}
                      />
                    </div>
                    <div>
                      <div className="fw-medium" style={{ color: '#ffffff' }}>{decodeHtmlEntities(song.name)}</div>
                      {song.album && (
                        <small style={{ color: '#e0e0e0' }}>{decodeHtmlEntities(song.album.name)}</small>
                      )}
                    </div>
                  </div>
                </td>
                <td
                  className="align-middle"
                  onClick={() => handleSongClick(song)}
                  style={{ color: '#e0e0e0' }}
                >
                  {song.artists && song.artists.primary &&
                    song.artists.primary.map(artist => decodeHtmlEntities(artist.name)).join(', ')}
                </td>
                <td
                  className="text-center align-middle"
                  onClick={() => handleSongClick(song)}
                  style={{ color: '#e0e0e0' }}
                >
                  {formatDuration(song.duration)}
                </td>
                <td className="text-center align-middle">
                  <div className="d-flex justify-content-center gap-2">
                    <Button
                      variant={isCurrentSongPlaying ? 'danger' : 'primary'}
                      size="sm"
                      className="rounded-circle d-flex align-items-center justify-content-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlayPause(song, index);
                      }}
                      style={{
                        width: '32px',
                        height: '32px',
                        padding: 0,
                        backgroundColor: isCurrentSongPlaying ? '#FF6B6B' : 'var(--dark-accent)',
                        color: isCurrentSongPlaying ? '#fff' : '#2c3e50',
                        border: 'none'
                      }}
                    >
                      {isCurrentSongPlaying ? <FaPause size={12} /> : <FaPlay size={12} />}
                    </Button>

                    <Button
                      variant="link"
                      size="sm"
                      className="rounded-circle d-flex align-items-center justify-content-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleFavorite(song);
                      }}
                      style={{
                        width: '32px',
                        height: '32px',
                        padding: 0,
                        color: isFavorite ? '#FF6B6B' : 'var(--dark-text)'
                      }}
                    >
                      {isFavorite ? <FaHeart size={14} /> : <FaRegHeart size={14} />}
                    </Button>

                    {showRemoveOption ? (
                      <Button
                        variant="link"
                        size="sm"
                        className="rounded-circle d-flex align-items-center justify-content-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveFromPlaylist(song.id);
                        }}
                        style={{
                          width: '32px',
                          height: '32px',
                          padding: 0,
                          color: 'var(--dark-text)'
                        }}
                      >
                        <FaTimes size={14} />
                      </Button>
                    ) : (
                      <Button
                        variant="link"
                        size="sm"
                        className="rounded-circle d-flex align-items-center justify-content-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddToPlaylist(song);
                        }}
                        style={{
                          width: '32px',
                          height: '32px',
                          padding: 0,
                          color: 'var(--dark-text)'
                        }}
                      >
                        <FaEllipsisH size={14} />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </Table>

      <AddToPlaylistModal
        show={showAddToPlaylistModal}
        onHide={() => setShowAddToPlaylistModal(false)}
        song={selectedSong}
      />
    </div>
  );
};

export default SongList;
