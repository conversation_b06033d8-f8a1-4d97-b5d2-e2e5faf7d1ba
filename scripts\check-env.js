#!/usr/bin/env node

/**
 * This script checks if all required environment variables are present
 * before building the application for production.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define required environment variables
const requiredVars = [
  'VITE_API_URL',
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

// Initialize environment variables object
const envVars = {};

// Check if running on Vercel
const isVercel = process.env.VERCEL === '1';

// If running on Vercel, use process.env directly
if (isVercel) {
  console.log('Running on Vercel, using environment variables from Vercel...');

  // Copy environment variables from process.env
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      envVars[varName] = process.env[varName];
    }
  });
} else {
  // Check if .env.production file exists
  const envPath = path.resolve(process.cwd(), '.env.production');
  if (!fs.existsSync(envPath)) {
    console.error('\x1b[31m%s\x1b[0m', 'Error: .env.production file not found!');
    console.log('Please create a .env.production file with the required environment variables.');
    process.exit(1);
  }

  // Read .env.production file
  const envContent = fs.readFileSync(envPath, 'utf8');

  // Parse environment variables from file
  envContent.split('\n').forEach(line => {
    // Skip comments and empty lines
    if (line.startsWith('#') || !line.trim()) return;

    const match = line.match(/^(VITE_[A-Z0-9_]+)=(.*)$/);
    if (match) {
      const [, key, value] = match;
      envVars[key] = value;
    }
  });
}

// No need for additional parsing as we've already handled it above

// Check for missing variables
const missingVars = requiredVars.filter(varName => !envVars[varName]);

if (missingVars.length > 0) {
  console.error('\x1b[31m%s\x1b[0m', 'Error: Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`  - ${varName}`);
  });
  console.log('\nPlease add these variables to your .env.production file.');
  process.exit(1);
}

// Check for empty variables
const emptyVars = requiredVars.filter(varName => envVars[varName] === '');

if (emptyVars.length > 0) {
  console.error('\x1b[33m%s\x1b[0m', 'Warning: The following environment variables are empty:');
  emptyVars.forEach(varName => {
    console.error(`  - ${varName}`);
  });
  console.log('\nPlease make sure these variables have proper values.');
}

// Success message
console.log('\x1b[32m%s\x1b[0m', 'Environment variables check passed!');
console.log('All required environment variables are present in .env.production');
