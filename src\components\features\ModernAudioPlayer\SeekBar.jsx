import React, { useRef, useState, useCallback } from 'react';
import { motion } from 'framer-motion';

const SeekBar = ({
  currentTime,
  duration,
  buffered,
  isDragging,
  dragProgress,
  onSeek,
  formatTime,
  isExpanded
}) => {
  const seekbarRef = useRef(null);
  const [isHovering, setIsHovering] = useState(false);
  const [hoverTime, setHoverTime] = useState(0);
  const [hoverPosition, setHoverPosition] = useState(0);

  // Calculate progress percentage
  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;
  const displayProgress = isDragging && dragProgress !== null ? dragProgress * 100 : progress;

  // Handle mouse/touch events
  const handleMouseDown = useCallback((event) => {
    if (!seekbarRef.current || !duration) return;

    event.preventDefault();
    event.stopPropagation();

    const rect = seekbarRef.current.getBoundingClientRect();
    const clientX = event.type.includes('touch') ? event.touches[0].clientX : event.clientX;
    const clickProgress = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    const newTime = duration * clickProgress;

    console.log('Seeking to:', newTime, 'seconds (', Math.round(clickProgress * 100), '%)');
    onSeek(newTime);
  }, [duration, onSeek]);

  const handleMouseMove = useCallback((event) => {
    if (!seekbarRef.current || !duration) return;

    const rect = seekbarRef.current.getBoundingClientRect();
    const clientX = event.type.includes('touch') ? event.touches[0].clientX : event.clientX;
    const hoverProgress = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    const time = duration * hoverProgress;
    
    setHoverTime(time);
    setHoverPosition(clientX - rect.left);
  }, [duration]);

  const handleMouseEnter = useCallback(() => {
    setIsHovering(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false);
  }, []);

  // Prevent context menu on right click
  const handleContextMenu = useCallback((event) => {
    event.preventDefault();
  }, []);

  return (
    <div className={`seekbar-section ${isExpanded ? 'expanded' : 'collapsed'}`}>
      {/* Time display */}
      <div className="time-display">
        <span className="current-time">
          {formatTime(isDragging && dragProgress !== null ? duration * dragProgress : currentTime)}
        </span>
        {isExpanded && (
          <span className="duration-time">
            {formatTime(duration)}
          </span>
        )}
      </div>

      {/* Seekbar container */}
      <div 
        className={`seekbar-container ${isDragging ? 'dragging' : ''} ${isHovering ? 'hovering' : ''}`}
        ref={seekbarRef}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleMouseDown}
        onContextMenu={handleContextMenu}
        role="slider"
        aria-label="Seek slider"
        aria-valuemin={0}
        aria-valuemax={duration}
        aria-valuenow={currentTime}
        tabIndex={0}
      >
        {/* Background track */}
        <div className="seekbar-track">
          {/* Buffered progress */}
          <div 
            className="seekbar-buffered"
            style={{ width: `${buffered}%` }}
          />
          
          {/* Progress bar */}
          <motion.div 
            className="seekbar-progress"
            style={{ width: `${displayProgress}%` }}
            transition={{ duration: isDragging ? 0 : 0.1 }}
          />
          
          {/* Thumb */}
          <motion.div 
            className="seekbar-thumb"
            style={{ left: `${displayProgress}%` }}
            animate={{
              scale: isDragging || isHovering ? 1.2 : 1,
              opacity: isDragging || isHovering ? 1 : 0.8
            }}
            transition={{ duration: 0.2 }}
          />
          
          {/* Hover preview */}
          {isHovering && !isDragging && isExpanded && (
            <motion.div 
              className="seekbar-preview"
              style={{ left: `${hoverPosition}px` }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
            >
              <div className="preview-tooltip">
                {formatTime(hoverTime)}
              </div>
            </motion.div>
          )}
        </div>

        {/* Chapter markers (if available) */}
        {isExpanded && (
          <div className="chapter-markers">
            {/* TODO: Add chapter markers if song has chapters */}
          </div>
        )}
      </div>

      {/* Duration display for collapsed mode */}
      {!isExpanded && (
        <span className="duration-time-compact">
          {formatTime(duration)}
        </span>
      )}
    </div>
  );
};

export default SeekBar;
