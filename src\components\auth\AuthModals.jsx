import { useState, useEffect } from 'react';
import LoginModal from './LoginModal';
import SignupModal from './SignupModal';
import ResetPasswordModal from './ResetPasswordModal';

const AuthModals = ({ showLogin, showSignup, showReset, onHide }) => {
  const [activeModal, setActiveModal] = useState(
    showLogin ? 'login' : showSignup ? 'signup' : showReset ? 'reset' : null
  );

  // Update active modal when props change
  useEffect(() => {
    if (showLogin) setActiveModal('login');
    else if (showSignup) setActiveModal('signup');
    else if (showReset) setActiveModal('reset');
    else setActiveModal(null);
  }, [showLogin, showSignup, showReset]);

  const handleHide = () => {
    setActiveModal(null);
    onHide();
  };

  const switchToLogin = () => {
    setActiveModal('login');
  };

  const switchToSignup = () => {
    setActiveModal('signup');
  };

  const switchToReset = () => {
    setActiveModal('reset');
  };

  return (
    <>
      <LoginModal
        show={activeModal === 'login'}
        onHide={handleHide}
        switchToSignup={switchToSignup}
        switchToReset={switchToReset}
      />

      <SignupModal
        show={activeModal === 'signup'}
        onHide={handleHide}
        switchToLogin={switchToLogin}
      />

      <ResetPasswordModal
        show={activeModal === 'reset'}
        onHide={handleHide}
        switchToLogin={switchToLogin}
      />
    </>
  );
};

export default AuthModals;
