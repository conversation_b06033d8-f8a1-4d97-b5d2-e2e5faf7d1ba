import { useEffect } from 'react';
import { useSong } from '../../context/SongContext';
<<<<<<< HEAD
import NewAudioPlayer from './NewAudioPlayer';
=======
import ModernAudioPlayer from './ModernAudioPlayer/ModernAudioPlayer';
>>>>>>> dd01f73 (hii general commit)

const GlobalAudioPlayer = () => {
  const {
    currentSong,
    isPlaying,
    showModal,
    playerVisible,
    playSong,
    pauseSong,
    resumeSong,
    closeModal,
    stopSong,
    hidePlayer,
    getNextSong,
    getPreviousSong,
    currentPlaylist,
    currentPlaylistIndex
  } = useSong();

  // Handle play/pause
  const handlePlay = (song) => {
    if (song) {
      playSong(song);
    } else if (currentSong) {
      resumeSong();
    }
  };

  const handlePause = () => {
    pauseSong();
  };

  // Handle next/previous
  const handleNext = async () => {
    try {
      const nextSong = await getNextSong();
      if (nextSong) {
        playSong(nextSong);
      }
    } catch (error) {
      console.error('Error getting next song:', error);
    }
  };

  const handlePrevious = () => {
    const prevSong = getPreviousSong();
    if (prevSong) {
      playSong(prevSong);
    }
  };

  // Handle close player
  const handleClosePlayer = () => {
    // Hide the player and pause the song
    hidePlayer();
    console.log('Player hidden and song paused');
  };

  // Extra check to ensure player is not shown by default
  useEffect(() => {
    console.log('GlobalAudioPlayer mounted, playerVisible:', playerVisible);
  }, [playerVisible]);

  // Don't render anything if there's no current song or player is not visible
  if (!currentSong || !playerVisible) {
    console.log('Not rendering player: currentSong=', !!currentSong, 'playerVisible=', playerVisible);
    if (currentSong) {
      console.log('Current song exists but player is not visible:', currentSong.name);
    }
    return null;
  }

  console.log('Rendering global audio player for song:', currentSong.name);
  console.log('Current playlist:', currentPlaylist?.id, 'Current index:', currentPlaylistIndex);

  return (
    <>
      {/* Show the fixed audio player at the bottom if playerVisible is true */}
      <div className="fixed-bottom p-3">
        <div className="container">
<<<<<<< HEAD
          <NewAudioPlayer
=======
          <ModernAudioPlayer
>>>>>>> dd01f73 (hii general commit)
            song={currentSong}
            onNext={handleNext}
            onPrevious={handlePrevious}
            onClose={handleClosePlayer}
          />
        </div>
      </div>
    </>
  );
};

export default GlobalAudioPlayer;
