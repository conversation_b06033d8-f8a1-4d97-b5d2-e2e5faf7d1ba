/* Mobile-specific styles for Musafir Music */

/* Mobile navigation bar styles */
.mobile-nav-bar {
  height: 60px;
  transition: transform 0.3s ease;
}

.mobile-nav-link {
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  padding: 0.5rem;
}

.mobile-nav-link:active {
  transform: scale(0.95);
  background-color: rgba(157, 78, 221, 0.1);
}

.mobile-nav-link.active {
  background-color: rgba(157, 78, 221, 0.15);
}

.mobile-nav-text {
  font-size: 0.7rem;
  font-weight: 500;
}

/* Hide mobile nav when player is visible */
@media (max-width: 767.98px) {
  .fixed-bottom.mobile-nav-bar {
    z-index: 1030;
  }

  .fixed-bottom:not(.mobile-nav-bar) {
    z-index: 1031;
  }
}

/* Adjust main container padding for mobile */
@media (max-width: 767.98px) {
  .container {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  main.container {
    padding-bottom: 5rem !important; /* Extra padding for mobile nav */
  }

  /* Adjust spacing when player is visible */
  main.container.mb-5 {
    padding-bottom: 8rem !important;
  }
}

/* Improve touch targets for mobile */
@media (max-width: 767.98px) {
  /* Make buttons more tappable */
  .btn {
    min-height: 38px;
    min-width: 38px;
  }

  /* Increase form control height */
  .form-control {
    min-height: 42px;
  }

  /* Improve card touch areas */
  .card-body {
    padding: 0.75rem !important;
  }

  /* Adjust spacing */
  .py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-5 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  /* Improve navbar spacing */
  .navbar {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  /* Optimize navbar collapse */
  .navbar-collapse {
    background-color: rgba(15, 15, 15, 0.98);
    border-radius: 1rem;
    padding: 1rem;
    margin-top: 0.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  }

  .navbar-nav .nav-item {
    margin-bottom: 0.5rem;
  }

  /* Make navbar links full width on mobile */
  .navbar-nav .nav-link {
    display: block;
    width: 100%;
    text-align: center;
    padding: 0.75rem !important;
  }

  /* Optimize song cards for mobile */
  .song-card {
    transition: transform 0.2s ease !important;
  }

  .song-card:active {
    transform: scale(0.98) !important;
  }

  /* Improve touch feedback */
  .suggestion-item:active {
    background-color: rgba(157, 78, 221, 0.15) !important;
  }
}

/* Optimize for small phones */
@media (max-width: 575.98px) {
  /* Further reduce spacing */
  .container {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  /* Adjust headings */
  h1, .h1 {
    font-size: 1.5rem !important;
  }

  h2, .h2 {
    font-size: 1.25rem !important;
  }

  /* Optimize footer for mobile */
  footer .row > div {
    margin-bottom: 1.5rem !important;
  }

  footer h5 {
    margin-bottom: 0.75rem !important;
  }
}

/* Add swipe indicators for the player */
.audio-player::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    rgba(0, 255, 209, 0.5) 0%,
    rgba(0, 255, 209, 0) 20%,
    rgba(0, 255, 209, 0) 80%,
    rgba(0, 255, 209, 0.5) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.audio-player:active::after {
  opacity: 1;
}

/* Optimize modals for mobile */
@media (max-width: 767.98px) {
  .modal-dialog {
    margin: 0.5rem auto !important;
    max-width: calc(100% - 1rem) !important;
  }

  .modal-content {
    border-radius: 1rem !important;
  }

  .modal-body {
    padding: 1rem !important;
  }

  .modal-header, .modal-footer {
    padding: 0.75rem 1rem !important;
  }
}

/* Improve scrolling experience */
* {
  -webkit-overflow-scrolling: touch;
}

/* Add active state feedback for touch */
.btn:active, .card:active, .nav-link:active {
  transform: scale(0.98);
}

/* Profile page mobile optimizations */
@media (max-width: 767.98px) {
  .profile-header h1 {
    font-size: 1.75rem !important;
  }

  .user-avatar {
    width: 80px !important;
    height: 80px !important;
    font-size: 2rem !important;
  }

  .profile-card {
    margin-bottom: 1rem !important;
  }

  .profile-stats .list-group-item {
    padding: 0.75rem 0.5rem !important;
  }
}

/* Playlist page mobile optimizations */
@media (max-width: 767.98px) {
  /* Playlist header */
  .playlist-header {
    padding: 1.5rem 1rem !important;
  }

  .playlist-header h2 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .playlist-header p {
    font-size: 0.85rem !important;
  }

  .playlist-header .btn {
    font-size: 0.85rem !important;
    padding: 0.4rem 1.2rem !important;
  }

  /* Playlist search and filter */
  .playlist-search {
    margin-bottom: 1rem !important;
  }

  /* Playlist cards */
  .playlist-card {
    margin-bottom: 0.75rem !important;
  }

  .playlist-card .card-img-overlay {
    padding: 0.5rem !important;
  }

  .playlist-card .dropdown {
    top: 0.25rem !important;
    right: 0.25rem !important;
  }

  .playlist-card .play-button {
    width: 36px !important;
    height: 36px !important;
    font-size: 0.9rem !important;
  }

  /* Playlist detail page */
  .playlist-detail-header {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    padding: 1.25rem 1rem !important;
  }

  .playlist-detail-header .playlist-image {
    width: 150px !important;
    height: 150px !important;
    margin-bottom: 1rem !important;
    margin-right: 0 !important;
  }

  .playlist-detail-header .playlist-info {
    width: 100% !important;
  }

  .playlist-detail-header .playlist-actions {
    margin-top: 1rem !important;
    justify-content: center !important;
  }

  /* Empty playlist state */
  .empty-playlist {
    padding: 2rem 1rem !important;
  }

  .empty-playlist .icon-container {
    width: 60px !important;
    height: 60px !important;
    margin-bottom: 1rem !important;
  }
}

/* Search page mobile optimizations */
@media (max-width: 767.98px) {
  /* Search header */
  .search-header {
    padding: 1.25rem 1rem !important;
    margin-bottom: 1rem !important;
  }

  .search-header h4 {
    font-size: 1.25rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* Search tabs */
  .search-tabs {
    overflow-x: auto !important;
    flex-wrap: nowrap !important;
    margin-bottom: 1rem !important;
    padding-bottom: 0.25rem !important;
  }

  .search-tabs .nav-item {
    flex: 0 0 auto !important;
  }

  .search-tabs .nav-link {
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
    white-space: nowrap !important;
  }

  /* Search results */
  .search-results {
    padding: 0 !important;
  }

  /* Album and artist cards */
  .album-grid .col-6 {
    padding: 0.25rem !important;
  }

  .album-card, .artist-card {
    margin-bottom: 0.5rem !important;
  }

  .album-card .card-body, .artist-card .card-body {
    padding: 0.75rem !important;
  }

  .album-card .card-title, .artist-card .card-title {
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
  }

  .album-card .card-text, .artist-card .card-text {
    font-size: 0.75rem !important;
  }

  /* Empty search state */
  .empty-search {
    padding: 2rem 1rem !important;
  }

  .empty-search .icon-container {
    margin-bottom: 1rem !important;
  }
}

/* Mobile player optimizations */
@media (max-width: 767.98px) {
  /* Enhanced player styling */
  .audio-player {
    border-radius: 1rem 1rem 0 0 !important;
    margin-bottom: 0 !important;
    border: none !important;
  }

  /* Progress bar enhancements */
  .custom-progress-bar {
    height: 6px !important;
    margin: 0.75rem 0;
    transition: height 0.2s ease;
  }

  .custom-progress-bar:active {
    height: 8px !important;
  }

  .progress-fill {
    transition: width 0.1s linear;
  }

  .progress-thumb {
    width: 20px !important;
    height: 20px !important;
    transform-origin: center;
    transition: transform 0.2s ease, opacity 0.2s ease;
    box-shadow: 0 0 10px rgba(0, 255, 209, 0.6) !important;
  }

  .progress-thumb:active {
    transform: translate(-50%, -50%) scale(1.3) !important;
    opacity: 1 !important;
  }

  /* Make the progress bar more visible when being interacted with */
  .progress-container:active .custom-progress-bar,
  .progress-container:focus .custom-progress-bar,
  .progress-container:hover .custom-progress-bar {
    height: 8px !important;
  }

  /* Always show thumb when touching the progress container on mobile */
  .progress-container:active .progress-thumb,
  .progress-container:focus .progress-thumb,
  .progress-container:hover .progress-thumb {
    opacity: 1 !important;
  }

  /* Time preview tooltip */
  .time-preview-tooltip {
    font-size: 10px !important;
    padding: 2px 6px !important;
    bottom: 20px !important;
    animation: fadeIn 0.2s ease;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px) translateX(-50%); }
    to { opacity: 1; transform: translateY(0) translateX(-50%); }
  }

  /* Mobile player controls */
  .mobile-player-controls {
    margin-top: 0.75rem !important;
    padding-bottom: 0.25rem !important;
  }

  .btn-play-circle {
    transform-origin: center;
    transition: transform 0.2s ease;
  }

  .btn-play-circle:active {
    transform: scale(0.95);
  }

  .btn-icon-circle {
    transform-origin: center;
    transition: transform 0.2s ease, background-color 0.2s ease;
  }

  .btn-icon-circle:active {
    transform: scale(0.9);
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* Swipe hint animation */
  .audio-player::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      rgba(0, 255, 209, 0) 0%,
      rgba(0, 255, 209, 0.3) 20%,
      rgba(0, 255, 209, 0.3) 80%,
      rgba(0, 255, 209, 0) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: swipeHint 2s ease-in-out infinite;
  }

  @keyframes swipeHint {
    0% {
      opacity: 0;
      transform: translateX(-10%);
    }
    50% {
      opacity: 1;
      transform: translateX(10%);
    }
    100% {
      opacity: 0;
      transform: translateX(-10%);
    }
  }

  /* Add extra bottom padding when player is visible */
  .container.mb-5 {
    margin-bottom: 7rem !important;
  }
}
