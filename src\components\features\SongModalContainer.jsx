import { useSong } from '../../context/SongContext';
import NewAudioPlayer from './NewAudioPlayer';

const SongModalContainer = () => {
  const {
    currentSong,
    isPlaying,
    showModal,
    playSong,
    pauseSong,
    resumeSong,
    closeModal,
    getNextSong,
    getPreviousSong
  } = useSong();

  // Handle play/pause
  const handlePlay = (song) => {
    if (song) {
      playSong(song);
    } else if (currentSong) {
      resumeSong();
    }
  };

  const handlePause = () => {
    pauseSong();
  };

  // Handle next/previous
  const handleNext = () => {
    const nextSong = getNextSong();
    if (nextSong) {
      console.log('Playing next song from handleNext:', nextSong.name);
      // Pass true to indicate we should navigate to the song page
      playSong(nextSong, true);
    }
  };

  const handlePrevious = () => {
    const prevSong = getPreviousSong();
    if (prevSong) {
      console.log('Playing previous song from handlePrevious:', prevSong.name);
      // Pass true to indicate we should navigate to the song page
      playSong(prevSong, true);
    }
  };

  if (!currentSong) return null;

  return (
    <>
      {/* Always show the fixed audio player at the bottom */}
      <div className="fixed-bottom p-3">
        <div className="container">
          <NewAudioPlayer
            song={currentSong}
            onNext={handleNext}
            onPrevious={handlePrevious}
          />
        </div>
      </div>
    </>
  );
};

export default SongModalContainer;
