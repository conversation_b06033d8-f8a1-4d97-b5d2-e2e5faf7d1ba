import React from 'react';
import { motion } from 'framer-motion';
import { 
  Fa<PERSON>lay, 
  FaPause, 
  FaStep<PERSON>or<PERSON>, 
  FaStepBackward, 
  FaRandom, 
  FaRedoAlt,
  FaSpinner
} from 'react-icons/fa';

const PlayerControls = ({
  isPlaying,
  isLoading,
  repeatMode,
  shuffleMode,
  onTogglePlay,
  onNext,
  onPrevious,
  onRepeatMode,
  onShuffleToggle,
  isExpanded
}) => {
  const getRepeatIcon = () => {
    switch (repeatMode) {
      case 'one':
        return <><FaRedoAlt /><span className="repeat-indicator">1</span></>;
      case 'all':
        return <FaRedoAlt />;
      default:
        return <FaRedoAlt />;
    }
  };

  const buttonVariants = {
    tap: { scale: 0.95 },
    hover: { scale: 1.05 }
  };

  const playButtonVariants = {
    tap: { scale: 0.9 },
    hover: { scale: 1.1 }
  };

  return (
    <div className={`player-controls ${isExpanded ? 'expanded' : 'collapsed'}`}>
      {/* Secondary controls (visible in expanded mode) */}
      {isExpanded && (
        <div className="secondary-controls">
          <motion.button
            className={`control-btn shuffle-btn ${shuffleMode ? 'active' : ''}`}
            onClick={onShuffleToggle}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            aria-label={shuffleMode ? "Disable shuffle" : "Enable shuffle"}
          >
            <FaRandom />
          </motion.button>
        </div>
      )}

      {/* Primary controls */}
      <div className="primary-controls">
        <motion.button
          className="control-btn previous-btn"
          onClick={onPrevious}
          variants={buttonVariants}
          whileTap="tap"
          whileHover="hover"
          aria-label="Previous track"
        >
          <FaStepBackward />
        </motion.button>

        <motion.button
          className="control-btn play-pause-btn"
          onClick={onTogglePlay}
          variants={playButtonVariants}
          whileTap="tap"
          whileHover="hover"
          disabled={isLoading}
          aria-label={isPlaying ? "Pause" : "Play"}
        >
          {isLoading ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <FaSpinner />
            </motion.div>
          ) : isPlaying ? (
            <FaPause />
          ) : (
            <FaPlay />
          )}
        </motion.button>

        <motion.button
          className="control-btn next-btn"
          onClick={onNext}
          variants={buttonVariants}
          whileTap="tap"
          whileHover="hover"
          aria-label="Next track"
        >
          <FaStepForward />
        </motion.button>
      </div>

      {/* Secondary controls (visible in expanded mode) */}
      {isExpanded && (
        <div className="secondary-controls">
          <motion.button
            className={`control-btn repeat-btn ${repeatMode !== 'none' ? 'active' : ''}`}
            onClick={onRepeatMode}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            aria-label={`Repeat mode: ${repeatMode}`}
          >
            {getRepeatIcon()}
          </motion.button>
        </div>
      )}

      {/* Compact controls for collapsed mode */}
      {!isExpanded && (
        <div className="compact-controls">
          <motion.button
            className={`control-btn-small shuffle-btn ${shuffleMode ? 'active' : ''}`}
            onClick={onShuffleToggle}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            aria-label={shuffleMode ? "Disable shuffle" : "Enable shuffle"}
          >
            <FaRandom />
          </motion.button>

          <motion.button
            className={`control-btn-small repeat-btn ${repeatMode !== 'none' ? 'active' : ''}`}
            onClick={onRepeatMode}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            aria-label={`Repeat mode: ${repeatMode}`}
          >
            {getRepeatIcon()}
          </motion.button>
        </div>
      )}
    </div>
  );
};

export default PlayerControls;
