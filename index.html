<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- No client-side redirect logic needed with proper server configuration -->

    <!-- Viewport and theme -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <meta name="theme-color" content="#0f0f0f" />

    <!-- Primary Meta Tags -->
    <title>MusicMania - Modern Music Player</title>
    <meta name="title" content="MusicMania - Modern Music Player" />
    <meta name="description" content="A responsive music player web app with a modern UI/UX, dark/light theme toggle, and integration with the saavn.dev API." />
    <meta name="keywords" content="music player, web app, react, bootstrap, firebase, responsive design" />
    <meta name="author" content="Abhinav" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://musicmania.vercel.app/" />
    <meta property="og:title" content="MusicMania - Modern Music Player" />
    <meta property="og:description" content="A responsive music player web app with a modern UI/UX, dark/light theme toggle, and integration with the saavn.dev API." />
    <meta property="og:image" content="https://musicmania.vercel.app/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://musicmania.vercel.app/" />
    <meta property="twitter:title" content="MusicMania - Modern Music Player" />
    <meta property="twitter:description" content="A responsive music player web app with a modern UI/UX, dark/light theme toggle, and integration with the saavn.dev API." />
    <meta property="twitter:image" content="https://musicmania.vercel.app/og-image.png" />

    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
