{"name": "musafirmusic", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "prebuild": "node --experimental-modules scripts/check-env.js", "build": "vite build --mode production && node scripts/generate-version.js", "build:analyze": "vite build --mode production --debug", "lint": "eslint .", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist", "check-env": "node --experimental-modules scripts/check-env.js", "generate-version": "node scripts/generate-version.js"}, "dependencies": {"@tanstack/react-query": "^5.76.1", "axios": "^1.9.0", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.13.1", "firebase": "^11.7.3", "framer-motion": "^12.12.1", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^6.22.3", "sass": "^1.89.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rimraf": "^5.0.5", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1"}}