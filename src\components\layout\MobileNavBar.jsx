import React from 'react';
import { NavLink } from 'react-router-dom';
import { <PERSON>aH<PERSON>, FaS<PERSON>ch, FaList, FaUser } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useAuth } from '../../context/AuthContext';

const MobileNavBar = () => {
  const { currentUser } = useAuth();

  return (
    <motion.div 
      className="d-block d-lg-none fixed-bottom mobile-nav-bar"
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
      style={{
        backgroundColor: 'rgba(15, 15, 15, 0.95)',
        backdropFilter: 'blur(10px)',
        borderTop: '1px solid rgba(255, 255, 255, 0.05)',
        boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.2)',
        zIndex: 1030, // Below fixed player but above other content
        padding: '0.5rem 0',
      }}
    >
      <div className="container-fluid px-2">
        <div className="row g-0">
          <div className="col-3 text-center">
            <NavLink 
              to="/" 
              className={({ isActive }) => 
                `mobile-nav-link d-flex flex-column align-items-center justify-content-center py-1 ${isActive ? 'active' : ''}`
              }
              style={({ isActive }) => ({
                color: isActive ? 'var(--dark-accent)' : 'var(--dark-text)',
              })}
            >
              <FaHome size={20} />
              <span className="mobile-nav-text mt-1">Home</span>
            </NavLink>
          </div>
          <div className="col-3 text-center">
            <NavLink 
              to="/search" 
              className={({ isActive }) => 
                `mobile-nav-link d-flex flex-column align-items-center justify-content-center py-1 ${isActive ? 'active' : ''}`
              }
              style={({ isActive }) => ({
                color: isActive ? 'var(--dark-accent)' : 'var(--dark-text)',
              })}
            >
              <FaSearch size={20} />
              <span className="mobile-nav-text mt-1">Search</span>
            </NavLink>
          </div>
          <div className="col-3 text-center">
            <NavLink 
              to="/playlists" 
              className={({ isActive }) => 
                `mobile-nav-link d-flex flex-column align-items-center justify-content-center py-1 ${isActive ? 'active' : ''}`
              }
              style={({ isActive }) => ({
                color: isActive ? 'var(--dark-accent)' : 'var(--dark-text)',
              })}
            >
              <FaList size={20} />
              <span className="mobile-nav-text mt-1">Playlists</span>
            </NavLink>
          </div>
          <div className="col-3 text-center">
            <NavLink 
              to={currentUser ? "/profile" : "/playlists"} 
              className={({ isActive }) => 
                `mobile-nav-link d-flex flex-column align-items-center justify-content-center py-1 ${isActive ? 'active' : ''}`
              }
              style={({ isActive }) => ({
                color: isActive ? 'var(--dark-accent)' : 'var(--dark-text)',
              })}
            >
              <FaUser size={20} />
              <span className="mobile-nav-text mt-1">
                {currentUser ? 'Profile' : 'Account'}
              </span>
            </NavLink>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default MobileNavBar;
