import { useState, useEffect, useRef } from 'react';
import { FaP<PERSON>, FaPause, FaStepForward, FaStepBackward, FaVolumeUp, FaVolumeMute, FaTimes } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';
import { useSong } from '../../context/SongContext';
import './NewAudioPlayer.css';

const NewAudioPlayer = ({ song, onNext, onPrevious, onClose }) => {
  // Context and state
  const { isPlaying: contextIsPlaying, pauseSong, resumeSong } = useSong();
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [wasPlaying, setWasPlaying] = useState(false);
  const [updateTimer, setUpdateTimer] = useState(null);

  // Refs
  const audioRef = useRef(null);
  const seekbarRef = useRef(null);
  const progressRef = useRef(null);
  const thumbRef = useRef(null);
  const { darkMode } = useTheme();

  // Use the playing state from context
  const isPlaying = contextIsPlaying;

  // Get audio URL from song
  const getAudioUrl = () => {
    console.log('getAudioUrl called for song:', song?.name);

    if (!song) {
      console.error('Song is null or undefined');
      return '';
    }

    // Check if we need to fetch the song details
    if (!song.downloadUrl || song.downloadUrl.length === 0) {
      console.warn('No downloadUrl available for song:', song.name);

      // Use a fallback URL for testing - this is a silent audio file
      // In a production app, you would want to fetch the song details from the API
      console.log('Using fallback audio URL for testing');
      return 'https://cdn.jsdelivr.net/gh/anars/blank-audio/1-second-of-silence.mp3';
    }

    console.log('Available download URLs:', song.downloadUrl);

    // Find high quality audio
    const highQualityAudio = song.downloadUrl.find(audio => audio.quality === '320kbps');
    if (highQualityAudio && highQualityAudio.url) {
      console.log('Using 320kbps audio URL');
      return highQualityAudio.url;
    }

    // If 320kbps not found or url is empty, try 160kbps
    const mediumQualityAudio = song.downloadUrl.find(audio => audio.quality === '160kbps');
    if (mediumQualityAudio && mediumQualityAudio.url) {
      console.log('Using 160kbps audio URL');
      return mediumQualityAudio.url;
    }

    // If no specific quality found, use the first available URL
    for (const audio of song.downloadUrl) {
      if (audio.url) {
        console.log('Using first available audio URL');
        return audio.url;
      }
    }

    console.warn('No valid audio URL found in downloadUrl array, using fallback');
    return 'https://cdn.jsdelivr.net/gh/anars/blank-audio/1-second-of-silence.mp3';
  };

  // Function to start the progress timer for smooth updates
  const startProgressTimer = () => {
    // Clear any existing timer
    if (updateTimer) {
      clearInterval(updateTimer);
    }

    // Set up a timer that updates the progress every 50ms
    // This ensures smooth updates even if timeupdate events are delayed
    const timer = setInterval(() => {
      if (audioRef.current && !isDragging && !audioRef.current.paused) {
        updateProgress();
      }
    }, 50);

    setUpdateTimer(timer);
  };

  // Function to update progress bar and time display
  const updateProgress = () => {
    if (!audioRef.current || !duration) return;

    const newTime = audioRef.current.currentTime;
    const percent = (newTime / duration) * 100;

    // Update time display
    setCurrentTime(newTime);

    // Update progress bar and thumb position if not dragging
    if (!isDragging && progressRef.current && thumbRef.current) {
      progressRef.current.style.width = `${percent}%`;
      thumbRef.current.style.left = `${percent}%`;
    }
  };

  // Effect for handling song changes
  useEffect(() => {
    console.log('Song changed in NewAudioPlayer:', song?.name);

    // Reset player when song changes
    if (audioRef.current && song) {
      // Pause current audio
      audioRef.current.pause();
      setCurrentTime(0);

      // Get the audio URL
      const audioUrl = getAudioUrl();

      if (audioUrl) {
        console.log('Loading new audio URL:', audioUrl);

        // Load new audio
        audioRef.current.src = audioUrl;
        audioRef.current.load();

        // Auto play when song changes
        console.log('Attempting to auto-play new song');
        const playPromise = audioRef.current.play();

        if (playPromise !== undefined) {
          playPromise.then(() => {
            console.log('Successfully playing audio:', audioUrl);
            startProgressTimer();
          }).catch(error => {
            console.error('Auto play failed:', error);
            // Try again after a short delay
            setTimeout(() => {
              console.log('Retrying playback after delay');
              audioRef.current.play()
                .then(() => {
                  console.log('Retry successful');
                  startProgressTimer();
                })
                .catch(retryError => {
                  console.error('Retry also failed:', retryError);
                });
            }, 300);
          });
        }
      } else {
        console.error('No valid audio URL found for song:', song);
      }
    } else {
      console.log('Audio ref or song not available:', !!audioRef.current, !!song);
    }

    // Clean up timer when component unmounts or song changes
    return () => {
      if (updateTimer) {
        clearInterval(updateTimer);
        setUpdateTimer(null);
      }
    };
  }, [song]);

  // Effect for syncing with isPlaying state from context
  useEffect(() => {
    if (audioRef.current) {
      if (isPlaying) {
        const playPromise = audioRef.current.play();
        if (playPromise !== undefined) {
          playPromise.then(() => {
            startProgressTimer();
          }).catch(error => {
            console.error('Play failed in sync effect:', error);
          });
        }
      } else {
        audioRef.current.pause();
        if (updateTimer) {
          clearInterval(updateTimer);
          setUpdateTimer(null);
        }
      }
    }
  }, [isPlaying]);

  // Effect for volume changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
    }
  }, [volume, isMuted]);

  // Toggle play/pause
  const togglePlay = () => {
    console.log('togglePlay called, isPlaying:', isPlaying);

    if (isPlaying) {
      console.log('Pausing audio');
      audioRef.current.pause();
      pauseSong();
      if (updateTimer) {
        clearInterval(updateTimer);
        setUpdateTimer(null);
      }
    } else {
      console.log('Playing audio');
      // Force a reload of the audio element to ensure it's in a good state
      const currentTime = audioRef.current.currentTime;
      const audioUrl = getAudioUrl();

      audioRef.current.src = audioUrl;
      audioRef.current.load();
      audioRef.current.currentTime = currentTime;

      const playPromise = audioRef.current.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          console.log('Play succeeded');
          resumeSong();
          startProgressTimer();
        }).catch(error => {
          console.error('Play failed:', error);
          // Try again after a short delay
          setTimeout(() => {
            console.log('Retrying play after error');
            audioRef.current.play()
              .then(() => {
                console.log('Retry play succeeded');
                resumeSong();
                startProgressTimer();
              })
              .catch(retryError => {
                console.error('Retry play also failed:', retryError);
              });
          }, 300);
        });
      }
    }
  };

  // Toggle mute
  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  // Time update handler
  const handleTimeUpdate = () => {
    if (!isDragging) {
      updateProgress();
    }
  };

  // Handle metadata loaded
  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
      updateProgress();
    }
  };

  // Calculate position from mouse/touch event
  const getPositionFromEvent = (e) => {
    if (!seekbarRef.current) return 0;

    const rect = seekbarRef.current.getBoundingClientRect();
    const clientX = e.clientX || (e.touches && e.touches[0].clientX);
    const position = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    return position;
  };

  // Update seekbar based on position
  const updateSeekPosition = (position) => {
    if (!progressRef.current || !thumbRef.current) return;

    const percent = position * 100;
    progressRef.current.style.width = `${percent}%`;
    thumbRef.current.style.left = `${percent}%`;

    // Update time display immediately for visual feedback
    const newTime = position * duration;
    setCurrentTime(newTime);

    // Update audio time for real-time feedback during dragging
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
    }
  };

  // Handle seekbar mouse/touch down
  const handleSeekStart = (e) => {
    e.preventDefault();

    setIsDragging(true);
    setWasPlaying(isPlaying);

    // Don't pause during seeking for smoother experience

    // Get position and update immediately
    const position = getPositionFromEvent(e);
    updateSeekPosition(position);

    // Add event listeners for drag and release
    document.addEventListener('mousemove', handleSeekMove);
    document.addEventListener('touchmove', handleSeekMove, { passive: false });
    document.addEventListener('mouseup', handleSeekEnd);
    document.addEventListener('touchend', handleSeekEnd);
  };

  // Handle mouse/touch move during drag
  const handleSeekMove = (e) => {
    if (!isDragging) return;

    // Prevent default to avoid scrolling on mobile
    if (e.cancelable) e.preventDefault();

    const position = getPositionFromEvent(e);
    updateSeekPosition(position);
  };

  // Handle mouse/touch up after drag
  const handleSeekEnd = () => {
    if (!isDragging) return;

    // Clean up event listeners
    document.removeEventListener('mousemove', handleSeekMove);
    document.removeEventListener('touchmove', handleSeekMove);
    document.removeEventListener('mouseup', handleSeekEnd);
    document.removeEventListener('touchend', handleSeekEnd);

    // Resume playback if it was playing before
    if (wasPlaying) {
      audioRef.current.play()
        .then(() => {
          startProgressTimer();
        })
        .catch(error => {
          console.error('Play failed after seeking:', error);
        });
    }

    setIsDragging(false);
  };

  // Format time in MM:SS
  const formatTime = (seconds) => {
    if (isNaN(seconds)) return '0:00';

    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${minutes}:${secs}`;
  };

  if (!song) return null;

  return (
    <div
      className={`modern-audio-player ${darkMode ? 'dark-theme' : 'light-theme'}`}
    >
      {/* Close button */}
      {onClose && (
        <button
          className="close-btn"
          onClick={onClose}
          aria-label="Close player"
        >
          <FaTimes size={16} />
        </button>
      )}

      {/* Audio element */}
      <audio
        ref={audioRef}
        src={getAudioUrl()}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => {
          console.log('Song ended, playing next song');
          if (onNext) {
            // Add a small delay before playing the next song
            // This helps ensure state updates are processed correctly
            setTimeout(async () => {
              console.log('Calling onNext after delay');
              try {
                await onNext();
              } catch (error) {
                console.error('Error in onNext callback:', error);
              }
            }, 100);
          } else {
            console.error('onNext callback is not defined');
          }
        }}
        onPlay={() => {
          console.log('Audio element started playing');
          if (!isPlaying) resumeSong();
          startProgressTimer();
        }}
        onPause={() => {
          console.log('Audio element paused');
          if (isPlaying) pauseSong();
          if (updateTimer) {
            clearInterval(updateTimer);
            setUpdateTimer(null);
          }
        }}
        onSeeking={() => {
          // Update display while seeking
          if (audioRef.current) {
            setCurrentTime(audioRef.current.currentTime);
          }
        }}
        onSeeked={() => {
          // Update display after seeking completes
          if (audioRef.current) {
            setCurrentTime(audioRef.current.currentTime);
            updateProgress();
          }
        }}
        preload="auto"
      />

      {/* Song info */}
      <div className="song-info">
        <div className="song-image">
          <img
            src={song.image && song.image.length > 0 ?
              (song.image.find(img => img.quality === '500x500')?.url || song.image[0].url)
              : 'https://via.placeholder.com/50'
            }
            alt={song.name}
            className="rounded"
          />
        </div>
        <div className="song-details">
          <div className="song-title">{song.name}</div>
          <div className="song-artist">
            {song.artists && song.artists.primary && song.artists.primary.map(artist => artist.name).join(', ')}
          </div>
        </div>
      </div>

      {/* Seekbar */}
      <div
        className={`seekbar-container ${isDragging ? 'active' : ''}`}
        ref={seekbarRef}
        onMouseDown={handleSeekStart}
        onTouchStart={handleSeekStart}
      >
        <div className="seekbar-bg">
          <div
            className="seekbar-progress"
            ref={progressRef}
            style={{
              width: `${(currentTime / duration) * 100}%`
            }}
          ></div>
        </div>
        <div
          className="seekbar-thumb"
          ref={thumbRef}
          style={{
            left: `${(currentTime / duration) * 100}%`
          }}
        ></div>
      </div>

      {/* Time display */}
      <div className="time-display">
        <div className="current-time">{formatTime(currentTime)}</div>
        <div className="duration">{formatTime(duration)}</div>
      </div>

      {/* Controls */}
      <div className="player-controls">
        <button
          className="control-btn prev-btn"
          onClick={onPrevious}
          disabled={!onPrevious}
          aria-label="Previous song"
        >
          <FaStepBackward size={16} />
        </button>

        <button
          className="play-pause-btn"
          onClick={togglePlay}
          aria-label={isPlaying ? "Pause" : "Play"}
        >
          {isPlaying ? <FaPause size={18} /> : <FaPlay size={18} style={{ marginLeft: '2px' }} />}
        </button>

        <button
          className="control-btn next-btn"
          onClick={onNext}
          disabled={!onNext}
          aria-label="Next song"
        >
          <FaStepForward size={16} />
        </button>

        <div className="volume-container">
          <button
            className="control-btn volume-btn"
            onClick={toggleMute}
            aria-label={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? <FaVolumeMute size={16} /> : <FaVolumeUp size={16} />}
          </button>

          <input
            type="range"
            className="volume-slider"
            min="0"
            max="1"
            step="0.01"
            value={volume}
            onChange={(e) => setVolume(parseFloat(e.target.value))}
          />
        </div>
      </div>
    </div>
  );
};

export default NewAudioPlayer;
