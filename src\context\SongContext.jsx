import { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { searchSongs, getSongById } from '../services/api';
import logger from '../utils/logger';

const SongContext = createContext();

export const SongProvider = ({ children }) => {
  // Initialize state from localStorage if available
  const [currentSong, setCurrentSong] = useState(() => {
    const savedSong = localStorage.getItem('currentSong');
    return savedSong ? JSON.parse(savedSong) : null;
  });
  const [isPlaying, setIsPlaying] = useState(false); // Always start paused
  const [showModal, setShowModal] = useState(false);
  const [previousSongs, setPreviousSongs] = useState([]);
  const [playerVisible, setPlayerVisible] = useState(false); // Start with player hidden
  const [relatedSongs, setRelatedSongs] = useState([]); // Store related songs for auto-play
  const [isLoadingNext, setIsLoadingNext] = useState(false); // Track if we're loading the next song
  const [currentPlaylist, setCurrentPlaylist] = useState(null); // Current playlist being played
  const [currentPlaylistIndex, setCurrentPlaylistIndex] = useState(-1); // Current index in the playlist
  const location = useLocation();
  const navigate = useNavigate();

  // Check if we're on a song details page
  const isOnSongPage = location.pathname.includes('/song/');

  // Hide modal when on song details page
  useEffect(() => {
    if (isOnSongPage) {
      setShowModal(false);
    }
    // Removed the popup functionality when navigating back
  }, [location, isOnSongPage]);

  // Function to fetch related songs based on artist and album
  const fetchRelatedSongs = useCallback(async (song) => {
    if (!song) return;

    try {
      setIsLoadingNext(true);
      logger.info('Fetching related songs for:', song.name);

      // Extract artist and album info
      const artistName = song.artists?.primary?.[0]?.name;
      const albumName = song.album?.name;

      let relatedSongsArray = [];

      // First try to find songs by the same artist
      if (artistName) {
        logger.info('Searching for songs by artist:', artistName);
        const artistResponse = await searchSongs(artistName, 0, 10);

        if (artistResponse.success && artistResponse.data && artistResponse.data.results) {
          // Filter out the current song
          const artistSongs = artistResponse.data.results
            .filter(s => s.id !== song.id)
            .slice(0, 5);

          relatedSongsArray.push(...artistSongs);
          logger.info(`Found ${artistSongs.length} songs by the same artist`);
        }
      }

      // If we need more songs, try to find songs from the same album
      if (albumName && relatedSongsArray.length < 5) {
        logger.info('Searching for songs from album:', albumName);
        const albumResponse = await searchSongs(albumName, 0, 10);

        if (albumResponse.success && albumResponse.data && albumResponse.data.results) {
          // Filter out the current song and any songs already in the array
          const existingIds = new Set(relatedSongsArray.map(s => s.id));
          const albumSongs = albumResponse.data.results
            .filter(s => s.id !== song.id && !existingIds.has(s.id))
            .slice(0, 5 - relatedSongsArray.length);

          relatedSongsArray.push(...albumSongs);
          logger.info(`Found ${albumSongs.length} songs from the same album`);
        }
      }

      // Shuffle the array to make playback more interesting
      relatedSongsArray = shuffleArray(relatedSongsArray);

      setRelatedSongs(relatedSongsArray);
      logger.info('Related songs set:', relatedSongsArray.length);
      setIsLoadingNext(false);

      return relatedSongsArray;
    } catch (error) {
      logger.error('Error fetching related songs:', error);
      setIsLoadingNext(false);
      return [];
    }
  }, []);

  // Helper function to shuffle an array
  const shuffleArray = (array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  // Effect to handle currentSong changes
  useEffect(() => {
    if (currentSong) {
      logger.info('Current song updated:', currentSong.name);

      // Fetch related songs when current song changes
      fetchRelatedSongs(currentSong);
    } else {
      logger.info('Current song cleared');
      setRelatedSongs([]);
    }
  }, [currentSong, fetchRelatedSongs]);

  // Effect to ensure player is hidden on initial load
  useEffect(() => {
    // Hide player on initial load
    setPlayerVisible(false);
    logger.info('Player hidden on initial load');
  }, []);

  // Play a song
  const playSong = (song, shouldNavigate = false) => {
    if (song) {
      logger.info('Playing song:', song.name);
      console.log('playSong called with song:', song.name, 'ID:', song.id);
      console.log('Audio URL available:', song.downloadUrl && song.downloadUrl.length > 0);

      // If it's a different song, add current song to history
      if (currentSong && currentSong.id !== song.id) {
        setPreviousSongs(prev => {
          // Add current song to history if not already there
          if (!prev.some(s => s.id === currentSong.id)) {
            return [currentSong, ...prev].slice(0, 10); // Keep last 10 songs
          }
          return prev;
        });

        // Navigate to the song page if shouldNavigate is true or we're auto-playing
        if (shouldNavigate || !isOnSongPage) {
          logger.info('Navigating to song page:', song.id);
          navigate(`/song/${song.id}`);
        }
      }

      // Save to state and localStorage
      setCurrentSong(song);
      localStorage.setItem('currentSong', JSON.stringify(song));
      setIsPlaying(true);

      // Make sure the player is visible
      console.log('Setting player visible');
      setPlayerVisible(true);

      // Force a small delay to ensure state updates are processed
      setTimeout(() => {
        if (!playerVisible) {
          console.log('Forcing player visibility after delay');
          setPlayerVisible(true);
        }
      }, 100);
    } else {
      console.error('Attempted to play a null or undefined song');
    }
  };

  // Pause the current song
  const pauseSong = () => {
    setIsPlaying(false);
  };

  // Resume the current song
  const resumeSong = () => {
    if (currentSong) {
      setIsPlaying(true);
    }
  };

  // Close the song modal
  const closeModal = () => {
    setShowModal(false);
  };

  // Stop the current song and hide the player
  const stopSong = () => {
    logger.info('Stopping song and clearing player state');
    // First pause playback
    setIsPlaying(false);
    // Then clear the current song
    setCurrentSong(null);
    // Remove from localStorage
    localStorage.removeItem('currentSong');
    logger.info('Song stopped and removed from localStorage');
  };

  // Hide the player and pause the song
  const hidePlayer = () => {
    logger.info('Hiding player and pausing song');
    // Pause the song if it's playing
    setIsPlaying(false);
    // Hide the player
    setPlayerVisible(false);
  };

  // Show the player
  const showPlayer = () => {
    logger.info('Showing player');
    // Show the player
    setPlayerVisible(true);
  };

  // Get the next song from playlist or related songs
  const getNextSong = async () => {
    console.log('getNextSong called, currentPlaylist:', currentPlaylist?.id, 'currentIndex:', currentPlaylistIndex);

    // First check if we're playing a playlist
    if (currentPlaylist && currentPlaylist.songs && currentPlaylist.songs.length > 0) {
      // Calculate the next index
      const nextIndex = currentPlaylistIndex + 1;
      console.log(`Checking for next song in playlist at index ${nextIndex}/${currentPlaylist.songs.length}`);

      // Check if we have more songs in the playlist
      if (nextIndex < currentPlaylist.songs.length) {
        // Get the next song from the playlist
        let nextSong = currentPlaylist.songs[nextIndex];
        console.log('Found next song in playlist:', nextSong?.name);

        if (!nextSong) {
          console.error('Next song is undefined or null');
          return null;
        }

        // Update the current playlist index
        setCurrentPlaylistIndex(nextIndex);

        logger.info(`Playing next song in playlist (${nextIndex + 1}/${currentPlaylist.songs.length}):`, nextSong.name);

        // Check if the song has downloadUrl, if not, fetch complete details
        if (!nextSong.downloadUrl || nextSong.downloadUrl.length === 0) {
          console.log('Next song missing downloadUrl, fetching complete details');

          // Show loading state
          setIsLoadingNext(true);

          // Fetch complete song details from API
          const completeSongDetails = await fetchCompleteSongDetails(nextSong.id);

          // Hide loading state
          setIsLoadingNext(false);

          if (completeSongDetails) {
            // Update the song in the playlist with complete details
            nextSong = completeSongDetails;

            // Also update the song in the playlist array
            const updatedSongs = [...currentPlaylist.songs];
            updatedSongs[nextIndex] = completeSongDetails;

            // Update the playlist with the updated songs
            const updatedPlaylist = {
              ...currentPlaylist,
              songs: updatedSongs
            };
            setCurrentPlaylist(updatedPlaylist);

            console.log('Updated next song with complete details:', completeSongDetails.name);
          } else {
            console.error('Failed to fetch complete song details for next song, using original song');
          }
        }

        // Make sure the player is visible
        setPlayerVisible(true);

        // Force a small delay to ensure state updates are processed
        setTimeout(() => {
          if (!playerVisible) {
            console.log('Forcing player visibility after delay in getNextSong');
            setPlayerVisible(true);
          }
        }, 100);

        // Navigate to the song page for the next song
        if (nextSong.id) {
          logger.info('Navigating to next song page:', nextSong.id);
          // Force navigation to the song page
          setTimeout(() => {
            navigate(`/song/${nextSong.id}`);
          }, 100);
        }

        return nextSong;
      } else {
        // We've reached the end of the playlist
        logger.info('Reached the end of the playlist, playing a random song');
        console.log('End of playlist reached');

        // Clear the current playlist since we've finished it
        setCurrentPlaylist(null);
        setCurrentPlaylistIndex(-1);

        // Fall back to related songs
        if (relatedSongs.length > 0) {
          // Get a random song from related songs for variety
          const randomIndex = Math.floor(Math.random() * relatedSongs.length);
          const nextSong = relatedSongs[randomIndex];
          console.log('Selected random related song:', nextSong?.name);

          if (!nextSong) {
            console.error('Random related song is undefined or null');
            return null;
          }

          // Remove it from the array
          setRelatedSongs(prev => prev.filter((_, i) => i !== randomIndex));

          // Make sure the player is visible
          setPlayerVisible(true);

          // Force a small delay to ensure state updates are processed
          setTimeout(() => {
            if (!playerVisible) {
              console.log('Forcing player visibility after delay for random song');
              setPlayerVisible(true);
            }
          }, 100);

          logger.info('Auto-playing random related song:', nextSong.name);

          // Navigate to the song page for the next song
          if (nextSong.id) {
            console.log('Navigating to next song page:', nextSong.id);
            // Force navigation to the song page
            setTimeout(() => {
              navigate(`/song/${nextSong.id}`);
            }, 100);
          }

          return nextSong;
        } else {
          console.log('No related songs available after playlist ended');
        }
      }
    } else if (relatedSongs.length > 0) {
      // If no playlist is active, use related songs as before
      // Get the first song from related songs
      const nextSong = relatedSongs[0];
      console.log('No playlist active, using related song:', nextSong?.name);

      if (!nextSong) {
        console.error('Related song is undefined or null');
        return null;
      }

      // Remove it from the array
      setRelatedSongs(prev => prev.slice(1));

      // Make sure the player is visible
      setPlayerVisible(true);

      // Force a small delay to ensure state updates are processed
      setTimeout(() => {
        if (!playerVisible) {
          console.log('Forcing player visibility after delay for related song');
          setPlayerVisible(true);
        }
      }, 100);

      console.log('Auto-playing next song from related songs:', nextSong.name);

      // Navigate to the song page for the next song
      if (nextSong.id) {
        console.log('Navigating to next song page:', nextSong.id);
        // Force navigation to the song page
        setTimeout(() => {
          navigate(`/song/${nextSong.id}`);
        }, 100);
      }

      return nextSong;
    } else {
      console.log('No playlist or related songs available');
    }

    console.log('No next song available for auto-play');
    return null;
  };

  // Get the previous song from history
  const getPreviousSong = () => {
    if (previousSongs.length > 0) {
      const prevSong = previousSongs[0];
      setPreviousSongs(prev => prev.slice(1));
      return prevSong;
    }
    return null;
  };

  // Helper function to fetch complete song details from API
  const fetchCompleteSongDetails = async (songId) => {
    try {
      console.log('Fetching complete song details for ID:', songId);
      const response = await getSongById(songId);

      if (response.success && response.data && response.data.length > 0) {
        const completeSong = response.data[0];
        console.log('Successfully fetched complete song details:', completeSong.name);
        return completeSong;
      } else {
        console.error('Failed to fetch song details, API returned no data');
        return null;
      }
    } catch (error) {
      console.error('Error fetching song details:', error);
      return null;
    }
  };

  // Play a playlist from the beginning or a specific index
  const playPlaylist = async (playlist, startIndex = 0) => {
    console.log('playPlaylist called with:', playlist?.id, 'startIndex:', startIndex);

    if (!playlist || !playlist.songs || playlist.songs.length === 0) {
      console.log('Cannot play empty playlist');
      return;
    }

    console.log(`Playing playlist: ${playlist.name} (${playlist.songs.length} songs) from index ${startIndex}`);

    // Ensure the playlist has a valid ID
    const validPlaylist = {
      ...playlist,
      id: playlist.id || `playlist-${Date.now()}`,
      name: playlist.name || 'Unnamed Playlist'
    };

    console.log('Using playlist with ID:', validPlaylist.id);

    // Set the current playlist
    setCurrentPlaylist(validPlaylist);

    // Set the current index
    setCurrentPlaylistIndex(startIndex);

    // Make sure the player is visible
    setPlayerVisible(true);

    // Get the first song to play
    let firstSong = playlist.songs[startIndex];
    console.log('Starting with song:', firstSong?.name, 'ID:', firstSong?.id);

    if (firstSong) {
      // Check if the song has downloadUrl, if not, fetch complete details
      if (!firstSong.downloadUrl || firstSong.downloadUrl.length === 0) {
        console.log('Song missing downloadUrl, fetching complete details');

        // Show loading state
        setIsLoadingNext(true);

        // Fetch complete song details from API
        const completeSongDetails = await fetchCompleteSongDetails(firstSong.id);

        // Hide loading state
        setIsLoadingNext(false);

        if (completeSongDetails) {
          // Update the song in the playlist with complete details
          firstSong = completeSongDetails;

          // Also update the song in the playlist array
          const updatedSongs = [...validPlaylist.songs];
          updatedSongs[startIndex] = completeSongDetails;

          // Update the playlist with the updated songs
          validPlaylist.songs = updatedSongs;
          setCurrentPlaylist(validPlaylist);

          console.log('Updated song with complete details:', completeSongDetails.name);
        } else {
          console.error('Failed to fetch complete song details, using original song');
        }
      }

      // Make sure we don't navigate away when playing the first song in a playlist
      playSong(firstSong, false);
    } else {
      console.error('First song is undefined or null');
    }
  };

  return (
    <SongContext.Provider
      value={{
        currentSong,
        isPlaying,
        showModal,
        previousSongs,
        playerVisible,
        relatedSongs,
        isLoadingNext,
        currentPlaylist,
        currentPlaylistIndex,
        playSong,
        pauseSong,
        resumeSong,
        closeModal,
        stopSong,
        hidePlayer,
        showPlayer,
        getNextSong,
        getPreviousSong,
        fetchRelatedSongs,
        playPlaylist,
      }}
    >
      {children}
    </SongContext.Provider>
  );
};

export const useSong = () => {
  const context = useContext(SongContext);
  if (context === undefined) {
    throw new Error('useSong must be used within a SongProvider');
  }
  return context;
};
