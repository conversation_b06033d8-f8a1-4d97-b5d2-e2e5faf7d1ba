import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import Navbar from './Navbar';
import Footer from './Footer';
import MobileNavBar from './MobileNavBar';
import MobileFooter from './MobileFooter';
import LoadingSpinner from '../common/LoadingSpinner';
import { useTheme } from '../../context/ThemeContext';
import { useSong } from '../../context/SongContext';

const Layout = () => {
  const { darkMode } = useTheme();
  const { playerVisible } = useSong();

  return (
    <div style={{
      backgroundColor: 'var(--dark-bg)',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <Navbar />
      <main className={`container py-4 flex-grow-1 ${playerVisible ? 'mb-5' : ''}`}>
        <Suspense fallback={<LoadingSpinner />}>
          <Outlet />
        </Suspense>
      </main>
      <Footer />
      {/* Mobile footer with social links */}
      <MobileFooter />
      {/* Add mobile navigation bar - hide when player is visible on mobile */}
      <div className={`${playerVisible ? 'd-none d-md-block' : 'd-block'}`}>
        <MobileNavBar />
      </div>
      {/* Add padding at the bottom when player is visible */}
      {playerVisible && <div className="d-block d-lg-none" style={{ height: '70px' }}></div>}
    </div>
  );
};

export default Layout;
